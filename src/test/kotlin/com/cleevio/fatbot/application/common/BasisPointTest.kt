package com.cleevio.fatbot.application.common

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.platform.commons.annotation.Testable
import java.math.BigDecimal

@Testable
class BasisPointTest {

	@Test
	fun `should convert percentage value`() {
		BasisPoint.ofPercentage(0.0).value shouldBe 0 // Zero percentage
		BasisPoint.ofPercentage(1.000).value shouldBe 100 // Exact integer percentage (1%)
		BasisPoint.ofPercentage(100.0).value shouldBe 10000 // Large whole number percentage (100%)
		BasisPoint.ofPercentage(0.01).value shouldBe 1 // Minimal non-zero percentage (0.01%)
		BasisPoint.ofPercentage(0.005).value shouldBe 0 // Rounding down from 0.5 basis points
		BasisPoint.ofPercentage(99.999).value shouldBe 9999 // Lossy percentage rounds to nearest integer
		BasisPoint.ofPercentage(25.555555).value shouldBe 2555 // Precision beyond two decimal places
		BasisPoint.ofPercentage(0.0001).value shouldBe 0 // Extremely small percentage
		BasisPoint.ofPercentage(66.6666).value shouldBe 6666 // Fractional percentage with repeating decimals

		// Illegal negative
		shouldThrow<IllegalArgumentException> { BasisPoint.ofPercentage(-1.000) }
		// Over max
		shouldThrow<IllegalArgumentException> { BasisPoint.ofPercentage(100.01) }
		// Over max
		shouldThrow<IllegalArgumentException> { BasisPoint.ofPercentage(123341.12) }
	}

	@Test
	fun `should convert to fraction`() {
		// Zero and very small fractions
		BasisPoint.ofFraction(BigDecimal.ZERO).value shouldBe 0
		BasisPoint.ofFraction("0.0001".toBigDecimal()).value shouldBe 1 // 0.01% = 1 bps

		BasisPoint.ofFraction("1".toBigDecimal()).value shouldBe 10000 // 100% = 10000 bps
		BasisPoint.ofFraction("0.01".toBigDecimal()).value shouldBe 100 // 1% = 100 bps

		BasisPoint.ofFraction("0.001".toBigDecimal()).value shouldBe 10 // 0.1% = 10 bps
		BasisPoint.ofFraction("0.0001".toBigDecimal()).value shouldBe 1 // 0.01% = 1 bps
		BasisPoint.ofFraction("0.00001".toBigDecimal()).value shouldBe 0 // 0.001% = 0 bps
		BasisPoint.ofFraction("0.025".toBigDecimal()).value shouldBe 250 // 2.5% = 250 bps
		BasisPoint.ofFraction("0.05".toBigDecimal()).value shouldBe 500 // 5% = 500 bps

		shouldThrow<IllegalArgumentException> { BasisPoint.ofFraction("-0.01".toBigDecimal()) }
		shouldThrow<IllegalArgumentException> { BasisPoint.ofFraction("-0.00001".toBigDecimal()) }
		shouldThrow<IllegalArgumentException> { BasisPoint.ofFraction("-123451".toBigDecimal()) }

		shouldThrow<IllegalArgumentException> { BasisPoint.ofFraction("1.5".toBigDecimal()) }
		shouldThrow<IllegalArgumentException> { BasisPoint.ofFraction("2".toBigDecimal()) }

		BasisPoint.ofFraction("0.031159".toBigDecimal()).value shouldBe 311 // 3.1159% = 311.59 bps → 311 bps
		BasisPoint.ofFraction("0.99999".toBigDecimal()).value shouldBe 9999 // 99.999% = 9999.9 bps → 9999 bps
		BasisPoint.ofFraction("0.123456".toBigDecimal()).value shouldBe 1234 // 12.3456% = 1234.56 bps → 1234 bps
	}

	@Test
	fun `should convert from fraction`() {
		BasisPoint.of(0).fraction shouldBe "0".toBigDecimal() // 0 bps = 0%
		BasisPoint.of(10_000).fraction shouldBe "1".toBigDecimal() // 10000 bps = 100%
		BasisPoint.of(100).fraction shouldBe "0.01".toBigDecimal() // 100 bps = 1%

		BasisPoint.of(250).fraction shouldBe "0.025".toBigDecimal() // 250 bps = 2.5%
		BasisPoint.of(500).fraction shouldBe "0.05".toBigDecimal() // 500 bps = 5%
		BasisPoint.of(125).fraction shouldBe "0.0125".toBigDecimal() // 125 bps = 1.25%

		BasisPoint.of(3333).fraction shouldBe "0.3333".toBigDecimal() // 3333 bps = 33.33%
		BasisPoint.of(1235).fraction shouldBe "0.1235".toBigDecimal() // 1235 bps = 12.35%

		BasisPoint.of(1).fraction shouldBe "0.0001".toBigDecimal() // 1 bps = 0.01%
		BasisPoint.of(10).fraction shouldBe "0.001".toBigDecimal() // 10 bps = 0.1%
	}

	@Test
	fun `should correctly apply on value`() {
		// BigInteger version
		BasisPoint.of(0).applyOn("1000".toBigInteger()) shouldBe "0".toBigInteger() // 0 bps → 0% of 1000
		BasisPoint.of(100).applyOn("1000".toBigInteger()) shouldBe "10".toBigInteger() // 1% of 1000
		BasisPoint.of(10000).applyOn("1000".toBigInteger()) shouldBe "1000".toBigInteger() // 100% of 1000
		BasisPoint.of(5000).applyOn("2000".toBigInteger()) shouldBe "1000".toBigInteger() // 50% of 2000
		BasisPoint.of(250).applyOn("8000".toBigInteger()) shouldBe "200".toBigInteger() // 2.5% of 8000
		BasisPoint.of(3333).applyOn("300".toBigInteger()) shouldBe "99".toBigInteger() // 33.33% of 300 → 99.99 truncated
		// 12.34% of 123456 → 15234.47 truncated
		BasisPoint.of(1234).applyOn("123456".toBigInteger()) shouldBe "15234".toBigInteger()

		// Base amount version
		BasisPoint.of(100).applyOn("1000".toBigInteger().asBaseAmount()) shouldBe "10".toBigInteger().asBaseAmount()
		BasisPoint.of(150).applyOn("2000".toBigInteger().asBaseAmount()) shouldBe "30".toBigInteger().asBaseAmount()
		// 12.5% of 1,000,000
		BasisPoint.of(1250).applyOn("1000000".toBigInteger().asBaseAmount()) shouldBe "125000".toBigInteger().asBaseAmount()

		// Int version
		BasisPoint.of(0).applyOn(1000) shouldBe 0
		BasisPoint.of(100).applyOn(1000) shouldBe 10 // 1% of 1000
		BasisPoint.of(2500).applyOn(200) shouldBe 50 // 25% of 200
		BasisPoint.of(3333).applyOn(300) shouldBe 99 // 33% of 300

		// Int overflow
		BasisPoint.of(10000).applyOn(Int.MAX_VALUE) shouldBe Int.MAX_VALUE
		BasisPoint.of(10000).applyOn(Int.MIN_VALUE) shouldBe Int.MIN_VALUE
	}
}
