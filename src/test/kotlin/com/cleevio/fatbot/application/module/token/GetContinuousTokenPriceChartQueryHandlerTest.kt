package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.bitquery.toTimeIntervalItem
import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.token.constant.TimeInterval
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.token.query.GetContinuousTokenPriceChartQuery
import com.cleevio.fatbot.application.module.tokenaudit.port.out.PerformTokenAudit
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.graphql.bitquery.v2operation.EVMTokenPriceQuery
import io.kotest.matchers.ints.shouldBeGreaterThan
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.stream.Stream
import kotlin.random.Random

class GetContinuousTokenPriceChartQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val integrationTestClock: IntegrationTestClock,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		every {
			etherscanConnector.getSourceCode(any(), any())
		} returns EtherscanConnector.GetSourceCodeResponse(
			listOf(EtherscanConnector.GetSourceCodeResultResponse(
				sourceCode = "sourceCode",
				proxy = "0",
				abi = "[ABI]",
				implementation = "",
			))
		)

		every {
			openAIConnector.responses(any(), PerformTokenAudit.Result::class.java)
		} returns PerformTokenAudit.Result(
			issues = emptyList(),
			riskFactor = PerformTokenAudit.RiskFactor.GREEN,
			riskFactorReason = "No problems found",
		)
		integrationTestHelper.getTokenPairInfo(
			chain = Chain.EVM_MAINNET,
			dexType = GetDex.Dex.UNISWAP_V2,
			uniswapV3Fee = null,
			tokenAddress = AddressWrapper("******************************************"),
			pairAddress = AddressWrapper("******************************************"),
			tokenDecimals = 18.toBigInteger(),
		)

		every {
			getTokenDetailDexScreener(
				ChainAddress(Chain.EVM_MAINNET, AddressWrapper("******************************************")),
			)
		} returns mockk {
			every { dexInfo } returns mockk {
				every { priceNative } returns BigDecimal("0.000000000015")
				every { priceUsd } returns BigDecimal("0.00000003")
			}
		}
		every {
			performTokenAuditOpenAI.invoke(any(), any())
		} just Runs

		// Workaround for issue https://github.com/Ninja-Squad/springmockk/issues/85
		clearAllMocks(answers = false, recordedCalls = true)
	}

	@ParameterizedTest
	@MethodSource("timeRangeAndExpectedIntervalAttributesProvider")
	fun `should get token price chart data`(
		timeRange: TimeRange,
		expectedTimeInterval: TimeInterval,
		expectedIntervalCount: Int,
		expectedLimit: Int,
		expectedRealSize: Int,
		withNowItem: Boolean,
		queryMockResults: List<EVMTokenPriceQuery.DEXTradeByToken>,
		queryBeforeIntervalResult: EVMTokenPriceQuery.DEXTradeByToken?,
	) {
		every {
			bitqueryApiV2Connector.getEvmTokenPrice(
				chainId = any(),
				tokenAddress = any(),
				pairAddress = any(),
				timeInterval = any(),
				intervalCount = any(),
				limit = any(),
				after = any(),
				before = null,
			)
		} returns queryMockResults.map { it.toTimeIntervalItem() }

		every {
			bitqueryApiV2Connector.getEvmTokenPrice(
				chainId = any(),
				tokenAddress = any(),
				pairAddress = any(),
				timeInterval = any(),
				intervalCount = any(),
				limit = 1,
				after = null,
				before = any(),
			)
		} returns listOfNotNull(queryBeforeIntervalResult).map { it.toTimeIntervalItem() }

		val query = if (withNowItem) {
			GetContinuousTokenPriceChartQuery.withNowItem(
				chain = Chain.EVM_MAINNET,
				pairAddress = AddressWrapper("******************************************"),
				timeRange = timeRange,
			)
		} else {
			GetContinuousTokenPriceChartQuery.withoutNowItem(
				chain = Chain.EVM_MAINNET,
				pairAddress = AddressWrapper("******************************************"),
				timeRange = timeRange,
			)
		}

		val result = queryBus(query)

		result.data.size shouldBe expectedRealSize

		if (withNowItem) {
			with(result.data.last()) {
				timestamp shouldBe Instant.parse("2024-08-30T10:00:00Z")
				close shouldBe BigDecimal("0.00000003")
				closeNative shouldBe BigDecimal("0.000000000015")
				volume shouldBe (result.data.takeLast(2).firstOrNull()?.volume ?: BigDecimal.ZERO)
			}
		}

		verify {
			bitqueryApiV2Connector.getEvmTokenPrice(
				chainId = 1,
				tokenAddress = any(),
				pairAddress = "******************************************",
				timeInterval = expectedTimeInterval,
				intervalCount = expectedIntervalCount,
				after = any(),
				before = null,
				limit = expectedLimit,
			)
		}

		if (withNowItem) {
			verify(exactly = 1) {
				getTokenDetailDexScreener(
					ChainAddress(Chain.EVM_MAINNET, AddressWrapper("******************************************")),
				)
			}
		}
	}

	@ParameterizedTest
	@MethodSource("timeRangeAndExpectedCachingAttributesProvider")
	fun `should get token price chart data with or without caching`(
		timeRange: TimeRange,
		advanceBySeconds: Int,
		shouldUseCachedValue: Boolean,
	) {
		every {
			bitqueryApiV2Connector.getEvmTokenPrice(
				chainId = any(),
				tokenAddress = any(),
				pairAddress = any(),
				timeInterval = any(),
				intervalCount = any(),
				limit = any(),
				after = any(),
				before = null,
			)
		} returns emptyList()

		every {
			bitqueryApiV2Connector.getEvmTokenPrice(
				chainId = any(),
				tokenAddress = any(),
				pairAddress = any(),
				timeInterval = any(),
				intervalCount = any(),
				limit = 1,
				after = null,
				before = any(),
			)
		} returns randomTokenPriceQueryResults("2000-08-30T09:30:00Z").map { it.toTimeIntervalItem() }

		// first call - should store response in cache
		val result = queryBus(
			GetContinuousTokenPriceChartQuery.withNowItem(
				chain = Chain.EVM_MAINNET,
				pairAddress = AddressWrapper("******************************************"),
				timeRange = timeRange,
			),
		)

		result.data.size shouldBeGreaterThan 0

		integrationTestClock.advanceBy(advanceBySeconds.toLong(), ChronoUnit.SECONDS)

		// second call - should or should not use value from cache
		queryBus(
			GetContinuousTokenPriceChartQuery.withNowItem(
				chain = Chain.EVM_MAINNET,
				pairAddress = AddressWrapper("******************************************"),
				timeRange = timeRange,
			),
		)

		// Note: One call consists of: 1. The main getEvmTokenPrice, 2. The pre-interval token price to infer the window
		if (shouldUseCachedValue) {
			verifyOrder {
				// connector should be called only once
				bitqueryApiV2Connector.getEvmTokenPrice(
					any(),
					any(),
					any(),
					any(),
					any(),
					any(),
					after = any(),
					before = null,
				)
				bitqueryApiV2Connector.getEvmTokenPrice(
					any(),
					any(),
					any(),
					any(),
					any(),
					limit = 1,
					after = null,
					before = any(),
				)
			}
		} else {
			verifyOrder {
				bitqueryApiV2Connector.getEvmTokenPrice(
					any(),
					any(),
					any(),
					any(),
					any(),
					any(),
					after = any(),
					before = null,
				)
				bitqueryApiV2Connector.getEvmTokenPrice(
					any(),
					any(),
					any(),
					any(),
					any(),
					limit = 1,
					after = null,
					before = any(),
				)
				bitqueryApiV2Connector.getEvmTokenPrice(
					any(),
					any(),
					any(),
					any(),
					any(),
					any(),
					after = any(),
					before = null,
				)
				bitqueryApiV2Connector.getEvmTokenPrice(
					any(),
					any(),
					any(),
					any(),
					any(),
					limit = 1,
					after = null,
					before = any(),
				)
			}
		}
	}

	companion object {

		private fun makeArgument(
			timeRange: TimeRange,
			timeInterval: TimeInterval,
			intervalCount: Int,
			expectedLimit: Int,
			expectedRealSize: Int,
			withNowItem: Boolean,
			queryMockResults: List<EVMTokenPriceQuery.DEXTradeByToken>,
			queryBeforeIntervalResult: EVMTokenPriceQuery.DEXTradeByToken? = null,
		): Arguments = Arguments.of(
			timeRange,
			timeInterval,
			intervalCount,
			expectedLimit,
			expectedRealSize,
			withNowItem,
			queryMockResults,
			queryBeforeIntervalResult,
		)

		@JvmStatic
		fun timeRangeAndExpectedIntervalAttributesProvider(): Stream<Arguments> {
			// Note: The test clock starts at 2024-08-30T10:00:00Z
			return Stream.of(
				// Extra result before the interval -> infers the entire interval
				makeArgument(
					timeRange = TimeRange.HOUR,
					timeInterval = TimeInterval.MINUTE,
					intervalCount = 1,
					expectedLimit = 60,
					expectedRealSize = 46,
					withNowItem = true,
					queryMockResults = randomTokenPriceQueryResults(
						"2024-08-30T09:00:00Z", // <- Result before the interval (comparison is not inclusive), will be ignored
						"2024-08-30T09:15:00Z",
					),
				),
				// First result in the half of the window + no price before -> only half of the interval returned
				makeArgument(
					timeRange = TimeRange.HOUR,
					timeInterval = TimeInterval.MINUTE,
					intervalCount = 1,
					expectedLimit = 60,
					expectedRealSize = 30,
					withNowItem = false,
					queryMockResults = randomTokenPriceQueryResults(
						"2024-08-30T09:30:00Z",
						"2024-08-30T09:48:00Z",
					),
				),
				// The window moves by the timeInterval * intervalCount
				// Here a result from one-minute late moves the window by 1 (compared to a previous test case)
				makeArgument(
					timeRange = TimeRange.HOUR,
					timeInterval = TimeInterval.MINUTE,
					intervalCount = 1,
					expectedLimit = 60,
					// One less than previous (!)
					expectedRealSize = 29,
					withNowItem = false,
					queryMockResults = randomTokenPriceQueryResults(
						"2024-08-30T09:31:00Z",
						"2024-08-30T09:48:00Z",
					),
				),
				// Only result before the interval -> infers the entire result
				makeArgument(
					timeRange = TimeRange.HOUR,
					timeInterval = TimeInterval.MINUTE,
					intervalCount = 1,
					expectedLimit = 60,
					expectedRealSize = 61,
					withNowItem = true,
					queryMockResults = emptyList(),
					queryBeforeIntervalResult = randomTokenPriceQueryResult("2024-08-30T05:00:00Z"),
				),
				makeArgument(
					timeRange = TimeRange.HOUR,
					timeInterval = TimeInterval.MINUTE,
					intervalCount = 1,
					expectedLimit = 60,
					expectedRealSize = 1,
					withNowItem = true,
					queryMockResults = emptyList(),
				),
				makeArgument(
					timeRange = TimeRange.HOUR,
					timeInterval = TimeInterval.MINUTE,
					intervalCount = 1,
					expectedLimit = 60,
					expectedRealSize = 0,
					withNowItem = false,
					queryMockResults = emptyList(),
				),
				makeArgument(
					timeRange = TimeRange.DAY,
					timeInterval = TimeInterval.MINUTE,
					intervalCount = 5,
					expectedLimit = 288,
					expectedRealSize = 47,
					withNowItem = false,
					queryMockResults = randomTokenPriceQueryResults(
						"2024-08-28T10:00:00Z",
						"2024-08-30T06:05:00Z",
					),
				),
				makeArgument(
					timeRange = TimeRange.WEEK,
					timeInterval = TimeInterval.MINUTE,
					intervalCount = 15,
					expectedLimit = 672,
					expectedRealSize = 672,
					withNowItem = false,
					queryMockResults = randomTokenPriceQueryResults("2024-08-23T09:30:00Z"),
				),
				// First result 15 min (one intervalPeriod) after start -> expectedLimit - 1
				makeArgument(
					timeRange = TimeRange.WEEK,
					timeInterval = TimeInterval.MINUTE,
					intervalCount = 15,
					expectedLimit = 672,
					expectedRealSize = 671,
					withNowItem = false,
					queryMockResults = randomTokenPriceQueryResults("2024-08-23T10:15:00Z"),
				),
				makeArgument(
					timeRange = TimeRange.MONTH,
					timeInterval = TimeInterval.HOUR,
					intervalCount = 1,
					expectedLimit = 744,
					expectedRealSize = 439,
					withNowItem = true,
					queryMockResults = randomTokenPriceQueryResults(
						"2024-07-30T09:00:00Z",
						"2024-08-30T09:00:00Z",
						"2024-08-12T05:00:00Z",
						"2024-08-30T09:00:00Z",
					),
				),
				makeArgument(
					timeRange = TimeRange.YEAR,
					timeInterval = TimeInterval.DAY,
					intervalCount = 1,
					expectedLimit = 365,
					expectedRealSize = 192,
					withNowItem = false,
					queryMockResults = randomTokenPriceQueryResults(
						"2023-01-29T00:00:00Z", // Ignored
						"2024-06-29T00:00:00Z", // Ignored
						"2024-02-21T00:00:00Z", // Start
						"2024-02-22T00:00:00Z",
						"2024-02-23T00:00:00Z",
					),
				),
				makeArgument(
					timeRange = TimeRange.ALL,
					timeInterval = TimeInterval.DAY,
					intervalCount = 1,
					expectedLimit = 730,
					expectedRealSize = 367, // 2024 is a leap year
					withNowItem = false,
					queryMockResults = randomTokenPriceQueryResults("2023-08-30T00:00:00Z"),
				),
				// Results are filled from first entry to current time
				// I.e. queryBeforeIntervalResult is ignored if there is any entry in queryResults
				makeArgument(
					timeRange = TimeRange.ALL,
					timeInterval = TimeInterval.DAY,
					intervalCount = 1,
					expectedLimit = 730,
					expectedRealSize = 367,
					withNowItem = false,
					queryMockResults = randomTokenPriceQueryResults("2023-08-30T00:00:00Z"),
					queryBeforeIntervalResult = randomTokenPriceQueryResult("2021-08-30T00:00:00Z"),
				),
			)
		}

		@JvmStatic
		fun timeRangeAndExpectedCachingAttributesProvider(): Stream<Arguments> = Stream.of(
			// under 1 minute
			Arguments.of(TimeRange.HOUR, 59, true),
			// 1 minute
			Arguments.of(TimeRange.HOUR, 60, false),
			// under 5 minutes
			Arguments.of(TimeRange.DAY, 299, true),
			// 5 minutes
			Arguments.of(TimeRange.DAY, 300, false),
			// under 15 minutes
			Arguments.of(TimeRange.WEEK, 899, true),
			// 15 minutes
			Arguments.of(TimeRange.WEEK, 900, false),
			// under 1 hour
			Arguments.of(TimeRange.MONTH, 3599, true),
			// 1 hour
			Arguments.of(TimeRange.MONTH, 3600, false),
			// clock in tests is set to 2024-08-30T10:00:00Z, 50400 is remaining number of seconds until midnight
			// under 14 hours
			Arguments.of(TimeRange.YEAR, 50399, true),
			// 14 hours
			Arguments.of(TimeRange.YEAR, 50400, false),
			// under 14 hours
			Arguments.of(TimeRange.ALL, 50399, true),
			// 14 hours
			Arguments.of(TimeRange.ALL, 50400, false),
		)
	}
}

private fun randomTokenPriceQueryResults(vararg timestamp: String) = timestamp.map { randomTokenPriceQueryResult(it) }

private fun randomTokenPriceQueryResult(timestamp: String): EVMTokenPriceQuery.DEXTradeByToken {
	val closeEth = Random.nextDouble(0.0, 15.0)

	return EVMTokenPriceQuery.DEXTradeByToken(
		Block = EVMTokenPriceQuery.Block(Time = Instant.parse(timestamp)),
		volume = Random.nextDouble(0.0, 100.0).toString(),
		price = closeEth * 3000,
		priceNative = closeEth,
	)
}
