package com.cleevio.fatbot.application.module.tokenaudit.service

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.tokenaudit.event.TokenAuditRequestedEvent
import com.cleevio.fatbot.application.module.tokenaudit.port.out.PerformTokenAudit
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.tokenaudit.TokenAuditState
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class GetTokenAuditTest(
	@Autowired private val underTest: GetTokenAudit,
	@Autowired private val tokenAuditFinderService: TokenAuditFinderService,
	@Autowired private val clock: IntegrationTestClock,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		every { emitApplicationEvent<TokenAuditRequestedEvent>() } just Runs
	}

	@Test
	fun `should create new token audit record if not found`() {
		// given
		tokenAuditFinderService.findAll().size shouldBe 0

		every {
			etherscanConnector.getSourceCode(any(), any())
		} returns EtherscanConnector.GetSourceCodeResponse(
			listOf(EtherscanConnector.GetSourceCodeResultResponse(
				sourceCode = "sourceCode",
				proxy = "0",
				abi = "[ABI]",
				implementation = "",
			))
		)

		every {
			openAIConnector.responses(any(), PerformTokenAudit.Result::class.java)
		} returns PerformTokenAudit.Result(
			issues = listOf(PerformTokenAudit.AuditIssue("test", "test", PerformTokenAudit.IssueSeverity.MEDIUM)),
			riskFactor = PerformTokenAudit.RiskFactor.RED,
			riskFactorReason = "Test",
		)

		// when
		val result = underTest(
			tokenAddress = ChainAddress(
				Chain.EVM_MAINNET,
				AddressWrapper("******************************************"),
			),
		)

		// then
		tokenAuditFinderService.findAll().size shouldBe 1
		result.state shouldBe TokenAuditState.PROCESSING
		result.result shouldBe null

		verify(exactly = 1) { emitApplicationEvent<TokenAuditRequestedEvent>() }
	}

	@Test
	fun `should mark new processing instant for old existing audit record`() {
		// given
		integrationTestHelper.getTokenAudit(
			tokenAddress = AddressWrapper("******************************************"),
			chain = Chain.EVM_MAINNET,
			entityModifier = { it.markNewProcessing(Instant.now(clock)) },
		)

		every {
			etherscanConnector.getSourceCode(any(), any())
		} returns EtherscanConnector.GetSourceCodeResponse(
			listOf(EtherscanConnector.GetSourceCodeResultResponse(
				sourceCode = "sourceCode",
				proxy = "0",
				abi = "[ABI]",
				implementation = "",
			))
		)

		every {
			openAIConnector.responses(any(), PerformTokenAudit.Result::class.java)
		} returns PerformTokenAudit.Result(
			issues = listOf(PerformTokenAudit.AuditIssue("test", "test", PerformTokenAudit.IssueSeverity.MEDIUM)),
			riskFactor = PerformTokenAudit.RiskFactor.RED,
			riskFactorReason = "Test",
		)

		// when
		clock.advanceBy(10, ChronoUnit.MINUTES)
		val result = underTest(
			tokenAddress = ChainAddress(
				Chain.EVM_MAINNET,
				AddressWrapper("******************************************"),
			),
		)

		// then
		tokenAuditFinderService.findAll().first().processingStartedAt shouldBe clock.currentTime()

		verify(exactly = 1) { emitApplicationEvent<TokenAuditRequestedEvent>() }
	}
}
