package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.bot.query.GetAllBotsQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class GetAllBotsQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return all bots for user`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())
		integrationTestHelper.getWallet(id = 3.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getFirebaseUser(id = 10.toUUID(), email = "<EMAIL>")
		integrationTestHelper.getWallet(id = 30.toUUID(), userId = 10.toUUID())

		integrationTestHelper.getBot(
			id = 4.toUUID(),
			userId = 1.toUUID(),
			name = "TestBot",
			avatarFileId = 2.toUUID(),
			tradeAmount = BigDecimal("10.123"),
			buyFrequency = 5,
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
		)

		integrationTestHelper.getBot(
			id = 5.toUUID(),
			userId = 1.toUUID(),
			name = "TestBot1",
			avatarFileId = 2.toUUID(),
			tradeAmount = BigDecimal("11"),
			buyFrequency = 6,
			profitTargetFraction = BigDecimal("0.11"),
			stopLossFraction = BigDecimal("0.051"),
		)

		integrationTestHelper.getBot(
			id = 6.toUUID(),
			userId = 10.toUUID(),
			name = "TestBot2",
			avatarFileId = 2.toUUID(),
			tradeAmount = BigDecimal("100.902"),
			buyFrequency = 50,
			profitTargetFraction = BigDecimal("0.2"),
			stopLossFraction = BigDecimal("0.1"),
		)

		val user1Results = queryBus(GetAllBotsQuery(userId = 1.toUUID()))
		val user2Results = queryBus(GetAllBotsQuery(userId = 10.toUUID()))

		user1Results shouldHaveSize 2
		user2Results shouldHaveSize 1

		user1Results.single { it.id == 4.toUUID() }.run {
			userReadableId shouldBe "00000000000000000000000000000004fatbot"
			name shouldBe "TestBot"
			avatarFileId shouldBe 2.toUUID()
			tradeAmount shouldBe BigDecimal("10.123")
			buyFrequency shouldBe 5.toBigInteger()
			profitTargetFraction shouldBe BigDecimal("0.1")
			stopLossFraction shouldBe BigDecimal("0.05")
			tokenTickerCopyIsChecked shouldBe false
			creatorHighBuyIsChecked shouldBe false
			bundledBuysDetectedIsChecked shouldBe false
			suspiciousWalletsDetectedIsChecked shouldBe false
			singleHighBuyIsChecked shouldBe false
			shouldWaitBeforeBuying shouldBe false
			shouldAutoSellAfterHoldTime shouldBe false
		}

		user1Results.single { it.id == 5.toUUID() }.run {
			userReadableId shouldBe "00000000000000000000000000000005fatbot"
			name shouldBe "TestBot1"
			avatarFileId shouldBe 2.toUUID()
			tradeAmount shouldBe BigDecimal("11")
			buyFrequency shouldBe 6.toBigInteger()
			profitTargetFraction shouldBe BigDecimal("0.11")
			stopLossFraction shouldBe BigDecimal("0.051")
			tokenTickerCopyIsChecked shouldBe false
			creatorHighBuyIsChecked shouldBe false
			bundledBuysDetectedIsChecked shouldBe false
			suspiciousWalletsDetectedIsChecked shouldBe false
			singleHighBuyIsChecked shouldBe false
			shouldWaitBeforeBuying shouldBe false
			shouldAutoSellAfterHoldTime shouldBe false
		}

		user2Results.single { it.id == 6.toUUID() }.run {
			userReadableId shouldBe "00000000000000000000000000000006fatbot"
			name shouldBe "TestBot2"
			avatarFileId shouldBe 2.toUUID()
			tradeAmount shouldBe BigDecimal("100.902")
			buyFrequency shouldBe 50.toBigInteger()
			profitTargetFraction shouldBe BigDecimal("0.2")
			stopLossFraction shouldBe BigDecimal("0.1")
			tokenTickerCopyIsChecked shouldBe false
			creatorHighBuyIsChecked shouldBe false
			bundledBuysDetectedIsChecked shouldBe false
			suspiciousWalletsDetectedIsChecked shouldBe false
			singleHighBuyIsChecked shouldBe false
			shouldWaitBeforeBuying shouldBe false
			shouldAutoSellAfterHoldTime shouldBe false
		}
	}
}
