package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.user.query.GetEnabledChainsQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.shouldBe
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.Test

class GetEnabledChainsQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should get enabled chains`() {
		// when
		val result = queryBus(GetEnabledChainsQuery())

		// then
		result.enabledChains.size shouldBe 5

		result.enabledChains shouldContain Chain.EVM_MAINNET
		result.enabledChains shouldContain Chain.EVM_BASE
		result.enabledChains shouldContain Chain.EVM_BSC
		result.enabledChains shouldContain Chain.EVM_ARBITRUM_ONE
		result.enabledChains shouldContain Chain.SOLANA
	}
}
