package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.solana.client.FatbotTsProxyConnector
import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalances
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.query.GetPredictedTokenAmountOnBuyQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.equals.shouldBeEqual
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.beans.factory.annotation.Autowired

class GetPredictedTokenAmountOnBuyQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should correctly calculate Pumpswap amount out`() {
		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("CrghoogVHHk5EzYr4NSQKWrdutKpMyWXNwwLHCnmpump"),
			tokenDecimals = 6.toBigInteger(),
			isVerified = true,
			isToken2022 = false,
		)

		mockkStatic(rpcClient::getTokenAccountBalances)

		val tokenVault = PublicKey("Hqubau2PKjc5jNs8wAfHFu868LbEHKMeUtDNQfD1wFy")
		val wsolVault = PublicKey("Ek6UhxsYZ22s3WGvT4Hoh4HwkxEymz5R7MUDJLqrBoqy")

		every {
			rpcClient.getTokenAccountBalances(listOf(tokenVault, wsolVault), Commitment.CONFIRMED)
		} returns listOf("***************".toBigInteger(), "************".toBigInteger())

		val result = queryBus(
			GetPredictedTokenAmountOnBuyQuery(
				tokenAddress = AddressWrapper("CrghoogVHHk5EzYr4NSQKWrdutKpMyWXNwwLHCnmpump"),
				buyForCurrencyNativeAmount = 0.1.toBigDecimal().asNativeAmount(),
				chain = Chain.SOLANA,
				dexPairInfo = GetDex.PumpSwap(pairAddress = AddressWrapper("4dzhz86UxRwCjovFzR91QaK5oABC1c4jvcCXmpzV5kzA")),
			),
		)

		result.predictedTokenNativeAmount shouldBeEqual "58648.857130".toBigDecimal().asNativeAmount()
	}

	@Test
	fun `should correctly calculate Pumpfun amount out`() {
		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("3bHhsPBcRumkzkxTDg9AdCmZpf9ggprhU6NtZb2Vpump"),
			tokenDecimals = 6.toBigInteger(),
			isVerified = true,
			isToken2022 = false,
		)

		/*
		{
		  "virtualTokenReserves": "***************",
		  "virtualSolReserves": "58879665670",
		  "realTokenReserves": "266808309961522",
		  "realSolReserves": "28879665670",
		  "tokenTotalSupply": "1000000000000000",
		  "complete": false,
		  "creator": "B8eaDdExDY3rxLLtmR8qahEjoQQVnAPyfH1du8rbw2ej"
		}
		 */
		@Suppress("ktlint:standard:max-line-length")
		val curveStateBytes =
			byteArrayOf(23, -73, -8, 55, 96, -40, -84, 96, 50, 107, -100, 114, 58, -15, 1, 0, 6, 102, -128, -75, 13, 0, 0, 0, 50, -45, -119, 38, -87, -14, 0, 0, 6, -70, 92, -71, 6, 0, 0, 0, 0, -128, -58, -92, 126, -115, 3, 0, 0, -106, -118, 107, 37, 31, -8, 101, -90, 72, 52, -117, 64, 19, -56, 83, 40, 56, 33, 9, -103, 23, -5, -126, -23, 34, 106, -38, 50, -83, 33, -101, -96, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)

		every { rpcClient.api.getAccountInfo(PublicKey("3LGdSpadHTCMAcYRR2LZkr5VBczveiFLq8JcbCqpvHs3")) } returns mockk {
			every { decodedData } returns curveStateBytes
		}

		val result = queryBus(
			GetPredictedTokenAmountOnBuyQuery(
				tokenAddress = AddressWrapper("3bHhsPBcRumkzkxTDg9AdCmZpf9ggprhU6NtZb2Vpump"),
				buyForCurrencyNativeAmount = 0.13.toBigDecimal().asNativeAmount(),
				chain = Chain.SOLANA,
				dexPairInfo = GetDex.PumpFun(pairAddress = AddressWrapper("3LGdSpadHTCMAcYRR2LZkr5VBczveiFLq8JcbCqpvHs3")),
			),
		)

		result.predictedTokenNativeAmount shouldBe "1192515.332815".toBigDecimal().asNativeAmount()
	}

	@Test
	fun `should call proxy for Raydium CLMM amount out`() {
		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("3bHhsPBcRumkzkxTDg9AdCmZpf9ggprhU6NtZb2Vpump"),
			tokenDecimals = 6.toBigInteger(),
			isVerified = true,
			isToken2022 = false,
		)

		every {
			fatbotTsProxyConnector.computeSwapCLMM(
				poolAddress = AddressWrapper("3LGdSpadHTCMAcYRR2LZkr5VBczveiFLq8JcbCqpvHs3"),
				inputAmount = 0.15e9.toLong().toBigInteger().asBaseAmount(),
				inputTokenMint = SolanaConstants.WSOL_MINT_ADDRESS,
			)
		} returns FatbotTsProxyConnector.ComputeSwapCLMMResponse(
			expectedAmountOut = 451.123e6.toBigDecimal().toBigInteger(),
			remainingAccounts = mockk(),
			observationId = mockk(),
			ammConfig = mockk(),
		)

		val result = queryBus(
			GetPredictedTokenAmountOnBuyQuery(
				tokenAddress = AddressWrapper("3bHhsPBcRumkzkxTDg9AdCmZpf9ggprhU6NtZb2Vpump"),
				buyForCurrencyNativeAmount = 0.15.toBigDecimal().asNativeAmount(),
				chain = Chain.SOLANA,
				dexPairInfo = GetDex.Raydium(
					pairAddress = AddressWrapper("3LGdSpadHTCMAcYRR2LZkr5VBczveiFLq8JcbCqpvHs3"),
					poolType = GetDex.PoolType.CLMM,
				),
			),
		)

		result.predictedTokenNativeAmount.amount shouldBeEqualComparingTo "451.123".toBigDecimal()

		verify(exactly = 1) {
			fatbotTsProxyConnector.computeSwapCLMM(
				poolAddress = AddressWrapper("3LGdSpadHTCMAcYRR2LZkr5VBczveiFLq8JcbCqpvHs3"),
				inputAmount = 0.15e9.toLong().toBigInteger().asBaseAmount(),
				inputTokenMint = SolanaConstants.WSOL_MINT_ADDRESS,
			)
		}
	}
}
