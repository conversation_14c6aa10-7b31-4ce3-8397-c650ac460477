package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.adapter.out.evm.GetTokenPricesEVM
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.bot.constant.BotStatus
import com.cleevio.fatbot.application.module.bot.query.CompareBotsQuery
import com.cleevio.fatbot.application.module.bot.query.SearchUserBotDetailQuery
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.botmarket.query.SearchBotMarketPositionQuery
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.getPrivatePropertyOfProxy
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION
import com.cleevio.fatbot.toUUID
import com.github.benmanes.caffeine.cache.AsyncCache
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import io.mockk.mockk
import org.jooq.DSLContext
import org.junit.jupiter.api.Test
import org.p2p.solanaj.core.PublicKey
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.net.URI
import java.time.Instant

class SearchUserBotDetailQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val dslContext: DSLContext,
) : IntegrationTest() {

	@Test
	fun `should correctly compute bot detail page of bot with no market positions`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())
		integrationTestHelper.getBot(id = 10.toUUID(), userId = 1.toUUID(), avatarFileId = 99999.toUUID())
		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("1000000000"))
			},
		)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		val result = queryBus(
			SearchUserBotDetailQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotDetailQuery.Filter(
					botId = 10.toUUID(),
					timeRange = TimeRange.HOUR,
				),
			),
		)

		with(result) {
			this.botTransactions shouldBe emptyList()
			this.activeBotMarketPositions shouldBe emptyList()
			this.closedBotMarketPositions shouldBe emptyList()
			this.botPortfolioLastValues shouldBe listOf(
				CompareBotsQuery.BotPortfolioPastValue(
					portfolioValueUsd = BigDecimal("200.000000000"),
					createdAt = Instant.parse("2024-08-30T10:00:00Z"),
				),
			)
		}
	}

	@Test
	fun `should correctly compute bot detail page of bot with no market positions and portfolio values history`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())
		integrationTestHelper.getBot(id = 10.toUUID(), userId = 1.toUUID(), avatarFileId = 99999.toUUID())
		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("1000000000"))
			},
		)

		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 10.toUUID(),
			snapshotMadeAt = Instant.parse("2024-08-30T09:50:00Z"),
			portfolioValueUsd = BigDecimal("193.15"),
			acquisitionValueUsd = BigDecimal("73.53"),
		)
		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 10.toUUID(),
			snapshotMadeAt = Instant.parse("2024-08-30T09:55:00Z"),
			portfolioValueUsd = BigDecimal("198.25"),
			acquisitionValueUsd = BigDecimal("44.32"),
		)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		val result = queryBus(
			SearchUserBotDetailQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotDetailQuery.Filter(
					botId = 10.toUUID(),
					timeRange = TimeRange.HOUR,
				),
			),
		)

		with(result) {
			this.botTransactions shouldBe emptyList()
			this.activeBotMarketPositions shouldBe emptyList()
			this.closedBotMarketPositions shouldBe emptyList()
			this.botPortfolioLastValues shouldBe listOf(
				CompareBotsQuery.BotPortfolioPastValue(
					portfolioValueUsd = BigDecimal("193.15"),
					createdAt = Instant.parse("2024-08-30T09:50:00Z"),
				),
				CompareBotsQuery.BotPortfolioPastValue(
					portfolioValueUsd = BigDecimal("198.25"),
					createdAt = Instant.parse("2024-08-30T09:55:00Z"),
				),
				CompareBotsQuery.BotPortfolioPastValue(
					portfolioValueUsd = BigDecimal("200.000000000"),
					createdAt = Instant.parse("2024-08-30T10:00:00Z"),
				),
			)

			this.botTotalValueAmountUsd shouldBe BigDecimal("200.000000000")
			this.transactionsVolumeSum shouldBe BigDecimal.ZERO
		}
	}

	@Test
	fun `should correctly compute bot detail page of bot with open market position without chain tx confirmation`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())
		integrationTestHelper.getBot(id = 10.toUUID(), userId = 1.toUUID(), avatarFileId = 99999.toUUID())
		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("1000000000"))
			},
		)

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			tokenDecimals = BigInteger("6"),
			isToken2022 = false,
		)
		integrationTestHelper.getTokenPairInfo(
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			tokenDecimals = BigInteger("6"),
			chain = Chain.SOLANA,
			dexType = GetDex.Dex.PUMP_FUN,
			uniswapV3Fee = null,
			pairAddress = AddressWrapper("********************************************"),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setBuyDataFromChain(
					amountOut = BigInteger("1000000"), // 1 token (6 decimals)
					amountIn = BigInteger("40"),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("200"),
				)
			},
		)

		// force market position creation date to be set
		dslContext
			.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.parse("2024-08-30T09:30:00Z"))
			.execute()

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		// The bonding curve in a state where price of one token is 48 lamports
		every {
			rpcApi.getMultipleAccounts(listOf(PublicKey("********************************************")), any())
		} returns listOf(
			mockk {
				every { data } returns listOf(
					"F7f4N2DYrGDEtoNK7+cCAOjXqikJAAAAxB5x/l3pAQDoK4ctAgAAAACAxqR+jQMAAA==",
					"base64",
				)
			},
		)

		val result = queryBus(
			SearchUserBotDetailQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotDetailQuery.Filter(
					botId = 10.toUUID(),
					timeRange = TimeRange.HOUR,
				),
			),
		)

		with(result) {
			this.botTransactions shouldBe emptyList()
			this.activeBotMarketPositions shouldBe listOf(
				SearchBotMarketPositionQuery.Result(
					id = 1000.toUUID(),
					state = BotMarketPositionState.OPENED,
					tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
					tokenDetailUrl = URI.create("https://solscan.io/address/BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
					tokenName = "BOT TOKEN",
					tokenSymbol = "BOT",
					tokenChain = Chain.SOLANA,
					tokenImageUrl = null,
					openValueUsd = BigDecimal("0.000008200000000"),
					closeValueUsd = null,
					openTimeStampAt = Instant.parse("2024-08-30T09:30:00Z"),
					closedTimeStampAt = null,
					pnlAmountUsd = BigDecimal("0.000001400000000"),
					pnlAmountFraction = BigDecimal("0.170731707317073170731707317073171"),
					currentValueUsd = BigDecimal("0.000009600000000"),
				),
			)
			this.closedBotMarketPositions shouldBe emptyList()
			this.botPortfolioLastValues shouldBe listOf(
				CompareBotsQuery.BotPortfolioPastValue(
					portfolioValueUsd = BigDecimal("200.407865600000000"),
					createdAt = Instant.parse("2024-08-30T10:00:00Z"),
				),
			)

			this.botTotalValueAmountUsd shouldBe BigDecimal("200.407865600000000")
			this.transactionsVolumeSum shouldBe BigDecimal("0.000008200000000")
		}
	}

	@Test
	fun `should correctly compute bot detail page of bot with closed market position`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())
		integrationTestHelper.getBot(id = 10.toUUID(), userId = 1.toUUID(), avatarFileId = 99999.toUUID())
		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("1000000000"))
			},
		)

		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 10.toUUID(),
			snapshotMadeAt = Instant.parse("2024-08-30T09:50:00Z"),
			portfolioValueUsd = BigDecimal("193.15"),
			acquisitionValueUsd = BigDecimal("73.53"),
		)
		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 10.toUUID(),
			snapshotMadeAt = Instant.parse("2024-08-30T09:55:00Z"),
			portfolioValueUsd = BigDecimal("198.25"),
			acquisitionValueUsd = BigDecimal("44.32"),
		)

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			tokenDecimals = BigInteger("6"),
			isToken2022 = false,
		)
		integrationTestHelper.getTokenPairInfo(
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			tokenDecimals = BigInteger("6"),
			chain = Chain.SOLANA,
			dexType = GetDex.Dex.PUMP_FUN,
			uniswapV3Fee = null,
			pairAddress = AddressWrapper("********************************************"),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setBuyDataFromChain(
					amountOut = BigInteger("1000000"), // 1 token (6 decimals)
					amountIn = BigInteger("40"),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("200"),
				)
				closePositionWithSellMatch(
					assumedSellPrice = BigDecimal("0.00000005"),
				)
				setSellDataFromChain(
					amountOfTokensSold = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyReceived = BigInteger("50"),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("200"),
				)
			},
		)

		// force market position creation date to be set
		dslContext
			.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.parse("2024-08-30T09:30:00Z"))
			.execute()

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		val result = queryBus(
			SearchUserBotDetailQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotDetailQuery.Filter(
					botId = 10.toUUID(),
					timeRange = TimeRange.HOUR,
				),
			),
		)

		with(result) {
			this.botTransactions shouldBe emptyList()
			this.activeBotMarketPositions shouldBe emptyList()
			this.closedBotMarketPositions shouldNotBe emptyList<SearchBotMarketPositionQuery.Result>()
			this.botPortfolioLastValues shouldBe listOf(
				CompareBotsQuery.BotPortfolioPastValue(
					portfolioValueUsd = BigDecimal("193.15"),
					createdAt = Instant.parse("2024-08-30T09:50:00Z"),
				),
				CompareBotsQuery.BotPortfolioPastValue(
					portfolioValueUsd = BigDecimal("198.25"),
					createdAt = Instant.parse("2024-08-30T09:55:00Z"),
				),
				CompareBotsQuery.BotPortfolioPastValue(
					portfolioValueUsd = BigDecimal("200.000000000"),
					createdAt = Instant.parse("2024-08-30T10:00:00Z"),
				),
			)

			this.botTotalValueAmountUsd shouldBe BigDecimal("200.000000000")
			this.transactionsVolumeSum shouldBe BigDecimal("0.000018200")
		}
	}

	@Test
	fun `should correctly compute bot detail page number of transactions`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())
		integrationTestHelper.getBot(id = 10.toUUID(), userId = 1.toUUID(), avatarFileId = 99999.toUUID())
		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("1000000000"))
			},
		)

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			tokenDecimals = BigInteger("6"),
			isToken2022 = false,
		)
		integrationTestHelper.getTokenPairInfo(
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			tokenDecimals = BigInteger("6"),
			chain = Chain.SOLANA,
			dexType = GetDex.Dex.PUMP_FUN,
			uniswapV3Fee = null,
			pairAddress = AddressWrapper("********************************************"),
		)

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = AddressWrapper("HTYLJ5QTnq28HFUWQuBPKdc4RJu6JGb5ucesqAt3xTt8"),
			tokenDecimals = BigInteger("6"),
			isToken2022 = false,
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = AddressWrapper("GeqXFRAwL9dH1iCg1dMuh9ToUYS3QsfdWqK2fE5Xpump"),
			tokenDecimals = BigInteger("6"),
			isToken2022 = false,
		)

		// open position
		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setBuyDataFromChain(
					amountOut = BigInteger("1000000"), // 1 token (6 decimals)
					amountIn = BigInteger("40"),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("200"),
				)
			},
		)

		// and closed profitable position
		integrationTestHelper.getBotMarketPosition(
			id = 1001.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("HTYLJ5QTnq28HFUWQuBPKdc4RJu6JGb5ucesqAt3xTt8"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setBuyDataFromChain(
					amountOut = BigInteger("1000000"), // 1 token (6 decimals)
					amountIn = BigInteger("40"),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("200"),
				)
				closePositionWithSellMatch(
					assumedSellPrice = BigDecimal("0.00000005"),
				)
				setSellDataFromChain(
					amountOfTokensSold = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyReceived = BigInteger("50"),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("200"),
				)
			},
		)

		// and closed loosing position
		integrationTestHelper.getBotMarketPosition(
			id = 1002.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("GeqXFRAwL9dH1iCg1dMuh9ToUYS3QsfdWqK2fE5Xpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setBuyDataFromChain(
					amountOut = BigInteger("1000000"), // 1 token (6 decimals)
					amountIn = BigInteger("40"),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("200"),
				)
				closePositionWithSellMatch(
					assumedSellPrice = BigDecimal("0.00000005"),
				)
				setSellDataFromChain(
					amountOfTokensSold = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyReceived = BigInteger("20"),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("200"),
				)
			},
		)

		// force market position creation date to be set
		dslContext
			.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.parse("2024-08-30T09:30:00Z"))
			.execute()

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		// The bonding curve in a state where price of one token is 48 lamports
		every {
			rpcApi.getMultipleAccounts(listOf(PublicKey("********************************************")), any())
		} returns listOf(
			mockk {
				every { data } returns listOf(
					"F7f4N2DYrGDEtoNK7+cCAOjXqikJAAAAxB5x/l3pAQDoK4ctAgAAAACAxqR+jQMAAA==",
					"base64",
				)
			},
		)

		val result = queryBus(
			SearchUserBotDetailQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotDetailQuery.Filter(
					botId = 10.toUUID(),
					timeRange = TimeRange.HOUR,
				),
			),
		)

		with(result) {
			this.transactionsCount shouldBe 5 // 1x buy + 2x buy-sell (on closed)
			this.buyTransactionsCount shouldBe 3
			this.profitableTransactionsCount shouldBe 1
			this.lossTransactionsCount shouldBe 1
		}
	}

	@Test
	fun `should compute botStatus as insufficient balance when wallet balance is less than trade amount`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())
		integrationTestHelper.getBot(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			tradeAmount = BigDecimal("10.0"),
			isActive = true,
		)
		integrationTestHelper.getBotWallet(
			id = 200.toUUID(),
			botId = 20.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("1000000"))
			},
		)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		val result = queryBus(
			SearchUserBotDetailQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotDetailQuery.Filter(
					botId = 20.toUUID(),
					timeRange = TimeRange.HOUR,
				),
			),
		)

		result.botStatus shouldBe BotStatus.INSUFFICIENT_BALANCE
	}

	@Test
	fun `should compute botStatus as insufficient balance with open position`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())
		integrationTestHelper.getBot(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			tradeAmount = BigDecimal("10.0"),
			isActive = true,
		)
		integrationTestHelper.getBotWallet(
			id = 200.toUUID(),
			botId = 20.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("1000000"))
			},
		)

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			name = "ADHD",
			symbol = "ADHD",
			imageFileId = null,
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotMarketPosition(
			id = 400.toUUID(),
			botWalletId = 200.toUUID(),
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			profitTargetFraction = BigDecimal("20.0"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234567,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "50".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(
						AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump")
							.toChainAddress(chain = Chain.SOLANA),
						BaseAmount(BigInteger("100")),
					)
				},
			)

		val result = queryBus(
			SearchUserBotDetailQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotDetailQuery.Filter(
					botId = 20.toUUID(),
					timeRange = TimeRange.HOUR,
				),
			),
		)

		result.botStatus shouldBe BotStatus.INSUFFICIENT_BALANCE_FUNDS_IN_OPEN_POSITIONS
	}

	@Test
	fun `should compute botStatus as deactivated when bot is not active`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())
		integrationTestHelper.getBot(
			id = 21.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			isActive = false,
			buyFrequency = 10,
			beforeSave = {
				repeat(10) { decreaseRemainingBuys() }
			},
		)
		integrationTestHelper.getBotWallet(
			id = 201.toUUID(),
			botId = 21.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("1000000"))
			},
		)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		val result = queryBus(
			SearchUserBotDetailQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotDetailQuery.Filter(
					botId = 21.toUUID(),
					timeRange = TimeRange.HOUR,
				),
			),
		)

		result.botStatus shouldBe BotStatus.DEACTIVATED
	}

	@Test
	fun `should compute botStatus as buy daily limit reached when remaining buy frequency is 0`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())
		integrationTestHelper.getBot(
			id = 22.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			isActive = true,
			buyFrequency = 10,
			beforeSave = {
				repeat(10) { decreaseRemainingBuys() }
			},
		)
		integrationTestHelper.getBotWallet(
			id = 202.toUUID(),
			botId = 22.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("10000000000"))
			},
		)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		val result = queryBus(
			SearchUserBotDetailQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotDetailQuery.Filter(
					botId = 22.toUUID(),
					timeRange = TimeRange.HOUR,
				),
			),
		)

		result.botStatus shouldBe BotStatus.BUY_DAILY_LIMIT_REACHED
	}

	@Test
	fun `should compute botStatus as no purchase when there are no buy transactions`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())
		integrationTestHelper.getBot(
			id = 23.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			isActive = true,
		)
		integrationTestHelper.getBotWallet(
			id = 203.toUUID(),
			botId = 23.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("10000000000"))
			},
		)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		val result = queryBus(
			SearchUserBotDetailQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotDetailQuery.Filter(
					botId = 23.toUUID(),
					timeRange = TimeRange.HOUR,
				),
			),
		)

		result.botStatus shouldBe BotStatus.NO_PURCHASE
	}

	@Test
	fun `should compute botStatus as has already purchased when there is at least one buy transaction`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())
		integrationTestHelper.getBot(
			id = 24.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			isActive = true,
		)
		integrationTestHelper.getBotWallet(
			id = 204.toUUID(),
			botId = 24.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("10000000000"))
			},
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			tokenDecimals = BigInteger("6"),
			isToken2022 = false,
		)
		integrationTestHelper.getBotMarketPosition(
			botWalletId = 204.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"),
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"),
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setBuyDataFromChain(
					amountOut = BigInteger("1000000"),
					amountIn = BigInteger("40"),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("200"),
				)
			},
		)

		// force market position creation date to be set
		dslContext
			.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.parse("2024-08-30T09:30:00Z"))
			.execute()

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(
						AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump").toChainAddress(chain = Chain.SOLANA),
						BaseAmount(BigInteger("100")),
					)
				},
			)

		val result = queryBus(
			SearchUserBotDetailQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotDetailQuery.Filter(
					botId = 24.toUUID(),
					timeRange = TimeRange.HOUR,
				),
			),
		)

		result.botStatus shouldBe BotStatus.HAS_PURCHASED
	}
}
