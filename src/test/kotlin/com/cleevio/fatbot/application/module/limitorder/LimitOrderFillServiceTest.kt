package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.module.limitorder.event.LimitOrderMatchedEvent
import com.cleevio.fatbot.application.module.limitorder.service.LimitOrderFillService
import com.cleevio.fatbot.application.module.limitorder.service.LimitOrderMatchingService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderRepository
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.toBigInteger
import com.cleevio.fatbot.toSolanaAddress
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.PayloadApplicationEvent

class LimitOrderFillServiceTest(
	@Autowired private val limitOrderFillService: LimitOrderFillService,
	@Autowired private val limitOrderRepository: LimitOrderRepository,
) : IntegrationTest() {

	@Test
	fun `should lock limit order and throw while locked`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			address = "abc".toSolanaAddress(),
			chain = Chain.SOLANA,
		)

		integrationTestHelper.getLimitOrder(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = "a".toSolanaAddress(),
			chain = Chain.SOLANA,
			type = LimitOrderType.BUY,
			initialAmount = 1e9.toBigInteger(),
			limitPrice = 0.05e9.toBigInteger(),
		)

		val result = LimitOrderMatchingService.Result(
			orderId = 10.toUUID(),
			walletId = 20.toUUID(),
			amountIn = 0.05e9.toBigInteger().asBaseAmount(),
			chain = Chain.SOLANA,
			type = LimitOrderType.BUY,
			walletBalance = 0.05e9.toBigInteger().asBaseAmount(),
			tokenAddress = "a".toSolanaAddress(),
			pairAddress = "a1".toSolanaAddress(),
		)

		// Capture LimitOrderMatchedEvent
		val captureSlot = slot<PayloadApplicationEvent<LimitOrderMatchedEvent>>()
		every { emitApplicationEvent<LimitOrderMatchedEvent>(captureSlot) } just Runs

		limitOrderFillService.enqueue(result)

		captureSlot.isCaptured shouldBe true
		captureSlot.captured.payload.run {
			orderId shouldBe 10.toUUID()
			walletId shouldBe 20.toUUID()
			chain shouldBe Chain.SOLANA
			type shouldBe LimitOrderType.BUY
			amount shouldBe 0.05e9.toBigInteger().asBaseAmount()
			tokenAddress shouldBe "a".toSolanaAddress()
			pairAddress shouldBe "a1".toSolanaAddress()
		}

		shouldThrow<IllegalStateException> {
			limitOrderFillService.enqueue(result)
		}

		val orders = limitOrderRepository.findAll()
		orders shouldHaveSize 1
		orders.single().run {
			id shouldBe 10.toUUID()
			isLocked shouldBe true
		}

		verify(exactly = 1) { emitApplicationEvent<LimitOrderMatchedEvent>() }
	}
}
