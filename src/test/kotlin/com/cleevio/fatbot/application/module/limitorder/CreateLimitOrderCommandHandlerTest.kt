package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.module.limitorder.command.CreateLimitOrderCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderRepository
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.toBigInteger
import com.cleevio.fatbot.toSolanaAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class CreateLimitOrderCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val limitOrderRepository: LimitOrderRepository,
) : IntegrationTest() {

	@Test
	fun `should create buy limit order`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = 1.toUUID())

		// when
		commandBus(
			CreateLimitOrderCommand(
				userId = 1.toUUID(),
				walletId = 2.toUUID(),
				tokenAddress = "a".toSolanaAddress(),
				chain = Chain.SOLANA,
				limitPrice = 0.001.toBigDecimal().asNativeAmount(),
				initialAmount = 1.toBigDecimal().asNativeAmount(),
				type = LimitOrderType.BUY,
			),
		)

		// then
		val limitOrders = limitOrderRepository.findAll()
		limitOrders shouldHaveSize 1
		val limitOrder = limitOrders.single()

		limitOrder.userId shouldBe 1.toUUID()
		limitOrder.walletId shouldBe 2.toUUID()
		limitOrder.tokenAddress shouldBe "a".toSolanaAddress()
		limitOrder.chain shouldBe Chain.SOLANA
		limitOrder.type shouldBe LimitOrderType.BUY
		limitOrder.limitPrice shouldBe 0.001e9.toBigInteger()
		limitOrder.remainingAmount shouldBe 1e9.toBigInteger()
	}

	@Test
	fun `should create sell limit order`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			id = 10.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = "a".toSolanaAddress(),
			tokenDecimals = 6.toBigInteger(),
		)

		// when
		commandBus(
			CreateLimitOrderCommand(
				userId = 1.toUUID(),
				walletId = 2.toUUID(),
				tokenAddress = "a".toSolanaAddress(),
				chain = Chain.SOLANA,
				limitPrice = 0.001.toBigDecimal().asNativeAmount(),
				initialAmount = 1.toBigDecimal().asNativeAmount(),
				type = LimitOrderType.SELL,
			),
		)

		// then
		val limitOrders = limitOrderRepository.findAll()
		limitOrders shouldHaveSize 1
		val limitOrder = limitOrders.single()

		limitOrder.userId shouldBe 1.toUUID()
		limitOrder.walletId shouldBe 2.toUUID()
		limitOrder.tokenAddress shouldBe "a".toSolanaAddress()
		limitOrder.chain shouldBe Chain.SOLANA
		limitOrder.type shouldBe LimitOrderType.SELL
		limitOrder.limitPrice shouldBe 0.001e9.toBigInteger()
		limitOrder.remainingAmount shouldBe 1e6.toBigInteger()
	}
}
