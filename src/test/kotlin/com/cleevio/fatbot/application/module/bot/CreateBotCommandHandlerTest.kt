package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.bot.command.CreateBotCommand
import com.cleevio.fatbot.application.module.bot.exception.MaxActiveBotsExceeded
import com.cleevio.fatbot.application.module.botdraft.model.BotDraftUpdate
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.bot.BotRepository
import com.cleevio.fatbot.domain.botdraft.BotDraftRepository
import com.cleevio.fatbot.domain.botwallet.BotWalletRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.Duration
import java.time.temporal.ChronoUnit

class CreateBotCommandHandlerTest(
	@Autowired private val botDraftRepository: BotDraftRepository,
	@Autowired private val botRepository: BotRepository,
	@Autowired private val botWalletRepository: BotWalletRepository,
	@Autowired private val commandBus: CommandBus,
) : IntegrationTest() {

	@Test
	fun `should create bot from bot draft`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val botDraft = integrationTestHelper.getBotDraft(id = 2.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getFile(id = 3.toUUID())

		botDraft.update(
			BotDraftUpdate(
				name = "Test",
				buyFrequency = 10,
				tradeAmount = BigDecimal("5"),
				avatarFileId = 3.toUUID(),
				profitTargetFraction = BigDecimal("0.1"),
				stopLossFraction = BigDecimal("0.05"),
				liquidityFromUsd = BigDecimal("100"),
				liquidityToUsd = BigDecimal("200"),
				marketCapFromUsd = BigDecimal("123"),
				marketCapToUsd = BigDecimal("321"),
				buyVolume = BigDecimal("0.5"),
				sellVolume = BigDecimal("0.5"),
				numberOfHoldersFrom = null,
				numberOfHoldersTo = 10000,
				dailyVolumeFromUsd = BigDecimal("100000"),
				dailyVolumeToUsd = null,
				sellTransactionFraction = BigDecimal("0.1"),
				buyTransactionFraction = BigDecimal("0.9"),
				tokenTickerCopyIsChecked = true,
				creatorHighBuyIsChecked = true,
				bundledBuysDetectedIsChecked = true,
				suspiciousWalletsDetectedIsChecked = true,
				singleHighBuyIsChecked = true,
				buyTokensAliveAtLeastFor = Duration.of(5, ChronoUnit.MINUTES),
				shouldAutoSellAfterHoldTime = true,
			),
		)

		botDraftRepository.save(botDraft)

		val result = commandBus(
			CreateBotCommand(userId = 1.toUUID(), botDraftId = 2.toUUID()),
		)
		val newBot = botRepository.findByIdOrNull(id = result.botId)
		val newBotWallet = botWalletRepository.findAll().find { it.botId == result.botId }

		newBot shouldNotBe null
		newBotWallet shouldNotBe null
		botDraftRepository.findByIdOrNull(id = 2.toUUID()) shouldBe null

		newBot!!.run {
			name shouldBe "Test"
			buyFrequency shouldBe 10
			tradeAmount shouldBe BigDecimal("5")
			avatarFileId shouldBe 3.toUUID()
			profitTargetFraction shouldBe BigDecimal("0.1")
			stopLossFraction shouldBe BigDecimal("0.05")
			liquidityFromUsd shouldBe BigDecimal("100")
			liquidityToUsd shouldBe BigDecimal("200")
			marketCapFromUsd shouldBe BigDecimal("123")
			marketCapToUsd shouldBe BigDecimal("321")
			buyVolume shouldBe BigDecimal("0.5")
			sellVolume shouldBe BigDecimal("0.5")
			numberOfHoldersFrom shouldBe null
			numberOfHoldersTo shouldBe 10000
			dailyVolumeFromUsd shouldBe BigDecimal("100000")
			dailyVolumeToUsd shouldBe null
			sellTransactionFraction shouldBe BigDecimal("0.1")
			buyTransactionFraction shouldBe BigDecimal("0.9")
			sellToBuyTransactionRatio shouldBe BigDecimal("0.**********************************")
			tokenTickerCopyIsChecked shouldBe true
			creatorHighBuyIsChecked shouldBe true
			bundledBuysDetectedIsChecked shouldBe true
			suspiciousWalletsDetectedIsChecked shouldBe true
			singleHighBuyIsChecked shouldBe true
			buyTokensAliveAtLeastFor shouldBe Duration.of(5, ChronoUnit.MINUTES)
			shouldAutoSellAfterHoldTime shouldBe true
		}

		newBotWallet!!.chain shouldBe Chain.SOLANA
	}

	@Test
	fun `should throw on missing required values`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getBotDraft(id = 2.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getFile(id = 3.toUUID())

		val baseUpdate = BotDraftUpdate(
			name = "Test",
			buyFrequency = 10,
			tradeAmount = BigDecimal("5"),
			avatarFileId = 3.toUUID(),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			liquidityFromUsd = BigDecimal("100"),
			liquidityToUsd = BigDecimal("200"),
			marketCapFromUsd = BigDecimal("123"),
			marketCapToUsd = BigDecimal("321"),
			buyVolume = BigDecimal("0.5"),
			sellVolume = BigDecimal("0.5"),
			numberOfHoldersFrom = null,
			numberOfHoldersTo = 10000,
			dailyVolumeFromUsd = BigDecimal("100000"),
			dailyVolumeToUsd = null,
			sellTransactionFraction = BigDecimal("0.1"),
			buyTransactionFraction = BigDecimal("0.9"),
			tokenTickerCopyIsChecked = false,
			creatorHighBuyIsChecked = false,
			bundledBuysDetectedIsChecked = false,
			suspiciousWalletsDetectedIsChecked = false,
			singleHighBuyIsChecked = false,
			buyTokensAliveAtLeastFor = null,
			shouldAutoSellAfterHoldTime = false,
		)

		val invalidUpdates = listOf(
			baseUpdate.copy(name = null),
			baseUpdate.copy(buyFrequency = null),
			baseUpdate.copy(tradeAmount = null),
			baseUpdate.copy(avatarFileId = null),
			baseUpdate.copy(profitTargetFraction = null),
			baseUpdate.copy(stopLossFraction = null),
		)

		invalidUpdates.forEach { draftUpdate ->
			botDraftRepository.findByIdOrNull(2.toUUID())!!
				.apply { update(draftUpdate) }
				.let { botDraftRepository.save(it) }

			shouldThrow<IllegalArgumentException> {
				commandBus(
					CreateBotCommand(userId = 1.toUUID(), botDraftId = 2.toUUID()),
				)
			}
		}
	}

	@Test
	fun `maximum number of active bots exceeded - should throw exception`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 3.toUUID())
		integrationTestHelper.getBot(id = 1.toUUID(), avatarFileId = 3.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getBot(id = 2.toUUID(), avatarFileId = 3.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getBot(id = 3.toUUID(), avatarFileId = 3.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getBotDraft(id = 2.toUUID(), userId = 1.toUUID()).also {
			it.update(
				BotDraftUpdate(
					name = "Test",
					buyFrequency = 10,
					tradeAmount = BigDecimal("5"),
					avatarFileId = 3.toUUID(),
					profitTargetFraction = BigDecimal("0.1"),
					stopLossFraction = BigDecimal("0.05"),
					liquidityFromUsd = BigDecimal("100"),
					liquidityToUsd = BigDecimal("200"),
					marketCapFromUsd = BigDecimal("123"),
					marketCapToUsd = BigDecimal("321"),
					buyVolume = BigDecimal("0.5"),
					sellVolume = BigDecimal("0.5"),
					numberOfHoldersFrom = null,
					numberOfHoldersTo = 10000,
					dailyVolumeFromUsd = BigDecimal("100000"),
					dailyVolumeToUsd = null,
					sellTransactionFraction = BigDecimal("0.1"),
					buyTransactionFraction = BigDecimal("0.9"),
					tokenTickerCopyIsChecked = false,
					creatorHighBuyIsChecked = false,
					bundledBuysDetectedIsChecked = false,
					suspiciousWalletsDetectedIsChecked = false,
					singleHighBuyIsChecked = false,
					buyTokensAliveAtLeastFor = null,
					shouldAutoSellAfterHoldTime = false,
				),
			)

			botDraftRepository.save(it)
		}

		shouldThrow<MaxActiveBotsExceeded> {
			commandBus(
				CreateBotCommand(userId = 1.toUUID(), botDraftId = 2.toUUID()),
			)
		}
	}
}
