package com.cleevio.fatbot.application.module.userstatistics

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.userstatistics.command.ResetUserStreakCommand
import com.cleevio.fatbot.application.module.userstatistics.finder.UserStatisticsFinderService
import com.cleevio.fatbot.domain.userstatistics.StreakState
import com.cleevio.fatbot.setAndReturnPrivateProperty
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.ZoneOffset

class ResetUserStreakCommandHandlerTest(
	@Autowired private val integrationTestClock: IntegrationTestClock,
	@Autowired private val commandBus: CommandBus,
	@Autowired private val userStatisticsFinderService: UserStatisticsFinderService,
) : IntegrationTest() {

	@Test
	fun `should reset user streak`() {
		// given
		val lastStreakStartedAtTimestamp = integrationTestClock.getCurrentDate().minusDays(2)
			.atStartOfDay().toInstant(ZoneOffset.UTC)

		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			daysInStreak = 0,
			lastManualTradeAt = integrationTestClock.getCurrentDate().minusDays(1),
			beforeSave = {
				setAndReturnPrivateProperty("streakState", StreakState.FAILED)
				setAndReturnPrivateProperty("lastStreakStartedAt", lastStreakStartedAtTimestamp)
			},
		)

		// when
		commandBus(ResetUserStreakCommand(userId = 1.toUUID()))

		// then
		val userStatistics = userStatisticsFinderService.getByUserId(userId = 1.toUUID())
		userStatistics.run {
			id shouldBe 2.toUUID()
			userId shouldBe 1.toUUID()
			streakState shouldBe StreakState.NOT_STARTED
			lastStreakStartedAt shouldBe null
			daysInStreak shouldBe 0
		}
	}
}
