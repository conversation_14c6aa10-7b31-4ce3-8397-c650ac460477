package com.cleevio.fatbot.application.module.botmarket

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.aggregator.response.GetTokenPriceChartResponse
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.botmarket.query.GetBotMarketPositionPriceChartQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldContainExactly
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.Instant

class GetBotMarketPositionPriceChartQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should work when no snapshot is present`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 999.toUUID())

		integrationTestHelper.getBot(id = 10.toUUID(), userId = 1.toUUID(), avatarFileId = 999.toUUID())
		integrationTestHelper.getBotWallet(id = 100.toUUID(), botId = 10.toUUID())

		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
		)

		every {
			aggregatorConnector.getTokenPriceChart(
				AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			)
		} returns listOf()

		val result = queryBus(
			GetBotMarketPositionPriceChartQuery(
				userId = 1.toUUID(),
				botId = 10.toUUID(),
				botMarketPositionId = 1000.toUUID(),
			),
		)

		result shouldContainExactly listOf()
	}

	@Test
	fun `should work when one snapshot is present`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 999.toUUID())

		integrationTestHelper.getBot(id = 10.toUUID(), userId = 1.toUUID(), avatarFileId = 999.toUUID())
		integrationTestHelper.getBotWallet(id = 100.toUUID(), botId = 10.toUUID())

		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
		)

		every {
			aggregatorConnector.getTokenPriceChart(
				AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			)
		} returns listOf(
			GetTokenPriceChartResponse(
				timestamp = Instant.parse("2025-06-17T00:00:00Z"),
				priceUsd = BigDecimal("1"),
			),
		)

		val result = queryBus(
			GetBotMarketPositionPriceChartQuery(
				userId = 1.toUUID(),
				botId = 10.toUUID(),
				botMarketPositionId = 1000.toUUID(),
			),
		)

		result shouldContainExactly listOf(
			GetBotMarketPositionPriceChartQuery.Result(
				timestamp = Instant.parse("2025-06-17T00:00:00Z"),
				openUsd = BigDecimal("1"),
				highUsd = BigDecimal("1"),
				lowUsd = BigDecimal("1"),
				closeUsd = BigDecimal("1"),
			),
		)
	}

	@Test
	fun `should work when more snapshots are present`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 999.toUUID())

		integrationTestHelper.getBot(id = 10.toUUID(), userId = 1.toUUID(), avatarFileId = 999.toUUID())
		integrationTestHelper.getBotWallet(id = 100.toUUID(), botId = 10.toUUID())

		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
		)

		every {
			aggregatorConnector.getTokenPriceChart(
				AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			)
		} returns listOf(
			GetTokenPriceChartResponse(
				timestamp = Instant.parse("2025-06-17T00:00:00Z"),
				priceUsd = BigDecimal("1"),
			),
			GetTokenPriceChartResponse(
				timestamp = Instant.parse("2025-06-17T00:00:01Z"),
				priceUsd = BigDecimal("2"),
			),
			GetTokenPriceChartResponse(
				timestamp = Instant.parse("2025-06-17T00:00:05Z"),
				priceUsd = BigDecimal("5"),
			),
		)

		val result = queryBus(
			GetBotMarketPositionPriceChartQuery(
				userId = 1.toUUID(),
				botId = 10.toUUID(),
				botMarketPositionId = 1000.toUUID(),
			),
		)

		result shouldContainExactly listOf(
			GetBotMarketPositionPriceChartQuery.Result(
				timestamp = Instant.parse("2025-06-17T00:00:00Z"),
				openUsd = BigDecimal("1"),
				highUsd = BigDecimal("1"),
				lowUsd = BigDecimal("1"),
				closeUsd = BigDecimal("2"),
			),
			GetBotMarketPositionPriceChartQuery.Result(
				timestamp = Instant.parse("2025-06-17T00:00:01Z"),
				openUsd = BigDecimal("2"),
				highUsd = BigDecimal("2"),
				lowUsd = BigDecimal("2"),
				closeUsd = BigDecimal("5"),
			),
			GetBotMarketPositionPriceChartQuery.Result(
				timestamp = Instant.parse("2025-06-17T00:00:05Z"),
				openUsd = BigDecimal("5"),
				highUsd = BigDecimal("5"),
				lowUsd = BigDecimal("5"),
				closeUsd = BigDecimal("5"),
			),
		)
	}

	@Test
	fun `should work when more snapshots are present with same timestamp`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 999.toUUID())

		integrationTestHelper.getBot(id = 10.toUUID(), userId = 1.toUUID(), avatarFileId = 999.toUUID())
		integrationTestHelper.getBotWallet(id = 100.toUUID(), botId = 10.toUUID())

		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
		)

		every {
			aggregatorConnector.getTokenPriceChart(
				AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			)
		} returns listOf(
			GetTokenPriceChartResponse(
				timestamp = Instant.parse("2025-06-17T11:55:40.123Z"),
				priceUsd = BigDecimal("4"),
			),
			GetTokenPriceChartResponse(
				timestamp = Instant.parse("2025-06-17T12:00:00.000Z"),
				priceUsd = BigDecimal("0"),
			),
			GetTokenPriceChartResponse(
				timestamp = Instant.parse("2025-06-17T12:00:01.100Z"),
				priceUsd = BigDecimal("1"),
			),
			GetTokenPriceChartResponse(
				timestamp = Instant.parse("2025-06-17T12:00:01.250Z"),
				priceUsd = BigDecimal("10"),
			),
			GetTokenPriceChartResponse(
				timestamp = Instant.parse("2025-06-17T12:00:01.400Z"),
				priceUsd = BigDecimal("2"),
			),
			GetTokenPriceChartResponse(
				timestamp = Instant.parse("2025-06-17T12:00:01.900Z"),
				priceUsd = BigDecimal("5"),
			),
		)

		val result = queryBus(
			GetBotMarketPositionPriceChartQuery(
				userId = 1.toUUID(),
				botId = 10.toUUID(),
				botMarketPositionId = 1000.toUUID(),
			),
		)

		result shouldContainExactly listOf(
			GetBotMarketPositionPriceChartQuery.Result(
				timestamp = Instant.parse("2025-06-17T11:55:40Z"),
				openUsd = BigDecimal("4"),
				highUsd = BigDecimal("4"),
				lowUsd = BigDecimal("4"),
				closeUsd = BigDecimal("0"),
			),
			GetBotMarketPositionPriceChartQuery.Result(
				timestamp = Instant.parse("2025-06-17T12:00:00Z"),
				openUsd = BigDecimal("0"),
				highUsd = BigDecimal("0"),
				lowUsd = BigDecimal("0"),
				closeUsd = BigDecimal("1"),
			),
			GetBotMarketPositionPriceChartQuery.Result(
				timestamp = Instant.parse("2025-06-17T12:00:01Z"),
				openUsd = BigDecimal("1"),
				highUsd = BigDecimal("10"),
				lowUsd = BigDecimal("1"),
				closeUsd = BigDecimal("5"),
			),
		)
	}
}
