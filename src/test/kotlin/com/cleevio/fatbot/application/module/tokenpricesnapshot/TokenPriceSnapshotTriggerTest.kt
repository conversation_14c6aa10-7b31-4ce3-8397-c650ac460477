package com.cleevio.fatbot.application.module.tokenpricesnapshot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.module.tokenpricesnapshot.scheduled.TokenPriceSnapshotTrigger
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.tokenprice.TokenPriceRepository
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant

@Suppress("ktlint:standard:max-line-length")
class TokenPriceSnapshotTriggerTest(
	@Autowired private val underTest: TokenPriceSnapshotTrigger,
	@Autowired private val tokenPriceRepository: TokenPriceRepository,
) : IntegrationTest() {

	@Test
	fun `should create token price snapshot`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// an old snapshot to be deleted
		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = "aaa".toAddress(),
			chain = Chain.EVM_MAINNET,
			priceWei = BigInteger.ONE,
			exchangeRateUsd = BigDecimal.ONE,
			validAt = Instant.parse("2024-08-28T10:00:00Z"),
		)

		integrationTestHelper.getEvmTokenInfo(chain = Chain.EVM_MAINNET, tokenAddress = "aaa".toAddress())
		integrationTestHelper.getEvmTokenInfo(chain = Chain.EVM_MAINNET, tokenAddress = "bbb".toAddress())
		integrationTestHelper.getEvmTokenInfo(chain = Chain.EVM_BASE, tokenAddress = "bbb".toAddress())

		// and mock to get token prices

		// on chain 1
		every {
			readOnlyTransactionManager.sendCall(
				"0xBfEa493c17B9Af8Aca1aFbb20D8400236D2dB794",
				// encoded input of ["0xaaa0000000000000000000000000000000000000","******************************************"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000002000000000000000000000000aaa0000000000000000000000000000000000000000000000000000000000000bbb0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000a0" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"000000000000000000000000bbb0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"0000000000000000000000000000000000000000000000000DE0B6B3A7640000" + // 1 ETH
			"000000000000000000000000000000000000000000000000016345785D8A0000" // 0.1ETH

		// on chain 8453
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000001000000000000000000000000bbb0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000bbb0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000001BC16D674EC80000" // 2 ETH

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns "3000".toBigDecimal()

		underTest.trigger()

		val tokenPriceSnapshots = tokenPriceRepository.findAll()
		tokenPriceSnapshots.size shouldBe 3
	}
}
