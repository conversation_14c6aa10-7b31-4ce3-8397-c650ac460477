package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.user.command.SignUpFirebaseUserCommand
import com.cleevio.fatbot.application.module.user.exception.ReferralCodeNotFoundException
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.user.FirebaseUserRepository
import com.cleevio.fatbot.domain.user.Language
import com.cleevio.fatbot.domain.userstatistics.UserStatisticsRepository
import com.cleevio.fatbot.domain.wallet.WalletRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class SignUpFirebaseUserCommandHandlerTest(
	@Autowired private val firebaseUserRepository: FirebaseUserRepository,
	@Autowired private val walletRepository: WalletRepository,
	@Autowired private val userStatisticsRepository: UserStatisticsRepository,
	@Autowired private val commandBus: CommandBus,
) : IntegrationTest() {

	@Test
	fun `should create new firebase user and immediately create wallets and selected chains`() {
		firebaseUserRepository.findAll().size shouldBe 0

		commandBus(
			SignUpFirebaseUserCommand(
				email = "<EMAIL>",
				referralCode = null,
				language = Language.ENGLISH,
			),
		)

		val firebaseUsers = firebaseUserRepository.findAll()
		firebaseUsers.size shouldBe 1

		firebaseUsers.first().run {
			email shouldBe "<EMAIL>"
			version shouldBe 0
			selectedChains shouldContainExactlyInAnyOrder setOf(
				Chain.EVM_MAINNET,
				Chain.EVM_BASE,
				Chain.EVM_BSC,
				Chain.EVM_ARBITRUM_ONE,
				Chain.SOLANA,
			)
			language shouldBe Language.ENGLISH
		}

		val wallets = walletRepository.findAll()

		wallets.all { it.userId == firebaseUsers.first().id }
		wallets.map { it.chain } shouldContainExactlyInAnyOrder listOf(
			Chain.SOLANA,
			Chain.EVM_BASE,
			Chain.EVM_MAINNET,
			Chain.EVM_BSC,
			Chain.EVM_ARBITRUM_ONE,
		)
		wallets.map { it.customName } shouldContainExactlyInAnyOrder listOf(
			"ETH wallet 1",
			"BASE wallet 1",
			"BSC wallet 1",
			"SOL wallet 1",
			"ARB wallet 1",
		)

		val userStatistics = userStatisticsRepository.findAll()
		userStatistics.size shouldBe 1
		userStatistics.first().run {
			this.userId shouldBe firebaseUsers.first().id
		}
	}

	@Test
	fun `should assign correct referrer to new user`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID(), referralCode = "cleevio_x")

		commandBus(
			SignUpFirebaseUserCommand(
				email = "<EMAIL>",
				referralCode = "cleevio_x",
				language = Language.ENGLISH,
			),
		)

		val newUser = firebaseUserRepository.findByEmail("<EMAIL>")
		newUser shouldNotBe null
		newUser?.referredByUserId shouldBe 1.toUUID()
	}

	@Test
	fun `should throw error when signing up with invalid referral token`() {
		firebaseUserRepository.findAll().size shouldBe 0

		shouldThrowExactly<ReferralCodeNotFoundException> {
			commandBus(
				SignUpFirebaseUserCommand(
					email = "<EMAIL>",
					referralCode = "non_existing_code",
					language = Language.ENGLISH,
				),
			)
		}
	}

	@Test
	fun `should not create multiple users when signing up user with email multiple times`() {
		firebaseUserRepository.findAll().size shouldBe 0

		commandBus(SignUpFirebaseUserCommand(email = "<EMAIL>", referralCode = null, language = Language.ENGLISH))
		commandBus(SignUpFirebaseUserCommand(email = "<EMAIL>", referralCode = null, language = Language.ENGLISH))
		commandBus(SignUpFirebaseUserCommand(email = "<EMAIL>", referralCode = null, language = Language.ENGLISH))

		val firebaseUsers = firebaseUserRepository.findAll()
		firebaseUsers.size shouldBe 1
	}
}
