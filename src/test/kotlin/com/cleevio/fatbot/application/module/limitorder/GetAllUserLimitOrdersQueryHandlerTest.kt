package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.limitorder.query.GetAllUserLimitOrdersQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.shouldBeEqualComparingTo
import com.cleevio.fatbot.toBigInteger
import com.cleevio.fatbot.toSolanaAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetAllUserLimitOrdersQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return all limit orders for user`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getFirebaseUser(id = 3.toUUID(), email = "<EMAIL>")
		integrationTestHelper.getWallet(id = 4.toUUID(), userId = 3.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			id = 10.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = "a".toSolanaAddress(),
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getLimitOrder(
			id = 5.toUUID(),
			userId = 1.toUUID(),
			walletId = 2.toUUID(),
			tokenAddress = "a".toSolanaAddress(),
			limitPrice = 0.1e9.toBigInteger(),
			initialAmount = 1e9.toBigInteger(),
			type = LimitOrderType.BUY,
			chain = Chain.SOLANA,
		)

		integrationTestHelper.getLimitOrder(
			id = 6.toUUID(),
			userId = 1.toUUID(),
			walletId = 2.toUUID(),
			tokenAddress = "a".toSolanaAddress(),
			limitPrice = 0.2e9.toBigInteger(),
			initialAmount = 2e6.toBigInteger(),
			type = LimitOrderType.SELL,
			chain = Chain.SOLANA,
			beforeSave = {
				lock()
				fill(amount = 0.5e6.toBigInteger())
			},
		)

		integrationTestHelper.getLimitOrder(
			id = 7.toUUID(),
			userId = 3.toUUID(),
			walletId = 4.toUUID(),
			tokenAddress = "a".toSolanaAddress(),
			limitPrice = 0.3e9.toBigInteger(),
			initialAmount = 3e9.toBigInteger(),
			type = LimitOrderType.BUY,
			chain = Chain.SOLANA,
			beforeSave = {
				lock()
				fill(amount = 1.5e9.toBigInteger())
			},
		)

		integrationTestHelper.getLimitOrder(
			id = 8.toUUID(),
			userId = 3.toUUID(),
			walletId = 4.toUUID(),
			tokenAddress = "a".toSolanaAddress(),
			limitPrice = 0.4e9.toBigInteger(),
			initialAmount = 4e6.toBigInteger(),
			type = LimitOrderType.SELL,
			chain = Chain.SOLANA,
			beforeSave = { lock() },
		)

		// when
		val firstUserResults = queryBus(GetAllUserLimitOrdersQuery(userId = 1.toUUID()))
		val secondUserResults = queryBus(GetAllUserLimitOrdersQuery(userId = 3.toUUID()))

		// then
		firstUserResults shouldHaveSize 2
		secondUserResults shouldHaveSize 2

		firstUserResults.single { it.id == 5.toUUID() }.run {
			walletId shouldBe 2.toUUID()
			tokenAddress shouldBe "a".toSolanaAddress()
			chain shouldBe Chain.SOLANA
			type shouldBe LimitOrderType.BUY
			isLocked shouldBe false
			limitPrice shouldBeEqualComparingTo 0.1.toBigDecimal()
			filledAmount shouldBeEqualComparingTo 0.toBigDecimal()
			remainingAmount shouldBeEqualComparingTo 1.toBigDecimal()
		}

		firstUserResults.single { it.id == 6.toUUID() }.run {
			walletId shouldBe 2.toUUID()
			tokenAddress shouldBe "a".toSolanaAddress()
			chain shouldBe Chain.SOLANA
			type shouldBe LimitOrderType.SELL
			isLocked shouldBe false
			limitPrice shouldBeEqualComparingTo 0.2.toBigDecimal()
			filledAmount shouldBeEqualComparingTo 0.5.toBigDecimal()
			remainingAmount shouldBeEqualComparingTo 1.5.toBigDecimal()
		}

		secondUserResults.single { it.id == 7.toUUID() }.run {
			walletId shouldBe 4.toUUID()
			tokenAddress shouldBe "a".toSolanaAddress()
			chain shouldBe Chain.SOLANA
			type shouldBe LimitOrderType.BUY
			isLocked shouldBe false
			limitPrice shouldBeEqualComparingTo 0.3.toBigDecimal()
			filledAmount shouldBeEqualComparingTo 1.5.toBigDecimal()
			remainingAmount shouldBeEqualComparingTo 1.5.toBigDecimal()
		}

		secondUserResults.single { it.id == 8.toUUID() }.run {
			walletId shouldBe 4.toUUID()
			tokenAddress shouldBe "a".toSolanaAddress()
			chain shouldBe Chain.SOLANA
			type shouldBe LimitOrderType.SELL
			isLocked shouldBe true
			limitPrice shouldBeEqualComparingTo 0.4.toBigDecimal()
			filledAmount shouldBeEqualComparingTo 0.toBigDecimal()
			remainingAmount shouldBeEqualComparingTo 4.toBigDecimal()
		}
	}
}
