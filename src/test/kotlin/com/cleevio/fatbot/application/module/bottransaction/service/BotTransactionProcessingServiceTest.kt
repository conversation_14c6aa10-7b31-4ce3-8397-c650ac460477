package com.cleevio.fatbot.application.module.bottransaction.service

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.crypto.asTxHash
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.bottransaction.finder.BotTransactionFinderService
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransferCurrencyTransactionSuccess
import com.cleevio.fatbot.domain.botwallet.BotWalletRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.nondeterministic.eventually
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.every
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.math.BigInteger
import kotlin.time.Duration.Companion.seconds

class BotTransactionProcessingServiceTest(
	@Autowired private val botTransactionProcessingService: BotTransactionProcessingService,
	@Autowired private val botWalletRepository: BotWalletRepository,
	@Autowired private val botTransactionFinderService: BotTransactionFinderService,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("100.0")
	}

	@Test
	fun `should increase acquisition value for bot wallet when processing DEPOSIT transaction`() {
		// every
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			name = "testBot",
			avatarFileId = 2.toUUID(),
		)

		integrationTestHelper.getBotWallet(
			id = 11.toUUID(),
			botId = 10.toUUID(),
			address = AddressWrapper("9rP1kBpqtSp9d78j9UMtpyEgMbnySVhrJMgJRabxZDtC"),
		)

		integrationTestHelper.getBotTransaction(
			id = 100.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.DEPOSIT,
			status = TransactionStatus.PENDING,
			txHash = "depositTxHash".asTxHash(),
			amountIn = BigInteger("1000000000"),
			exchangeRateUsd = BigDecimal.ZERO,
		)

		every { getTransactionResultsSVM.invoke(setOf("depositTxHash".asTxHash())) } returns mapOf(
			"depositTxHash".asTxHash() to TransferCurrencyTransactionSuccess(
				value = BigInteger("1000000000"),
				fee = BigInteger("5000"),
			),
		)

		val initialWallet = botWalletRepository.findByIdOrNull(11.toUUID())!!
		val initialAcquisitionValue = initialWallet.acquisitionValueUsd
		initialAcquisitionValue shouldBeEqualComparingTo BigDecimal.ZERO

		// when
		botTransactionProcessingService.tryStartProcessor()

		// then
		runBlocking {
			eventually(1.seconds) {
				val updatedWallet = botWalletRepository.findByIdOrNull(11.toUUID())!!

				updatedWallet.acquisitionValueUsd shouldBeEqualComparingTo BigDecimal("100.0")

				val updatedTransaction = botTransactionFinderService.findById(100.toUUID())!!
				updatedTransaction.status shouldBe TransactionStatus.SUCCESS
			}
		}
	}
}
