package com.cleevio.fatbot.application.module.matchmaking

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.adapter.out.evm.GetTokenPricesEVM
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.bottransaction.service.BotTransactionProcessingService
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.bot.BotRepository
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionRepository
import com.cleevio.fatbot.domain.bottransaction.BotTransactionRepository
import com.cleevio.fatbot.domain.botwallet.BotWalletRepository
import com.cleevio.fatbot.getPrivatePropertyOfProxy
import com.cleevio.fatbot.setAndReturnPrivateProperty
import com.cleevio.fatbot.toUUID
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.github.benmanes.caffeine.cache.AsyncCache
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.types.ConfirmedTransaction
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.util.Optional
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

class BotTransactionStatusProcessingTriggerTest(
	@Autowired private val botTransactionProcessingService: BotTransactionProcessingService,
	@Autowired private val botRepository: BotRepository,
	@Autowired private val botWalletRepository: BotWalletRepository,
	@Autowired private val botMarketPositionRepository: BotMarketPositionRepository,
	@Autowired private val botTransactionRepository: BotTransactionRepository,
	@Autowired private val integrationTestClock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `bot should recover from buy transaction not landing`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())

		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(0.1),
			buyFrequency = 1,
			beforeSave = { decreaseRemainingBuys() }, // simulating making the buy
		)

		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(1000000000)) },
		)

		// open position
		val marketPosition = integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setFrozenAmount(
					buyFreezeAmount = BaseAmount(BigInteger("50000")),
					sellFreezeAmount = BaseAmount(BigInteger("16000")),
				)
			},
		)

		marketPosition.buyAmountFrozen shouldBe BigInteger("50000")
		marketPosition.feeToSellFrozen shouldBe BigInteger("16000")

		val botTx = integrationTestHelper.getBotTransaction(
			id = 10_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.BUY,
			status = TransactionStatus.PENDING,
			txHash = TxHash("buyTxHash"),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
		)

		every { rpcClient.api.getTransactions(listOf("buyTxHash"), any()) } returns listOf(null)
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "101.01".toBigDecimal()

		// when
		repeat(11) { botTransactionProcessingService.process(listOf(botTx.id)) }

		// then
		with(botTransactionRepository.findAll().first()) {
			status shouldBe TransactionStatus.NOT_LANDED
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.OPEN_NOT_LANDED
		}

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(1000066000)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L // on failed attempts we do not refund buy frequency yet
		}
	}

	@Test
	fun `bot should recover from buy transaction failing`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())

		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(0.1),
			buyFrequency = 1,
			beforeSave = { decreaseRemainingBuys() }, // simulating making the buy
		)

		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(1000000000)) },
		)

		// open position
		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setFrozenAmount(
					buyFreezeAmount = BaseAmount(BigInteger("50000")),
					sellFreezeAmount = BaseAmount(BigInteger("16000")),
				)
			},
		)

		val botTx = integrationTestHelper.getBotTransaction(
			id = 10_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.BUY,
			status = TransactionStatus.PENDING,
			txHash = TxHash("buyTxHash"),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
		)

		every { rpcClient.api.getTransactions(listOf("buyTxHash"), any()) } returns listOf(
			mockk {
				every { transaction.signatures } returns listOf("buyTxHash")
				every { meta.err } returns "there was an error"
				every { meta.logMessages } returns emptyList()
				every { meta.preBalances[0] } returns 10_000
				every { meta.postBalances[0] } returns 4500
			},
		)
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "101.01".toBigDecimal()

		// when
		botTransactionProcessingService.process(listOf(botTx.id))

		// then
		with(botTransactionRepository.findAll().first()) {
			status shouldBe TransactionStatus.FAILED
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.OPEN_FAILED
		}

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(**********)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L // on failed attempts we do not refund buy frequency yet
		}
	}

	@Test
	fun `bot should recover from sell transaction not landing`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())

		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(0.1),
			buyFrequency = 1,
			beforeSave = { decreaseRemainingBuys() }, // simulating making the buy
		)

		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(1000000000)) },
		)

		// open position
		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setFrozenAmount(
					buyFreezeAmount = BaseAmount(BigInteger("50000")),
					sellFreezeAmount = BaseAmount(BigInteger("16000")),
				)
				setBuyDataFromChain(
					amountOut = BigInteger("1000000"), // 1 token (6 decimals)
					amountIn = BigInteger("40"),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("200"),
				)
				closePositionWithSellMatch(
					assumedSellPrice = BigDecimal("0.00000005"),
				)
			},
		)

		val botTx = integrationTestHelper.getBotTransaction(
			id = 10_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.SELL,
			status = TransactionStatus.PENDING,
			txHash = TxHash("sellTxHash"),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
		)

		every { rpcClient.api.getTransactions(listOf("sellTxHash"), any()) } returns listOf(null)
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "101.01".toBigDecimal()

		integrationTestClock.setTo(Instant.now())

		// when in grace period
		repeat(15) { botTransactionProcessingService.process(listOf(botTx.id)) }

		// then
		with(botTransactionRepository.findAll().first()) {
			status shouldBe TransactionStatus.PENDING
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.PENDING_CLOSED
		}

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(1000000000)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L
		}

		// when outside of grace period
		integrationTestClock.setTo(Instant.now().plus(20.seconds.toJavaDuration()))

		botTransactionProcessingService.process(listOf(botTx.id))

		// then
		with(botTransactionRepository.findAll().first()) {
			status shouldBe TransactionStatus.NOT_LANDED
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.OPENED
		}

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(1000016000)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L
		}
	}

	@Test
	fun `bot should recover from sell transaction not landing when buy did not land`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())

		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(0.1),
			buyFrequency = 1,
			beforeSave = { decreaseRemainingBuys() }, // simulating making the buy
		)

		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(1000000000)) },
		)

		// open position
		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setFrozenAmount(
					buyFreezeAmount = BaseAmount(BigInteger("50000")),
					sellFreezeAmount = BaseAmount(BigInteger("16000")),
				)
				markAsOpenNotLanded()
			},
		)

		val botTx = integrationTestHelper.getBotTransaction(
			id = 10_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.SELL,
			status = TransactionStatus.PENDING,
			txHash = TxHash("sellTxHash"),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
		)

		every { rpcClient.api.getTransactions(listOf("sellTxHash"), any()) } returns listOf(null)
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "101.01".toBigDecimal()

		// when
		repeat(11) { botTransactionProcessingService.process(listOf(botTx.id)) }

		// then
		with(botTransactionRepository.findAll().first()) {
			status shouldBe TransactionStatus.NOT_LANDED
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.OPEN_NOT_LANDED
		}

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(1000016000)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L
		}
	}

	@Test
	fun `bot should recover from sell transaction not landing when buy failed`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())

		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(0.1),
			buyFrequency = 1,
			beforeSave = { decreaseRemainingBuys() }, // simulating making the buy
		)

		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(1000000000)) },
		)

		// open position
		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setFrozenAmount(
					buyFreezeAmount = BaseAmount(BigInteger("50000")),
					sellFreezeAmount = BaseAmount(BigInteger("16000")),
				)
				markAsOpenFailed()
			},
		)

		val botTx = integrationTestHelper.getBotTransaction(
			id = 10_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.SELL,
			status = TransactionStatus.PENDING,
			txHash = TxHash("sellTxHash"),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
		)

		every { rpcClient.api.getTransactions(listOf("sellTxHash"), any()) } returns listOf(null)
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "101.01".toBigDecimal()

		integrationTestClock.setTo(Instant.now().plus(20.seconds.toJavaDuration()))

		// when
		repeat(11) { botTransactionProcessingService.process(listOf(botTx.id)) }

		// then
		with(botTransactionRepository.findAll().first()) {
			status shouldBe TransactionStatus.NOT_LANDED
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.OPEN_FAILED
		}

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(1000016000)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L
		}
	}

	@Test
	fun `bot should recover from sell transaction failing`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())

		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(0.1),
			buyFrequency = 1,
			beforeSave = { decreaseRemainingBuys() }, // simulating making the buy
		)

		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(1000000000)) },
		)

		// open position
		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setFrozenAmount(
					buyFreezeAmount = BaseAmount(BigInteger("50000")),
					sellFreezeAmount = BaseAmount(BigInteger("16000")),
				)
				setBuyDataFromChain(
					amountOut = BigInteger("1000000"), // 1 token (6 decimals)
					amountIn = BigInteger("40"),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("200"),
				)
				closePositionWithSellMatch(
					assumedSellPrice = BigDecimal("0.00000005"),
				)
			},
		)

		val botTx = integrationTestHelper.getBotTransaction(
			id = 10_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.SELL,
			status = TransactionStatus.PENDING,
			txHash = TxHash("sellTxHash"),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
		)

		every { rpcClient.api.getTransactions(listOf("sellTxHash"), any()) } returns listOf(
			mockk {
				every { transaction.signatures } returns listOf("sellTxHash")
				every { meta.err } returns "there was an error"
				every { meta.logMessages } returns emptyList()
				every { meta.preBalances[0] } returns 10_000
				every { meta.postBalances[0] } returns 4500
			},
		)

		every { rpcClient.api.getMultipleAccountsOptional(any(), any()) } returns listOf()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "101.01".toBigDecimal()

		// when
		botTransactionProcessingService.process(listOf(botTx.id))

		// then
		with(botTransactionRepository.findAll().first()) {
			status shouldBe TransactionStatus.FAILED
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.UNKNOWN_ERROR
		}

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(**********)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L
		}
	}

	@Test
	fun `bot should handle buy and sell transaction confirmed in reversed order due to buy not landing`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())

		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(0.1),
			buyFrequency = 1,
			beforeSave = { decreaseRemainingBuys() }, // simulating making the buy
		)

		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(1000000000)) },
		)

		// open position
		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setFrozenAmount(
					buyFreezeAmount = BaseAmount(BigInteger("50000")),
					sellFreezeAmount = BaseAmount(BigInteger("16000")),
				)
				closePositionWithSellMatch(
					assumedSellPrice = BigDecimal("0.00000005"),
				)
			},
		)

		val botTx1 = integrationTestHelper.getBotTransaction(
			id = 10_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.BUY,
			status = TransactionStatus.PENDING,
			txHash = TxHash("buyTxHash"),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
		)
		val botTx2 = integrationTestHelper.getBotTransaction(
			id = 10_001.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.SELL,
			status = TransactionStatus.PENDING,
			txHash = TxHash("sellTxHash"),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
		)

		every { rpcClient.api.getTransactions(listOf("buyTxHash"), any()) } returns listOf(
			null,
		)
		every { rpcClient.api.getTransactions(listOf("buyTxHash", "sellTxHash"), any()) } returns listOf(
			null,
			mockk {
				every { transaction.signatures } returns listOf("sellTxHash")
				every { meta.err } returns "there was an error"
				every { meta.logMessages } returns emptyList()
				every { meta.preBalances[0] } returns 10_000
				every { meta.postBalances[0] } returns 4500
			},
		)

		every { rpcClient.api.getMultipleAccountsOptional(any(), any()) } returns listOf()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "101.01".toBigDecimal()

		// when on the first run sell transaction will be handled, on 6th pending buy transaction will be handled
		repeat(11) { botTransactionProcessingService.process(listOf(botTx1.id, botTx2.id)) }

		// then
		val botTransactions = botTransactionRepository.findAll()
		with(botTransactions.first { it.type == BotTransactionType.BUY }) {
			status shouldBe TransactionStatus.NOT_LANDED
		}
		with(botTransactions.first { it.type == BotTransactionType.SELL }) {
			status shouldBe TransactionStatus.FAILED
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.OPEN_NOT_LANDED
		}

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(**********)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L // on failed attempts we do not refund buy frequency yet
		}
	}

	@Test
	fun `bot should recover from sell transaction failing, because of zeroBaseAmount error when buy not landed yet`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 99999.toUUID())

		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(0.1),
			buyFrequency = 1,
			beforeSave = { decreaseRemainingBuys() }, // simulating making the buy
		)

		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(1000000000)) },
		)

		// open position
		integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
			assumedBuyPrice = BigDecimal("0.00000004"), // 40 lamports
			blockSlot = 300,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("1000000"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("40"),
				)
				setFrozenAmount(
					buyFreezeAmount = BaseAmount(BigInteger("50000")),
					sellFreezeAmount = BaseAmount(BigInteger("16000")),
				)
				closePositionWithSellMatch(
					assumedSellPrice = BigDecimal("0.00000005"),
				)
			},
		)

		val botTx1 = integrationTestHelper.getBotTransaction(
			id = 10_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.BUY,
			status = TransactionStatus.PENDING,
			txHash = TxHash("buyTxHash"),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
		)

		val botTx2 = integrationTestHelper.getBotTransaction(
			id = 20_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.SELL,
			status = TransactionStatus.PENDING,
			txHash = TxHash("sellTxHash"),
			tokenAddress = AddressWrapper("BGp4e9ait1VTs3yAQA69D5Ys1CX6Rf2wpKBbtE8Hpump"),
		)

		every { rpcClient.api.getTransactions(listOf("buyTxHash", "sellTxHash"), any()) } returns listOf(
			null,
			mockk {
				every { transaction.signatures } returns listOf("sellTxHash")
				every { meta.err } returns "there was an error"
				every { meta.logMessages } returns listOf("xyz Error Number: 6001. Error Message: ZeroBaseAmount.")
				every { meta.preBalances[0] } returns 10_000
				every { meta.postBalances[0] } returns 4500
			},
		)

		every { rpcClient.api.getMultipleAccountsOptional(any(), any()) } returns listOf()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "101.01".toBigDecimal()

		// when
		botTransactionProcessingService.process(listOf(botTx1.id, botTx2.id))

		// then
		val botTransactions = botTransactionRepository.findAll()
		with(botTransactions.first { it.type == BotTransactionType.BUY }) {
			status shouldBe TransactionStatus.PENDING
		}
		with(botTransactions.first { it.type == BotTransactionType.SELL }) {
			status shouldBe TransactionStatus.FAILED
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.OPENED
		}

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(*********)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L // on failed attempts we do not refund buy frequency yet
		}
	}

	@Test
	fun `bot should have proper balance after buy sell transactions - real world example`() {
		// example based on trade with these two transactions
		// https://solscan.io/tx/2eYN6s3tzoiUXCqBB1q4MfBFPx66F9iryBwPTwCY4zCSmpY66mhz7rLngnj4wwAJkKHZewaNeKQTBd5mubtftu13
		// https://solscan.io/tx/2X77qXzWm3zvLSmXMDkwvWDPTshEWjkpezwfjempH5V3yA8rzZb9ZboA2LyLrwWmGb6GfTXUszMLg1hzNxQL6KNe

		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(id = 1.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getFattyCard(id = 1.toUUID(), avatarFileId = integrationTestHelper.getFile().id)
		integrationTestHelper.getFile(id = 99999.toUUID())

		integrationTestHelper.getBotTokenInfo(
			id = 1.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("********************************************"),
			tokenDecimals = BigInteger.valueOf(6),
			isToken2022 = false,
		)

		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(0.1),
			buyFrequency = 1,
			beforeSave = { decreaseRemainingBuys() }, // simulating making the buy
		)

		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(3986161)) },
		)

		// open position
		val position = integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("********************************************"),
			profitTargetFraction = BigDecimal("0.4"),
			stopLossFraction = BigDecimal("0.2"),
			assumedBuyPrice = BigDecimal("0.00000005305269092004894770286573325844922"),
			blockSlot = 338398909,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("18475495658"), // 1 token (6 decimals)
					amountOfBaseCurrencyPaid = BigInteger("1000000"),
				)
				setFrozenAmount(
					buyFreezeAmount = BaseAmount(BigInteger("3079280")),
					sellFreezeAmount = BaseAmount(BigInteger("16000")),
				)
				closePositionWithSellMatch(
					assumedSellPrice = BigDecimal("0.00000003512903370078755348585238181599517"),
				)
			},
		)

		// decrease the wallet amount by the frozen amount
		botWalletRepository.save(
			botWalletRepository.findAll().first().also {
				it.decreaseBalance(position.balanceFrozen)
			},
		)

		val botTx1 = integrationTestHelper.getBotTransaction(
			id = 10_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.BUY,
			status = TransactionStatus.PENDING,
			txHash = TxHash("2eYN6s3tzoiUXCqBB1q4MfBFPx66F9iryBwPTwCY4zCSmpY66mhz7rLngnj4wwAJkKHZewaNeKQTBd5mubtftu13"),
			tokenAddress = AddressWrapper("********************************************"),
		)

		val botTx2 = integrationTestHelper.getBotTransaction(
			id = 20_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.SELL,
			status = TransactionStatus.PENDING,
			txHash = TxHash("2X77qXzWm3zvLSmXMDkwvWDPTshEWjkpezwfjempH5V3yA8rzZb9ZboA2LyLrwWmGb6GfTXUszMLg1hzNxQL6KNe"),
			tokenAddress = AddressWrapper("********************************************"),
		)

		every {
			rpcClient.api.getTransactions(
				listOf(
					"2eYN6s3tzoiUXCqBB1q4MfBFPx66F9iryBwPTwCY4zCSmpY66mhz7rLngnj4wwAJkKHZewaNeKQTBd5mubtftu13",
					"2X77qXzWm3zvLSmXMDkwvWDPTshEWjkpezwfjempH5V3yA8rzZb9ZboA2LyLrwWmGb6GfTXUszMLg1hzNxQL6KNe",
				),
				any(),
			)
		} returns listOf(
			jacksonObjectMapper().readValue(CONFIRMED_BUY_TX_FIXTURE, ConfirmedTransaction::class.java),
			null,
		)

		every {
			rpcClient.api.getTransactions(
				listOf("2X77qXzWm3zvLSmXMDkwvWDPTshEWjkpezwfjempH5V3yA8rzZb9ZboA2LyLrwWmGb6GfTXUszMLg1hzNxQL6KNe"),
				any(),
			)
		} returns listOf(
			jacksonObjectMapper().readValue(CONFIRMED_SELL_TX_FIXTURE, ConfirmedTransaction::class.java),
		)

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.put(
				ChainAddress(
					chain = Chain.SOLANA,
					address = AddressWrapper("********************************************"),
				),
				BaseAmount(BigInteger("100000")), // just something random for now...
			)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("150.00")

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(890881) // exact amount needed for minimal rent + 1 lamport
		}

		// when first trigger confirms BUY tx
		botTransactionProcessingService.process(listOf(botTx1.id, botTx2.id))

		// then
		val botTransactions = botTransactionRepository.findAll()
		with(botTransactions.first { it.type == BotTransactionType.BUY }) {
			status shouldBe TransactionStatus.SUCCESS
		}
		with(botTransactions.first { it.type == BotTransactionType.SELL }) {
			status shouldBe TransactionStatus.PENDING
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.PENDING_CLOSED
		}

		with(botWalletRepository.findAll().first()) {
			// real balance on chain after tx is 924381 - 16000 for frozen sell fees
			balance shouldBe BigInteger.valueOf(908381)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L // on failed attempts we do not refund buy frequency yet
		}

		// and then when second trigger confirms SELL tx
		botTransactionProcessingService.process(listOf(botTx1.id, botTx2.id))

		// then
		val botTransactionsAfter = botTransactionRepository.findAll()
		with(botTransactionsAfter.first { it.type == BotTransactionType.BUY }) {
			status shouldBe TransactionStatus.SUCCESS
		}
		with(botTransactionsAfter.first { it.type == BotTransactionType.SELL }) {
			status shouldBe TransactionStatus.SUCCESS
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.CONFIRMED_CLOSED
		}

		with(botWalletRepository.findAll().first()) {
			// balance in DB + frozen sell fees + gained from selling and rent
			balance shouldBe BigInteger.valueOf(908381 + 16000 + 2664877) // 3589258
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L // on failed attempts we do not refund buy frequency yet
		}
	}

	@Test
	fun `bot should have proper balance after buy not landing but sell is executed - real world example`() {
		// example based on trade with this transaction
		// https://solscan.io/tx/k4eag8zcbkuZe188xqiPBrv1WcSduGgTvU7Zd4HzoAqg7nVvo76jMD2kRoM7amfTU2rJmi8YY2ZV4FvS8t2KPDr

		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(id = 1.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getFattyCard(id = 1.toUUID(), avatarFileId = integrationTestHelper.getFile().id)
		integrationTestHelper.getFile(id = 99999.toUUID())

		integrationTestHelper.getBotTokenInfo(
			id = 1.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("81DJ9BEvtEwwREWxEgXyNQUbxknnJtMzat353KWJpump"),
			tokenDecimals = BigInteger.valueOf(6),
			isToken2022 = false,
		)

		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(0.1),
			buyFrequency = 1,
			beforeSave = { decreaseRemainingBuys() }, // simulating making the buy
		)

		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(51785182)) },
		)

		// open position
		val position = integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("81DJ9BEvtEwwREWxEgXyNQUbxknnJtMzat353KWJpump"),
			profitTargetFraction = BigDecimal("0.4"),
			stopLossFraction = BigDecimal("0.2"),
			assumedBuyPrice = BigDecimal("0.0000000795455971580234422874881823801187"),
			blockSlot = 338886901,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("25141818359"),
					amountOfBaseCurrencyPaid = BigInteger("2000000"),
				)
				setFrozenAmount(
					buyFreezeAmount = BaseAmount(BigInteger("4079280")),
					sellFreezeAmount = BaseAmount(BigInteger("16000")),
				)
				closePositionWithSellMatch(
					assumedSellPrice = BigDecimal("0.0000001147364207832936215366400284634522"),
				)
			},
		)

		// decrease the wallet amount by the frozen amount
		botWalletRepository.save(
			botWalletRepository.findAll().first().also {
				it.decreaseBalance(position.balanceFrozen)
			},
		)

		val botTx1 = integrationTestHelper.getBotTransaction(
			id = 10_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.BUY,
			status = TransactionStatus.PENDING,
			txHash = TxHash("5Pji2MUthURhtfD53vefPZ5mxztuWBvAKCPwj5KoQwF9ATg2iMJPZzDGFLJh7c4xP1JJaVor9yZH5mGRmP18CRgf"),
			tokenAddress = AddressWrapper("81DJ9BEvtEwwREWxEgXyNQUbxknnJtMzat353KWJpump"),
		)

		val botTx2 = integrationTestHelper.getBotTransaction(
			id = 20_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.SELL,
			status = TransactionStatus.PENDING,
			txHash = TxHash("k4eag8zcbkuZe188xqiPBrv1WcSduGgTvU7Zd4HzoAqg7nVvo76jMD2kRoM7amfTU2rJmi8YY2ZV4FvS8t2KPDr"),
			tokenAddress = AddressWrapper("81DJ9BEvtEwwREWxEgXyNQUbxknnJtMzat353KWJpump"),
		)

		every {
			rpcClient.api.getTransactions(
				listOf(
					"5Pji2MUthURhtfD53vefPZ5mxztuWBvAKCPwj5KoQwF9ATg2iMJPZzDGFLJh7c4xP1JJaVor9yZH5mGRmP18CRgf",
					"k4eag8zcbkuZe188xqiPBrv1WcSduGgTvU7Zd4HzoAqg7nVvo76jMD2kRoM7amfTU2rJmi8YY2ZV4FvS8t2KPDr",
				),
				any(),
			)
		} returns listOf(
			null,
			jacksonObjectMapper().readValue(SELL_ZERO_AMOUNT_TX_FIXTURE, ConfirmedTransaction::class.java),
		)

		every {
			rpcClient.api.getTransactions(
				listOf("5Pji2MUthURhtfD53vefPZ5mxztuWBvAKCPwj5KoQwF9ATg2iMJPZzDGFLJh7c4xP1JJaVor9yZH5mGRmP18CRgf"),
				any(),
			)
		} returns listOf(
			null,
		)

		every {
			rpcClient.api.getMultipleAccountsOptional(
				listOf(
					PublicKey("EFw5xoPBSHzN1RnEJZigQx8L8vMNVYzSsPuJntaiv3T1"),
				),
				any(),
			)
		} returns listOf(Optional.empty()) // no tokens

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.put(
				ChainAddress(
					chain = Chain.SOLANA,
					address = AddressWrapper("81DJ9BEvtEwwREWxEgXyNQUbxknnJtMzat353KWJpump"),
				),
				BaseAmount(BigInteger("100000")), // just something random for now...
			)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("150.00")

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(********)
		}

		// when first trigger confirms failed SELL tx
		botTransactionProcessingService.process(listOf(botTx1.id, botTx2.id))

		// then
		val botTransactions = botTransactionRepository.findAll()
		with(botTransactions.first { it.type == BotTransactionType.BUY }) {
			status shouldBe TransactionStatus.PENDING
		}
		with(botTransactions.first { it.type == BotTransactionType.SELL }) {
			status shouldBe TransactionStatus.FAILED
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.UNKNOWN_ERROR
		}

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(47695402)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L // on failed attempts we do not refund buy frequency yet
		}

		// and then when second trigger
		repeat(10) { botTransactionProcessingService.process(listOf(botTx1.id, botTx2.id)) }

		// then
		val botTransactionsAfter = botTransactionRepository.findAll()
		with(botTransactionsAfter.first { it.type == BotTransactionType.BUY }) {
			status shouldBe TransactionStatus.NOT_LANDED
		}
		with(botTransactionsAfter.first { it.type == BotTransactionType.SELL }) {
			status shouldBe TransactionStatus.FAILED
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.OPEN_NOT_LANDED
		}

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(51774682)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L // on failed attempts we do not refund buy frequency yet
		}
	}

	@Test
	fun `bot should have proper balance after buy fails - real world example`() {
		// example based on trade with this transaction
		// https://solscan.io/tx/45VcfYF6cGY1M3UJYoHN4i6Mszdk3PaGG5par7FdLUeGaE7fzJPVmUtUgx1NF6xwwfxs1tHxgqXQK5CKKSZK6TYw

		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(id = 1.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getFattyCard(id = 1.toUUID(), avatarFileId = integrationTestHelper.getFile().id)
		integrationTestHelper.getFile(id = 99999.toUUID())

		integrationTestHelper.getBotTokenInfo(
			id = 1.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("8kvvvG96urphBs3dc5ixrB5YLurRyD7RRhT94NrYpump"),
			tokenDecimals = BigInteger.valueOf(6),
			isToken2022 = false,
		)

		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 99999.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(0.1),
			buyFrequency = 1,
			beforeSave = { decreaseRemainingBuys() }, // simulating making the buy
		)

		integrationTestHelper.getBotWallet(
			id = 100.toUUID(),
			botId = 10.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(26731506)) },
		)

		// open position
		val position = integrationTestHelper.getBotMarketPosition(
			id = 1000.toUUID(),
			botWalletId = 100.toUUID(),
			tokenAddress = AddressWrapper("8kvvvG96urphBs3dc5ixrB5YLurRyD7RRhT94NrYpump"),
			profitTargetFraction = BigDecimal("0.4"),
			stopLossFraction = BigDecimal("0.2"),
			assumedBuyPrice = BigDecimal("0.0000000795455971580234422874881823801187"),
			blockSlot = 338886901,
			beforeSave = {
				newAssumedBuy(
					amountOfTokensReceived = BigInteger("32908573597"),
					amountOfBaseCurrencyPaid = BigInteger("1000000"),
				)
				setFrozenAmount(
					buyFreezeAmount = BaseAmount(BigInteger("3079280")),
					sellFreezeAmount = BaseAmount(BigInteger("16000")),
				)
			},
		)

		// decrease the wallet amount by the frozen amount
		botWalletRepository.save(
			botWalletRepository.findAll().first().also {
				it.decreaseBalance(position.balanceFrozen)
			},
		)

		val botTx = integrationTestHelper.getBotTransaction(
			id = 10_000.toUUID(),
			botWalletId = 100.toUUID(),
			type = BotTransactionType.BUY,
			status = TransactionStatus.PENDING,
			txHash = TxHash("45VcfYF6cGY1M3UJYoHN4i6Mszdk3PaGG5par7FdLUeGaE7fzJPVmUtUgx1NF6xwwfxs1tHxgqXQK5CKKSZK6TYw"),
			tokenAddress = AddressWrapper("8kvvvG96urphBs3dc5ixrB5YLurRyD7RRhT94NrYpump"),
		)

		every {
			rpcClient.api.getTransactions(
				listOf(
					"45VcfYF6cGY1M3UJYoHN4i6Mszdk3PaGG5par7FdLUeGaE7fzJPVmUtUgx1NF6xwwfxs1tHxgqXQK5CKKSZK6TYw",
				),
				any(),
			)
		} returns listOf(
			jacksonObjectMapper().readValue(BUY_FAILED_TX_FIXTURE, ConfirmedTransaction::class.java),
		)

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.put(
				ChainAddress(
					chain = Chain.SOLANA,
					address = AddressWrapper("81DJ9BEvtEwwREWxEgXyNQUbxknnJtMzat353KWJpump"),
				),
				BaseAmount(BigInteger("100000")), // just something random for now...
			)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("150.00")

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(23636226)
		}

		// when first trigger confirms failed BUY tx
		botTransactionProcessingService.process(listOf(botTx.id))

		// then
		val botTransactions = botTransactionRepository.findAll()
		with(botTransactions.first { it.type == BotTransactionType.BUY }) {
			status shouldBe TransactionStatus.FAILED
		}

		with(botMarketPositionRepository.findAll().first()) {
			state shouldBe BotMarketPositionState.OPEN_FAILED
		}

		with(botWalletRepository.findAll().first()) {
			balance shouldBe BigInteger.valueOf(26709006)
		}

		with(botRepository.findAll().first()) {
			remainingBuyFrequency shouldBe 0L // on failed attempts we do not refund buy frequency yet
		}
	}
}
