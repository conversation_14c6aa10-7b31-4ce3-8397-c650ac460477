package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.command.SaveTokenInfoCommand
import com.cleevio.fatbot.application.module.token.event.NewEvmTokenInfoCreatedEvent
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.token.EvmTokenInfoRepository
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.p2p.solanaj.core.PublicKey
import org.springframework.beans.factory.annotation.Autowired
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.toJavaDuration

class SaveEvmTokenInfoCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val tokenInfoRepository: EvmTokenInfoRepository,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		mockkObject(EtherscanConnector.Companion)
		// To save time on tests that test retry and have to wait this cooldown multiple times
		every { EtherscanConnector.ETHERSCAN_RATE_LIMIT_COOLDOWN } returns 10.milliseconds.toJavaDuration()

		// Capture EvmTokenInfoCreatedEvent
		every { emitApplicationEvent<NewEvmTokenInfoCreatedEvent>() } just Runs
	}

	@AfterEach
	fun tearDown() {
		unmockkObject(EtherscanConnector.Companion)
	}

	@Test
	fun `should save verified EVM token info`() {
		every {
			etherscanConnector.getSourceCode("1".toAddress(), any())
		} returns EtherscanConnector.GetSourceCodeResultResponse(
			sourceCode = "sourceCode",
			proxy = "0",
			abi = "[ABI]",
			implementation = "",
		)

		commandBus(
			SaveTokenInfoCommand(
				chain = Chain.EVM_MAINNET,
				tokenAddress = "1".toAddress(),
				pairAddress = "2".toAddress(),
				decimals = 18.toBigInteger(),
				name = "Test0",
				symbol = "Test0",
				imageUrl = "testUrl",
			),
		)

		val tokenInfos = tokenInfoRepository.findAll()
		tokenInfos shouldHaveSize 1
		tokenInfos.single().run {
			chain shouldBe Chain.EVM_MAINNET
			address shouldBe "1".toAddress()
			decimals shouldBe 18.toBigInteger()
			name shouldBe "Test0"
			symbol shouldBe "Test0"
			imageFileId shouldBe null
			isVerified shouldBe true
			isToken2022 shouldBe false
		}

		verify(exactly = 1) { emitApplicationEvent<NewEvmTokenInfoCreatedEvent>() }
	}

	@Test
	fun `should save unverified EVM token info with retry`() {
		integrationTestHelper.getFile(id = 10.toUUID())

		every {
			etherscanConnector.getSourceCode("1".toAddress(), Chain.EVM_MAINNET.evmId)
		} returnsMany listOf(
			EtherscanConnector.GetSourceCodeResultResponse(
				sourceCode = "",
				proxy = "0",
				abi = "fail",
				implementation = "",
			),
			EtherscanConnector.GetSourceCodeResultResponse(
				sourceCode = "sourceCode",
				proxy = "0",
				abi = "Contract source code not verified",
				implementation = "",
			),
		)

		every { fileDownloadClient.downloadAndSaveFile("testUrl") } returns mockk {
			every { id } returns 10.toUUID()
		}

		commandBus(
			SaveTokenInfoCommand(
				chain = Chain.EVM_MAINNET,
				tokenAddress = "1".toAddress(),
				pairAddress = "2".toAddress(),
				decimals = 18.toBigInteger(),
				name = "Test0",
				symbol = "Test0",
				imageUrl = "testUrl",
			),
		)

		val tokenInfos = tokenInfoRepository.findAll()
		tokenInfos shouldHaveSize 1
		tokenInfos.single().run {
			chain shouldBe Chain.EVM_MAINNET
			address shouldBe "1".toAddress()
			imageFileId shouldBe 10.toUUID()
			decimals shouldBe 18.toBigInteger()
			name shouldBe "Test0"
			symbol shouldBe "Test0"
			isVerified shouldBe false
			isToken2022 shouldBe false
		}

		verify(exactly = 2) {
			etherscanConnector.getSourceCode("1".toAddress(), Chain.EVM_MAINNET.evmId)
		}

		verify(exactly = 1) { emitApplicationEvent<NewEvmTokenInfoCreatedEvent>() }
	}

	@Test
	fun `should verify if solana token is token2022`() {
		every { rpcApi.getSplTokenAccountInfo(PublicKey("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump")) } returns mockk {
			every { value.owner } returns SolanaConstants.TOKEN_2022_PROGRAM_ID.toBase58()
		}

		every { rpcApi.getSplTokenAccountInfo(PublicKey("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc")) } returns mockk {
			every { value.owner } returns SolanaConstants.TOKEN_PROGRAM_ID.toBase58()
		}

		commandBus(
			SaveTokenInfoCommand(
				chain = Chain.SOLANA,
				tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
				pairAddress = "2".toAddress(),
				decimals = 9.toBigInteger(),
				name = "Test0",
				symbol = "Test0",
				imageUrl = null,
			),
		)

		commandBus(
			SaveTokenInfoCommand(
				chain = Chain.SOLANA,
				tokenAddress = AddressWrapper("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc"),
				pairAddress = "2".toAddress(),
				decimals = 6.toBigInteger(),
				name = "Test1",
				symbol = "Test1",
				imageUrl = null,
			),
		)

		val tokenInfos = tokenInfoRepository.findAll()
		tokenInfos shouldHaveSize 2

		tokenInfos.single { it.name == "Test0" }.run {
			chain shouldBe Chain.SOLANA
			address shouldBe AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump")
			decimals shouldBe 9.toBigInteger()
			name shouldBe "Test0"
			symbol shouldBe "Test0"
			imageFileId shouldBe null
			isVerified shouldBe true
			isToken2022 shouldBe true
		}

		tokenInfos.single { it.name == "Test1" }.run {
			chain shouldBe Chain.SOLANA
			address shouldBe AddressWrapper("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc")
			decimals shouldBe 6.toBigInteger()
			name shouldBe "Test1"
			symbol shouldBe "Test1"
			imageFileId shouldBe null
			isVerified shouldBe true
			isToken2022 shouldBe false
		}

		// 1 event per token
		verify(exactly = 2) { emitApplicationEvent<NewEvmTokenInfoCreatedEvent>() }
	}

	@Test
	fun `should fail after all verify token retries `() {
		integrationTestHelper.getFile(id = 10.toUUID())

		// 1 Initial call + 5 retries
		every {
			etherscanConnector.getSourceCode("1".toAddress(), Chain.EVM_MAINNET.evmId)
		} returnsMany List(6) {
			EtherscanConnector.GetSourceCodeResultResponse(
				sourceCode = "sourceCode",
				proxy = "0",
				abi = "fail",
				implementation = "",
			)
		} + EtherscanConnector.GetSourceCodeResultResponse(
			sourceCode = "sourceCode",
			proxy = "0",
			abi = "[ABI]",
			implementation = "",
		)

		shouldThrow<IllegalStateException> {
			commandBus(
				SaveTokenInfoCommand(
					chain = Chain.EVM_MAINNET,
					tokenAddress = "1".toAddress(),
					pairAddress = "2".toAddress(),
					decimals = 18.toBigInteger(),
					name = "Test0",
					symbol = "Test0",
					imageUrl = null,
				),
			)
		}

		val tokenInfos = tokenInfoRepository.findAll()
		tokenInfos shouldHaveSize 0

		verify(exactly = 6) {
			etherscanConnector.getSourceCode("1".toAddress(), Chain.EVM_MAINNET.evmId)
		}
	}

	@Test
	fun `should save token info only once`() {
		integrationTestHelper.getEvmTokenInfo(
			id = 1.toUUID(),
			chain = Chain.EVM_MAINNET,
			tokenAddress = "1".toAddress(),
			name = "Test0",
		)

		commandBus(
			SaveTokenInfoCommand(
				chain = Chain.EVM_MAINNET,
				tokenAddress = "1".toAddress(),
				pairAddress = "2".toAddress(),
				decimals = 18.toBigInteger(),
				name = "Test1",
				symbol = "Test1",
				imageUrl = null,
			),
		)

		val tokenInfos = tokenInfoRepository.findAll()
		tokenInfos shouldHaveSize 1

		tokenInfos.single().run {
			id shouldBe 1.toUUID()
			name shouldBe "Test0"
		}
	}

	@Test
	fun `should save verified EVM proxy token info`() {
		every {
			etherscanConnector.getSourceCode("1".toAddress(), any())
		} returns EtherscanConnector.GetSourceCodeResultResponse(
			sourceCode = "sourceCode",
			proxy = "1",
			abi = "Contract source code not verified",
			implementation = "2",
		)

		commandBus(
			SaveTokenInfoCommand(
				chain = Chain.EVM_MAINNET,
				tokenAddress = "1".toAddress(),
				pairAddress = "2".toAddress(),
				decimals = 18.toBigInteger(),
				name = "Test0",
				symbol = "Test0",
				imageUrl = "testUrl",
			),
		)

		val tokenInfos = tokenInfoRepository.findAll()
		tokenInfos shouldHaveSize 1
		tokenInfos.single().run {
			chain shouldBe Chain.EVM_MAINNET
			address shouldBe "1".toAddress()
			decimals shouldBe 18.toBigInteger()
			name shouldBe "Test0"
			symbol shouldBe "Test0"
			imageFileId shouldBe null
			isVerified shouldBe true
			isToken2022 shouldBe false
		}

		verify(exactly = 1) {
			applicationEventMulticaster.multicastEvent(any(), tokenCreatedEventPayloadType)
		}

		verify(exactly = 1) {
			etherscanConnector.getSourceCode("1".toAddress(), any())
		}
	}
}
