package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalances
import com.cleevio.fatbot.application.common.util.AssociatedTokenAddressPair
import com.cleevio.fatbot.application.common.util.getAssociatedTokenAddressPair
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.limitorder.event.LimitOrderMatchedEvent
import com.cleevio.fatbot.application.module.limitorder.service.LimitOrderMatchingService
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderRepository
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.toBigInteger
import com.cleevio.fatbot.toSolanaAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.p2p.solanaj.core.PublicKey
import org.springframework.beans.factory.annotation.Autowired

class LimitOrderMatchingServiceTest(
	@Autowired val limitOrderMatchingService: LimitOrderMatchingService,
	@Autowired val limitOrderRepository: LimitOrderRepository,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		// Capture LimitOrderMatchedEvent
		every { emitApplicationEvent<LimitOrderMatchedEvent>() } just Runs

		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			address = "abc".toSolanaAddress(),
			chain = Chain.SOLANA,
		)

		every { rpcClient.api.getBalance("abc".toSolanaAddress().getSolanaPublicKey()) } returns 1e9.toLong()

		mockkStatic(rpcClient::getTokenAccountBalances)
		mockkStatic(::getAssociatedTokenAddressPair)
	}

	@AfterEach
	fun tearDown() {
		unmockkStatic(rpcClient::getTokenAccountBalances)
		unmockkStatic(::getAssociatedTokenAddressPair)
	}

	@Test
	fun `should match BUY orders`() {
		val tokenAddress = "a".toSolanaAddress()

		integrationTestHelper.getTokenPairInfo(
			chain = Chain.SOLANA,
			tokenAddress = tokenAddress,
			pairAddress = "a1".toSolanaAddress(),
			tokenDecimals = 9.toBigInteger(),
			dexType = GetDex.Dex.PUMP_SWAP,
			uniswapV3Fee = null,
		)

		/*
		Will get matched by the DB (limitPrice < realPrice), but not by amountOut (high amount will move the avg price over the limit)
		 */
		integrationTestHelper.getLimitOrder(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = tokenAddress,
			chain = Chain.SOLANA,
			type = LimitOrderType.BUY,
			initialAmount = 1e9.toBigInteger(),
			limitPrice = 0.05e9.toBigInteger(),
		)

		/*
		Will get matched by the DB (limitPrice < realPrice) and amountOut

		Notice the same limitPrice the first LimitOrder, but lower initial amount
		 */
		integrationTestHelper.getLimitOrder(
			id = 11.toUUID(),
			userId = 1.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = tokenAddress,
			chain = Chain.SOLANA,
			type = LimitOrderType.BUY,
			initialAmount = 0.1e9.toBigInteger(),
			limitPrice = 0.05e9.toBigInteger(),
		)

		/*
		Will not get matched by DB
		 */
		integrationTestHelper.getLimitOrder(
			id = 12.toUUID(),
			userId = 1.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = tokenAddress,
			chain = Chain.SOLANA,
			type = LimitOrderType.BUY,
			initialAmount = 0.1e9.toBigInteger(),
			limitPrice = 0.001e9.toBigInteger(),
		)

		every {
			rpcClient.getTokenAccountBalances(any(), any())
		} returns listOf("***************".toBigInteger(), "*************".toBigInteger())

		runBlocking {
			limitOrderMatchingService.submitTokenPriceUpdate(
				tokenToPrice = mapOf(
					tokenAddress to "0.01".toBigDecimal(),
					// Random token prices -> Should not affect the execution and be ignored by the query
					AddressWrapper("EC15R5KpmM1zo58PFhNYvBR6V6czQWEBscE2LHNYWqkK") to "0.123".toBigDecimal(),
					AddressWrapper("CpdWca87PH71Lh2kvvjEchTnodLUt6WJT7Xo613DxjFi") to "1.1".toBigDecimal(),
				),
			)
		}

		testScheduler.advanceUntilIdle()

		val limitOrders = limitOrderRepository.findAll()
		limitOrders shouldHaveSize 3

		limitOrders.single { it.id == 10.toUUID() }.run {
			isLocked shouldBe false
		}

		limitOrders.single { it.id == 11.toUUID() }.run {
			isLocked shouldBe true
		}

		limitOrders.single { it.id == 12.toUUID() }.run {
			isLocked shouldBe false
		}

		verify(exactly = 1) { emitApplicationEvent<LimitOrderMatchedEvent>() }
	}

	@Test
	fun `should match SELL orders`() {
		val tokenAddress = "a".toSolanaAddress()

		integrationTestHelper.getTokenPairInfo(
			chain = Chain.SOLANA,
			tokenAddress = tokenAddress,
			pairAddress = "a1".toSolanaAddress(),
			tokenDecimals = 9.toBigInteger(),
			dexType = GetDex.Dex.PUMP_SWAP,
			uniswapV3Fee = null,
		)

		/*
		Will get matched by the DB (limitPrice > realPrice), but not by amountOut (high amount will move the avg price under the limit)
		 */
		integrationTestHelper.getLimitOrder(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = tokenAddress,
			chain = Chain.SOLANA,
			type = LimitOrderType.SELL,
			initialAmount = 1000e9.toBigInteger(),
			limitPrice = 0.05e9.toBigInteger(),
		)

		/*
		Will get matched by the DB (limitPrice > realPrice) and amountOut

		Notice the same limitPrice the first LimitOrder, but lower initial amount
		 */
		integrationTestHelper.getLimitOrder(
			id = 11.toUUID(),
			userId = 1.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = tokenAddress,
			chain = Chain.SOLANA,
			type = LimitOrderType.SELL,
			initialAmount = 1e9.toBigInteger(),
			limitPrice = 0.05e9.toBigInteger(),
		)

		/*
		Will not get matched by DB
		 */
		integrationTestHelper.getLimitOrder(
			id = 12.toUUID(),
			userId = 1.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = tokenAddress,
			chain = Chain.SOLANA,
			type = LimitOrderType.SELL,
			initialAmount = 0.1e9.toBigInteger(),
			limitPrice = 0.1e9.toBigInteger(),
		)

		val tokenVault = PublicKey("5rZeeZf5V2r6kbqSgHssmSnUCmsjMy5VVsiiJRwin8Fb")
		val wsolVault = PublicKey("GMKRLd8KfPBUFkSQ9zCvKLwa8oeTu39Xc9j2V1a2ePSn")

		every {
			rpcClient.getTokenAccountBalances(listOf(tokenVault, wsolVault), any())
		} returns listOf("***************".toBigInteger(), "*************".toBigInteger())

		val tokenAccountAddress = PublicKey("Dv6dpUa2kxYVX9EjfvAeNZW975yLavHvHnMfX6whNVQ9")
		val token2022AccountAddress = PublicKey("9TYhScZdRhREkp3SovW1xbuWTgWkDC1JSEYY2uxmS8kK")
		every {
			getAssociatedTokenAddressPair(
				mint = tokenAddress.getSolanaPublicKey(),
				owner = PublicKey("88LcF1eRycbVuFovUhCToUGzW5C4xR588m2Vvq79eosP"),
			)
		} returns AssociatedTokenAddressPair(
			tokenAddress = tokenAccountAddress,
			token2022Address = token2022AccountAddress,
		)

		every {
			rpcClient.getTokenAccountBalances(listOf(tokenAccountAddress, token2022AccountAddress), any())
		} returns listOf(1000e9.toBigInteger(), null)

		runBlocking {
			limitOrderMatchingService.submitTokenPriceUpdate(
				tokenToPrice = mapOf(
					tokenAddress to "0.06".toBigDecimal(),
					// Random token prices -> Should not affect the execution and be ignored by the query
					AddressWrapper("EC15R5KpmM1zo58PFhNYvBR6V6czQWEBscE2LHNYWqkK") to "0.123".toBigDecimal(),
					AddressWrapper("CpdWca87PH71Lh2kvvjEchTnodLUt6WJT7Xo613DxjFi") to "1.1".toBigDecimal(),
				),
			)
		}

		testScheduler.advanceUntilIdle()

		val limitOrders = limitOrderRepository.findAll()
		limitOrders shouldHaveSize 3

		limitOrders.single { it.id == 10.toUUID() }.run {
			isLocked shouldBe false
		}

		limitOrders.single { it.id == 11.toUUID() }.run {
			isLocked shouldBe true
		}

		limitOrders.single { it.id == 12.toUUID() }.run {
			isLocked shouldBe false
		}

		verify(exactly = 1) { emitApplicationEvent<LimitOrderMatchedEvent>() }
	}
}
