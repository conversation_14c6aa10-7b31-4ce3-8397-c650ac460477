package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailureReason
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.constant.TransferTokenTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.query.GetUserLifetimeTradeVolumeQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.bigdecimal.shouldBeEqualIgnoringScale
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger

class GetUserLifetimeTradeVolumeQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should work when user has no wallets`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		val result = queryBus(GetUserLifetimeTradeVolumeQuery(userId = 1.toUUID()))

		result.lifetimeTradeVolumeUsd shouldBe BigDecimal.ZERO
	}

	@Test
	fun `should work when user has no transactions`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getWallet(id = 10.toUUID(), userId = 1.toUUID(), address = "aaa".toAddress())
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), address = "bbb".toAddress())

		val result = queryBus(GetUserLifetimeTradeVolumeQuery(userId = 1.toUUID()))

		result.lifetimeTradeVolumeUsd shouldBe BigDecimal.ZERO
	}

	@Test
	fun `should correctly compute user lifetime trade volume`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getWallet(id = 10.toUUID(), userId = 1.toUUID(), address = "aaa".toAddress())
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), address = "bbb".toAddress())

		// and unrelated transactions
		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			type = TransactionType.TRANSFER_CURRENCY,
			signedTx = "0x1",
			beforeSave = {
				markAsSuccess(
					TransferTokenTransactionSuccess(
						amountIn = BigInteger.ONE,
						amountOut = BigInteger.ONE,
					),
					exchangeRateUsd = BigDecimal("2222.22"),
				)
			},
		)
		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			type = TransactionType.TRANSFER_TOKEN,
			signedTx = "0x2",
			beforeSave = {
				markAsSuccess(
					TransferTokenTransactionSuccess(
						amountIn = BigInteger.ONE,
						amountOut = BigInteger.ONE,
					),
					exchangeRateUsd = BigDecimal("2222.22"),
				)
			},
		)
		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			signedTx = "0x3",
			type = TransactionType.APPROVE,
			beforeSave = { markAsSuccess() },
		)

		// and tracking transactions
		integrationTestHelper.getTrackingTransaction(
			walletId = 10.toUUID(),
			type = TransactionType.BUY,
			baseValue = 10000000000000000L.toBigInteger(),
			beforeSave = {
				completeTrackingTransaction(
					tokenAmount = BigInteger("100"),
					baseValue = 10000000000000000L.toBigInteger(),
				)
			},
		)
		integrationTestHelper.getTrackingTransaction(
			walletId = 10.toUUID(),
			type = TransactionType.SELL,
			baseValue = 10000000000000000L.toBigInteger(),
			beforeSave = {
				completeTrackingTransaction(
					tokenAmount = BigInteger("100"),
					baseValue = 10000000000000000L.toBigInteger(),
				)
			},
		)

		// and failed transactions
		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			type = TransactionType.BUY,
			signedTx = "0x4",
			beforeSave = {
				markAsFailed(failReason = TransactionFailureReason.UNDEFINED)
			},
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			type = TransactionType.SELL,
			signedTx = "0x5",
			beforeSave = {
				markAsFailed(failReason = TransactionFailureReason.UNDEFINED)
			},
		)

		// and pending transactions
		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			type = TransactionType.BUY,
			signedTx = "0x6",
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			type = TransactionType.SELL,
			signedTx = "0x7",
		)

		// and proper buy transactions
		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			type = TransactionType.BUY,
			signedTx = "0x8",
			beforeSave = {
				markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger("500000000000000000"), // 0.5 ETH
						amountOut = BigInteger("123"), // does not matter
						fee = BigInteger("1000"), // does not matter
						referralFee = BigInteger("1000"), // does not matter
						referralRewardRecipient = 0.toUUID(), // does not matter
						balanceChange = BaseAmount.ZERO,
						instructionAmount = BigInteger.ZERO,
					),
					exchangeRateUsd = BigDecimal("2500.01"),
				)
			},
		)

		integrationTestHelper.getTransaction(
			walletId = 20.toUUID(),
			type = TransactionType.BUY,
			signedTx = "0x9",
			beforeSave = {
				markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger("1000000000000000000"), // 1 ETH
						amountOut = BigInteger("123"), // does not matter
						fee = BigInteger("1000"), // does not matter
						referralFee = BigInteger("1000"), // does not matter
						referralRewardRecipient = 0.toUUID(), // does not matter
						balanceChange = BaseAmount.ZERO,
						instructionAmount = BigInteger.ZERO,
					),
					exchangeRateUsd = BigDecimal("3000"),
				)
			},
		)

		// and proper sell transactions
		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			type = TransactionType.SELL,
			signedTx = "0x10",
			beforeSave = {
				markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger("123"), // does not matter
						amountOut = BigInteger("1500000000000000000"), // 1.5 ETH
						fee = BigInteger("1000"), // does not matter
						referralFee = BigInteger("1000"), // does not matter
						referralRewardRecipient = 0.toUUID(), // does not matter
						balanceChange = BaseAmount.ZERO,
						instructionAmount = BigInteger.ZERO,
					),
					exchangeRateUsd = BigDecimal("4200"),
				)
			},
		)

		integrationTestHelper.getTransaction(
			walletId = 20.toUUID(),
			type = TransactionType.SELL,
			signedTx = "0x11",
			beforeSave = {
				markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger("123"), // does not matter
						amountOut = BigInteger("1500000000000000000"), // 1.5 ETH
						fee = BigInteger("1000"), // does not matter
						referralFee = BigInteger("1000"), // does not matter
						referralRewardRecipient = 0.toUUID(), // does not matter
						balanceChange = BaseAmount.ZERO,
						instructionAmount = BigInteger.ZERO,
					),
					exchangeRateUsd = BigDecimal("4000"),
				)
			},
		)

		val result = queryBus(GetUserLifetimeTradeVolumeQuery(userId = 1.toUUID()))

		result.lifetimeTradeVolumeUsd shouldBeEqualIgnoringScale BigDecimal("16550.005")
	}

	@Test
	fun `should correctly sum across chains`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = "aaa".toAddress(),
			chain = Chain.SOLANA,
		)
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), address = "bbb".toAddress())

		integrationTestHelper.getTransaction(
			walletId = 20.toUUID(),
			type = TransactionType.BUY,
			signedTx = "0x1",
			beforeSave = {
				markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger("1000000000000000000"), // 1 ETH
						amountOut = BigInteger("123"), // does not matter
						fee = BigInteger("1000"), // does not matter
						referralFee = BigInteger("1000"), // does not matter
						referralRewardRecipient = 0.toUUID(), // does not matter
						balanceChange = BaseAmount.ZERO,
						instructionAmount = BigInteger.ZERO,
					),
					exchangeRateUsd = BigDecimal("3000"),
				)
			},
		)

		integrationTestHelper.getTransactionSolana(
			walletId = 10.toUUID(),
			type = TransactionType.BUY,
			signedTx = "0x2",
			beforeSave = {
				markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger("1000000000"), // 1 SOL
						amountOut = BigInteger("123"), // does not matter
						fee = BigInteger("1000"), // does not matter
						referralFee = BigInteger("1000"), // does not matter
						referralRewardRecipient = 0.toUUID(), // does not matter
						balanceChange = BaseAmount.ZERO,
						instructionAmount = BigInteger.ZERO,
					),
					exchangeRateUsd = BigDecimal("300"),
				)
			},
		)

		val result = queryBus(GetUserLifetimeTradeVolumeQuery(userId = 1.toUUID()))

		result.lifetimeTradeVolumeUsd shouldBeEqualIgnoringScale BigDecimal("3300")
	}
}
