package com.cleevio.fatbot.application.module.userstatistics

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.util.atStartOfNextDay
import com.cleevio.fatbot.application.common.util.getWeekStart
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.userstatistics.constant.StreakDayState
import com.cleevio.fatbot.application.module.userstatistics.query.GetUserStreakQuery
import com.cleevio.fatbot.domain.userstatistics.StreakState
import com.cleevio.fatbot.setAndReturnPrivateProperty
import com.cleevio.fatbot.tables.references.TRANSACTION
import com.cleevio.fatbot.tables.references.USER_STATISTICS
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.equals.shouldBeEqual
import io.kotest.matchers.shouldBe
import org.jooq.DSLContext
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.ZoneOffset

class GetUserStreakQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val integrationTestClock: IntegrationTestClock,
	@Autowired private val dslContext: DSLContext,
) : IntegrationTest() {

	@Test
	fun `should return correct streak info for new user without transactions`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(userId = 1.toUUID())

		val result = queryBus(GetUserStreakQuery(userId = 1.toUUID()))

		result.userId shouldBe 1.toUUID()
		result.daysInStreak shouldBe 0
		result.currentMultiplier shouldBe BigDecimal.ONE
		result.daysToNextStreak shouldBe 3
		result.nextMultiplier!! shouldBeEqual 1.2.toBigDecimal()
	}

	@Test
	fun `should return correct streak info for user with 3 day streak`() {
		val userId = 1.toUUID()
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			daysInStreak = 3,
		)

		val result = queryBus(GetUserStreakQuery(userId))

		result.userId shouldBe userId
		result.daysInStreak shouldBe 3
		result.currentMultiplier shouldBe 1.2.toBigDecimal()
		result.daysToNextStreak shouldBe 4 // 7 - 3
		result.nextMultiplier!! shouldBeEqualComparingTo 1.5.toBigDecimal()
	}

	@Test
	fun `should return correct streak info for user with 7 day streak`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			daysInStreak = 7,
		)

		val result = queryBus(GetUserStreakQuery(userId = 1.toUUID()))

		result.userId shouldBe 1.toUUID()
		result.daysInStreak shouldBe 7
		result.currentMultiplier shouldBe 1.5.toBigDecimal()
		result.daysToNextStreak shouldBe 23 // 30 - 7
		result.nextMultiplier!! shouldBeEqual 2.toBigDecimal()
	}

	@Test
	fun `should return correct streak info for user with transaction`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getTransaction(
			walletId = 2.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			daysInStreak = 1,
		)

		val result = queryBus(GetUserStreakQuery(1.toUUID()))

		result.userId shouldBe 1.toUUID()
		result.daysInStreak shouldBe 1
		result.currentMultiplier shouldBe BigDecimal.ONE
		result.daysToNextStreak shouldBe 2 // 3 - 1
		result.nextMultiplier!! shouldBeEqual 1.2.toBigDecimal()
	}

	@Test
	fun `should return correct streak info for user with more than max day streak`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			daysInStreak = 777,
		)

		val result = queryBus(GetUserStreakQuery(userId = 1.toUUID()))

		result.userId shouldBe 1.toUUID()
		result.daysInStreak shouldBe 777
		result.currentMultiplier shouldBe 3.toBigDecimal()
		result.daysToNextStreak shouldBe null
		result.nextMultiplier shouldBe null
	}

	@Test
	fun `should return NOT_STARTED state for today if task is not completed`() {
		val userId = 1.toUUID()
		integrationTestHelper.getFirebaseUser(id = userId)
		integrationTestHelper.getUserStatistics(userId = userId)

		val result = queryBus(GetUserStreakQuery(userId))

		result.userId shouldBe userId
		result.streakState shouldBe StreakState.NOT_STARTED
		val today = integrationTestClock.getCurrentDate()
		val todayResult = result.streakDates.find { it.date == today }
		todayResult?.state shouldBe StreakDayState.NOT_STARTED
	}

	@Test
	fun `should return streak days where start of streak was today`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getUserStatistics(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			daysInStreak = 1,
			lastManualTradeAt = integrationTestClock.getCurrentDate(),
			beforeSave = {
				setAndReturnPrivateProperty("streakState", StreakState.IN_PROGRESS)
				setAndReturnPrivateProperty("lastStreakStartedAt", integrationTestClock.currentTime())
			},
		)

		integrationTestHelper.getWallet(id = 3.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getTransaction(
			id = 4.toUUID(),
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, integrationTestClock.currentTime())
			.set(TRANSACTION.UPDATED_AT, integrationTestClock.currentTime())
			.where(TRANSACTION.ID.eq(4.toUUID()))
			.execute()

		// when
		val result = queryBus(GetUserStreakQuery(userId = 1.toUUID()))

		// then
		result.run {
			userId shouldBe 1.toUUID()
			daysInStreak shouldBe 1
			streakExpiresAt shouldBe integrationTestClock.getCurrentDate().plusDays(1).atStartOfNextDay()
			streakState shouldBe StreakState.IN_PROGRESS
			streakDates shouldContainExactly listOf(
				GetUserStreakQuery.StreakDayResult(
					date = integrationTestClock.getCurrentDate(),
					state = StreakDayState.COMPLETED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = integrationTestClock.getCurrentDate().plusDays(1),
					state = StreakDayState.NEXT,
				),
				GetUserStreakQuery.StreakDayResult(
					date = integrationTestClock.getCurrentDate().plusDays(2),
					state = StreakDayState.NEXT,
				),
				GetUserStreakQuery.StreakDayResult(
					date = integrationTestClock.getCurrentDate().plusDays(3),
					state = StreakDayState.NEXT,
				),
				GetUserStreakQuery.StreakDayResult(
					date = integrationTestClock.getCurrentDate().plusDays(4),
					state = StreakDayState.NEXT,
				),
				GetUserStreakQuery.StreakDayResult(
					date = integrationTestClock.getCurrentDate().plusDays(5),
					state = StreakDayState.NEXT,
				),
				GetUserStreakQuery.StreakDayResult(
					date = integrationTestClock.getCurrentDate().plusDays(6),
					state = StreakDayState.NEXT,
				),
			)
		}
	}

	@Test
	fun `should return streak days where start of streak was couple of days ago`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		val lastStreakStartedDate = integrationTestClock.getCurrentDate().minusDays(3)
		val lastStreakStartedAt = lastStreakStartedDate.atStartOfDay().toInstant(ZoneOffset.UTC)

		integrationTestHelper.getUserStatistics(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			daysInStreak = 3,
			lastManualTradeAt = integrationTestClock.getCurrentDate().minusDays(1),
			beforeSave = {
				setAndReturnPrivateProperty("streakState", StreakState.IN_PROGRESS)
				setAndReturnPrivateProperty("lastStreakStartedAt", lastStreakStartedAt)
			},
		)

		integrationTestHelper.getWallet(id = 3.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getTransaction(
			id = 4.toUUID(),
			signedTx = "txHash1",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 5.toUUID(),
			signedTx = "txHash2",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 6.toUUID(),
			signedTx = "txHash3",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedAt)
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedAt)
			.where(TRANSACTION.ID.eq(4.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(5.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(2).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(2).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(6.toUUID()))
			.execute()

		// when
		val result = queryBus(GetUserStreakQuery(userId = 1.toUUID()))

		// then
		result.run {
			userId shouldBe 1.toUUID()
			daysInStreak shouldBe 3
			streakState shouldBe StreakState.IN_PROGRESS
			streakExpiresAt shouldBe integrationTestClock.getCurrentDate().plusDays(1).atStartOfNextDay()
			streakDates shouldContainExactly listOf(
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate,
					state = StreakDayState.COMPLETED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(1),
					state = StreakDayState.COMPLETED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(2),
					state = StreakDayState.COMPLETED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(3),
					state = StreakDayState.NOT_STARTED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(4),
					state = StreakDayState.NEXT,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(5),
					state = StreakDayState.NEXT,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(6),
					state = StreakDayState.NEXT,
				),
			)
		}
	}

	@Test
	fun `should return streak days where start of streak was a week ago`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		val lastStreakStartedDate = integrationTestClock.getCurrentDate().minusDays(9)
		val lastStreakStartedAt = lastStreakStartedDate.atStartOfDay().toInstant(ZoneOffset.UTC)

		integrationTestHelper.getUserStatistics(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			daysInStreak = 10,
			lastManualTradeAt = integrationTestClock.getCurrentDate(),
			beforeSave = {
				setAndReturnPrivateProperty("streakState", StreakState.IN_PROGRESS)
				setAndReturnPrivateProperty("lastStreakStartedAt", lastStreakStartedAt)
			},
		)

		integrationTestHelper.getWallet(id = 3.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getTransaction(
			id = 4.toUUID(),
			signedTx = "txHash1",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 5.toUUID(),
			signedTx = "txHash2",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 6.toUUID(),
			signedTx = "txHash3",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 7.toUUID(),
			signedTx = "txHash4",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 8.toUUID(),
			signedTx = "txHash5",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 9.toUUID(),
			signedTx = "txHash6",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 10.toUUID(),
			signedTx = "txHash7",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 11.toUUID(),
			signedTx = "txHash8",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 12.toUUID(),
			signedTx = "txHash9",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 13.toUUID(),
			signedTx = "txHash10",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedAt)
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedAt)
			.where(TRANSACTION.ID.eq(4.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(5.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(2).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(2).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(6.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(3).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(3).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(7.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(4).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(4).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(8.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(5).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(5).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(9.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(6).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(6).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(10.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(7).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(7).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(11.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(8).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(8).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(12.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(9).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(9).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(13.toUUID()))
			.execute()

		// when
		val result = queryBus(GetUserStreakQuery(userId = 1.toUUID()))

		val dayOfWeek = DayOfWeek.from(lastStreakStartedDate)
		val nextWeekStartDate = integrationTestClock.getCurrentDate().getWeekStart().plusDays(dayOfWeek.value.toLong() - 1)

		// then
		result.run {
			userId shouldBe 1.toUUID()
			daysInStreak shouldBe 10
			streakState shouldBe StreakState.IN_PROGRESS
			streakExpiresAt shouldBe integrationTestClock.getCurrentDate().plusDays(1).atStartOfNextDay()
			streakDates shouldContainExactly listOf(
				GetUserStreakQuery.StreakDayResult(
					date = nextWeekStartDate,
					state = StreakDayState.COMPLETED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = nextWeekStartDate.plusDays(1),
					state = StreakDayState.COMPLETED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = nextWeekStartDate.plusDays(2),
					state = StreakDayState.COMPLETED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = nextWeekStartDate.plusDays(3),
					state = StreakDayState.NEXT,
				),
				GetUserStreakQuery.StreakDayResult(
					date = nextWeekStartDate.plusDays(4),
					state = StreakDayState.NEXT,
				),
				GetUserStreakQuery.StreakDayResult(
					date = nextWeekStartDate.plusDays(5),
					state = StreakDayState.NEXT,
				),
				GetUserStreakQuery.StreakDayResult(
					date = nextWeekStartDate.plusDays(6),
					state = StreakDayState.NEXT,
				),
			)
		}
	}

	@Test
	fun `should return failed streak days when streak is failed even when longer than a week ago`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		val lastStreakStartedDate = integrationTestClock.getCurrentDate().minusDays(6)
		val lastStreakStartedAt = lastStreakStartedDate.atStartOfDay().toInstant(ZoneOffset.UTC)

		integrationTestHelper.getUserStatistics(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			daysInStreak = 0,
			lastManualTradeAt = lastStreakStartedDate.plusDays(1),
			beforeSave = {
				setAndReturnPrivateProperty("streakState", StreakState.FAILED)
				setAndReturnPrivateProperty("lastStreakStartedAt", lastStreakStartedAt)
			},
		)

		integrationTestHelper.getWallet(id = 3.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getTransaction(
			id = 4.toUUID(),
			signedTx = "txHash1",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 5.toUUID(),
			signedTx = "txHash2",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedAt)
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedAt)
			.where(TRANSACTION.ID.eq(4.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(5.toUUID()))
			.execute()

		dslContext.update(USER_STATISTICS)
			.set(
				USER_STATISTICS.CREATED_AT,
				integrationTestClock.getCurrentDate().minusDays(11).atStartOfDay().toInstant(ZoneOffset.UTC),
			)
			.set(USER_STATISTICS.UPDATED_AT, integrationTestClock.currentTime())
			.where(USER_STATISTICS.ID.eq(2.toUUID()))
			.execute()

		// when
		val result = queryBus(GetUserStreakQuery(userId = 1.toUUID()))

		// then
		result.run {
			userId shouldBe 1.toUUID()
			daysInStreak shouldBe 0
			streakState shouldBe StreakState.FAILED
			streakExpiresAt shouldBe integrationTestClock.getCurrentDate().atStartOfNextDay()
			streakDates shouldContainExactly listOf(
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate,
					state = StreakDayState.COMPLETED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(1),
					state = StreakDayState.COMPLETED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(2),
					state = StreakDayState.FAILED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(3),
					state = StreakDayState.FAILED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(4),
					state = StreakDayState.FAILED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(5),
					state = StreakDayState.FAILED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(6),
					state = StreakDayState.NOT_STARTED,
				),
			)
		}
	}

	@Test
	fun `should return failed streak days when streak is failed longer than a week ago`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		val lastStreakStartedDate = integrationTestClock.getCurrentDate().minusDays(15)
		val lastStreakStartedAt = lastStreakStartedDate.atStartOfDay().toInstant(ZoneOffset.UTC)

		integrationTestHelper.getUserStatistics(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			daysInStreak = 0,
			lastManualTradeAt = lastStreakStartedDate.plusDays(1),
			beforeSave = {
				setAndReturnPrivateProperty("streakState", StreakState.FAILED)
				setAndReturnPrivateProperty("lastStreakStartedAt", lastStreakStartedAt)
			},
		)

		integrationTestHelper.getWallet(id = 3.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getTransaction(
			id = 4.toUUID(),
			signedTx = "txHash1",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getTransaction(
			id = 5.toUUID(),
			signedTx = "txHash2",
			walletId = 3.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedAt)
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedAt)
			.where(TRANSACTION.ID.eq(4.toUUID()))
			.execute()

		dslContext.update(TRANSACTION)
			.set(TRANSACTION.CREATED_AT, lastStreakStartedDate.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC))
			.set(TRANSACTION.UPDATED_AT, lastStreakStartedDate.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC))
			.where(TRANSACTION.ID.eq(5.toUUID()))
			.execute()

		dslContext.update(USER_STATISTICS)
			.set(
				USER_STATISTICS.CREATED_AT,
				integrationTestClock.getCurrentDate().minusDays(20).atStartOfDay().toInstant(ZoneOffset.UTC),
			)
			.set(USER_STATISTICS.UPDATED_AT, integrationTestClock.currentTime())
			.where(USER_STATISTICS.ID.eq(2.toUUID()))
			.execute()

		// when
		val result = queryBus(GetUserStreakQuery(userId = 1.toUUID()))

		// then
		result.run {
			userId shouldBe 1.toUUID()
			daysInStreak shouldBe 0
			streakState shouldBe StreakState.FAILED
			streakExpiresAt shouldBe integrationTestClock.getCurrentDate().atStartOfNextDay()
			streakDates shouldContainExactly listOf(
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate,
					state = StreakDayState.COMPLETED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(1),
					state = StreakDayState.COMPLETED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(2),
					state = StreakDayState.FAILED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(3),
					state = StreakDayState.FAILED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(4),
					state = StreakDayState.FAILED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(5),
					state = StreakDayState.FAILED,
				),
				GetUserStreakQuery.StreakDayResult(
					date = lastStreakStartedDate.plusDays(6),
					state = StreakDayState.FAILED,
				),
			)
		}
	}
}
