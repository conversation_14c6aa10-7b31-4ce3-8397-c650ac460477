package com.cleevio.fatbot.application.module.hottoken

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.adapter.out.dexscreener.response.GetTokenPairsInfoResponse
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.hottoken.query.GetHotTokensQuery
import com.cleevio.fatbot.application.module.hottoken.scheduled.HotTokenRefreshTrigger
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.referral.ReferralRewardService.Companion.ZERO_UUID
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailureReason
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.hottoken.HotTokenRepository
import com.cleevio.fatbot.domain.transaction.Transaction
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.bigdecimal.shouldBeEqualIgnoringScale
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.equals.shouldNotBeEqual
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.time.temporal.ChronoUnit

@Suppress("ktlint:standard:max-line-length")
class GetHotTokensQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val hotTokenRefreshTrigger: HotTokenRefreshTrigger,
	@Autowired private val integrationTestClock: IntegrationTestClock,
	@Autowired private val hotTokenRepository: HotTokenRepository,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = "aaa".toAddress(),
			name = "CLEEVIO TOKEN 1",
			symbol = "CLT1",
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = "aaa".toAddress(),
			name = "CLEEVIO TOKEN 1",
			symbol = "CLT1",
			chain = Chain.EVM_BASE,
		)

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = "bbb".toAddress(),
			name = "CLEEVIO TOKEN 2",
			symbol = "CLT2",
			chain = Chain.EVM_MAINNET,
		)

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = "ccc".toAddress(),
			name = "CLEEVIO TOKEN 3",
			symbol = "CLT3",
			chain = Chain.EVM_BASE,
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = "ccc".toAddress(),
			name = "CLEEVIO TOKEN 3",
			symbol = "CLT3",
			chain = Chain.EVM_ARBITRUM_ONE,
		)

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("DeiZk6A4eqD8iANsdEwP2EyCHzAdWfWyYQcgNGiXpump"),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN"),
			chain = Chain.SOLANA,
			tokenDecimals = 9.toBigInteger(),
		)

		integrationTestHelper.getTokenPairInfo(
			chain = Chain.SOLANA,
			dexType = GetDex.Dex.PUMP_FUN,
			tokenAddress = AddressWrapper("DeiZk6A4eqD8iANsdEwP2EyCHzAdWfWyYQcgNGiXpump"),
			pairAddress = AddressWrapper("********************************************"),
			tokenDecimals = 6.toBigInteger(),
			uniswapV3Fee = null,
		)
	}

	@Test
	fun `should get all EVM mainnet hot tokens for signed user`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID(), selectedChains = setOf(Chain.EVM_MAINNET))

		integrationTestHelper.getWallet(id = 10.toUUID(), userId = 1.toUUID(), address = 10.toAddress())
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), address = 20.toAddress())

		// Utility for marking Transaction successful. Only used here as the exact values are not considered.
		val defaultMarkAsSuccessModifier: (Transaction) -> Unit = {
			it.markAsSuccess(
				result = BuySellTransactionSuccess(
					amountIn = BigInteger.valueOf(100_000_000_000_000),
					amountOut = BigInteger.valueOf(100_000_000_000_000_000),
					fee = BigInteger.valueOf(1),
					referralFee = BigInteger.ZERO,
					referralRewardRecipient = ZERO_UUID,
					balanceChange = BaseAmount.ZERO,
					instructionAmount = BigInteger.ZERO,
				),
				exchangeRateUsd = BigDecimal("2000.000"),
			)
		}

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "aaa".toAddress(),
			signedTx = "0x000000000000",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "aaa".toAddress(),
			signedTx = "0x000000000001",
			type = TransactionType.SELL,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(100_000_000_000_000_000),
						amountOut = BigInteger.valueOf(100_000_000_000_000),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
						instructionAmount = BigInteger.ZERO,
					),
					exchangeRateUsd = BigDecimal("2000.000"),
				)
			},
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "bbb".toAddress(),
			signedTx = "0x000000000002",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		/*
		All invalid transactions for Hot token logic are put on tokenAddress 123
		Meaning the token should not appear at all in the result set

		Invalid type Transactions. Only Buy & Sell are considered
		 */
		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "ccc".toAddress(),
			signedTx = "0x000000000004",
			type = TransactionType.TRANSFER_TOKEN,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "ccc".toAddress(),
			signedTx = "0x000000000005",
			type = TransactionType.TRANSFER_CURRENCY,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "ccc".toAddress(),
			signedTx = "0x000000000006",
			type = TransactionType.APPROVE,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		/*
		Invalid status Transactions. Only Success is considered
		 */
		val tx7 = integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "ccc".toAddress(),
			signedTx = "0x000000000007",
			type = TransactionType.BUY,
		)
		tx7.status shouldBe TransactionStatus.PENDING

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "ccc".toAddress(),
			signedTx = "0x000000000008",
			type = TransactionType.BUY,
			entityModifier = { it.markAsFailed(failReason = TransactionFailureReason.UNDEFINED) },
		)

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************","******************************************"]
				"0xe016d3ab0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000002" +
					"000000000000000000000000aaa0000000000000000000000000000000000000" +
					"000000000000000000000000bbb0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000a0" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"000000000000000000000000bbb0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"0000000000000000000000000000000000000000000000000000000000989680" +
			"0000000000000000000000000000000000000000000000000000000000e4e1c0"

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2000.00")
		every { dexScreenerConnector.getTokenPairsInfoByAddress(any()) } returns GetTokenPairsInfoResponse(listOf())

		integrationTestClock.setTo(Instant.now())
		hotTokenRefreshTrigger.trigger()

		val result = queryBus(
			GetHotTokensQuery.asSignedUser(
				infiniteScroll = InfiniteScrollDesc.BigDecimal(size = 10),
				userId = 1.toUUID(),
				useSelectedChains = true,
			),
		)

		result.content shouldHaveSize 2

		// 1 BUY and 1 SELL transaction of the same amount
		result.content[0].run {
			tokenAddress shouldBe "aaa".toAddress()
			tokenName shouldBe "CLEEVIO TOKEN 1"
			tokenSymbol shouldBe "CLT1"
			tokenImageUrl shouldBe null
			change24h shouldBe null
			volume24hUsd shouldBeEqualComparingTo 0.4.toBigDecimal()
			priceUsd shouldBeEqualIgnoringScale 0.00000002.toBigDecimal()
		}
		// only 1 BUY transaction of different amount
		result.content[1].run {
			tokenAddress shouldBe "bbb".toAddress()
			tokenName shouldBe "CLEEVIO TOKEN 2"
			tokenSymbol shouldBe "CLT2"
			tokenImageUrl shouldBe null
			change24h shouldBe null
			volume24hUsd shouldBeEqualComparingTo 0.2.toBigDecimal()
			priceUsd shouldBeEqualIgnoringScale 0.00000003.toBigDecimal()
		}
	}

	@Test
	fun `should slice EVM hot tokens`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getWallet(id = 10.toUUID(), userId = 1.toUUID(), address = 10.toAddress())
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), address = 20.toAddress())

		// Utility for marking Transaction successful. Only used here as the exact values are not considered.
		val defaultMarkAsSuccessModifier: (Transaction) -> Unit = {
			it.markAsSuccess(
				result = BuySellTransactionSuccess(
					amountIn = BigInteger.valueOf(100),
					amountOut = BigInteger.valueOf(100_000),
					fee = BigInteger.valueOf(1),
					referralFee = BigInteger.ZERO,
					referralRewardRecipient = ZERO_UUID,
					balanceChange = BaseAmount.ZERO,
					instructionAmount = BigInteger.ZERO,
				),
				exchangeRateUsd = BigDecimal("2000.001"),
			)
		}

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "aaa".toAddress(),
			signedTx = "0x000000000000",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "aaa".toAddress(),
			signedTx = "0x000000000001",
			type = TransactionType.SELL,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "bbb".toAddress(),
			signedTx = "0x000000000002",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************"]
				"0xe016d3ab" +
					"0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000001" +
					"000000000000000000000000aaa0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns // mock for getTokenPrices - 15000000 in hex
			"0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000000000000e4e1c0"

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************"]
				"0xe016d3ab" +
					"0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000001" +
					"000000000000000000000000bbb0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns // mock for getTokenPrices - 15000000 in hex
			"0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000bbb0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000000000000e4e1c0"

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2004.01")

		integrationTestClock.setTo(Instant.now())
		hotTokenRefreshTrigger.trigger()

		val resultFirstChunk = queryBus(
			GetHotTokensQuery.asSignedUser(
				infiniteScroll = InfiniteScrollDesc.BigDecimal(size = 1),
				userId = 1.toUUID(),
				useSelectedChains = true,
			),
		)

		resultFirstChunk.content shouldHaveSize 1

		val firstResult = resultFirstChunk.content.single()

		val resultSecondChunk = queryBus(
			GetHotTokensQuery.asSignedUser(
				infiniteScroll = InfiniteScrollDesc.BigDecimal(size = 1, lastId = firstResult.volume24hUsd),
				userId = 1.toUUID(),
				useSelectedChains = true,
			),
		)

		resultSecondChunk.content shouldHaveSize 1

		val secondResult = resultSecondChunk.content.single()

		firstResult.tokenAddress shouldNotBeEqual secondResult.tokenAddress

		val resultThirdChunk = queryBus(
			GetHotTokensQuery.asSignedUser(
				infiniteScroll = InfiniteScrollDesc.BigDecimal(size = 1, lastId = secondResult.volume24hUsd),
				userId = 1.toUUID(),
				useSelectedChains = true,
			),
		)

		resultThirdChunk.content shouldHaveSize 0
	}

	@Test
	fun `should get all EVM hot tokens with 1d change from DEX and price from router`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 10.toUUID(), userId = 1.toUUID(), address = 10.toAddress())

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "aaa".toAddress(),
			signedTx = "0x000000000000",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(100_000_000_000_000),
						amountOut = BigInteger.valueOf(100_000_000_000_000_000),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
						instructionAmount = BigInteger.ZERO,
					),
					exchangeRateUsd = BigDecimal("2000.000"),
				)
			},
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "aaa".toAddress(),
			signedTx = "0x000000000001",
			type = TransactionType.SELL,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(100_000_000_000_000_000),
						amountOut = BigInteger.valueOf(100_000_000_000_000),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
						instructionAmount = BigInteger.ZERO,
					),
					exchangeRateUsd = BigDecimal("2000.000"),
				)
			},
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.TEN
		every { web3jWrapper.getWeb3j() } returns web3j
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************"]
				"0xe016d3ab" +
					"0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000001" +
					"000000000000000000000000aaa0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returnsMany listOf(
			// mock for getTokenPrices - 15000000 in hex
			"0x" +
				"0000000000000000000000000000000000000000000000000000000000000040" +
				"0000000000000000000000000000000000000000000000000000000000000080" +
				"0000000000000000000000000000000000000000000000000000000000000001" +
				"000000000000000000000000aaa0000000000000000000000000000000000000" +
				"0000000000000000000000000000000000000000000000000000000000000001" +
				"0000000000000000000000000000000000000000000000000000000000e4e1c0",
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2000.00")

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = "aaa".toAddress(),
			chain = Chain.EVM_MAINNET,
			validAt = integrationTestClock.currentTime().minus(24, ChronoUnit.HOURS),
			priceWei = BigInteger("15000000"),
			exchangeRateUsd = BigDecimal("2000.00"),
		)

		integrationTestClock.setTo(Instant.now())
		hotTokenRefreshTrigger.trigger()

		// advance time to expire GetTokenPricesEVM cache
		integrationTestClock.advanceBy(91, ChronoUnit.SECONDS)

		val result = queryBus(
			GetHotTokensQuery.asSignedUser(
				infiniteScroll = InfiniteScrollDesc.BigDecimal(size = 10),
				userId = 1.toUUID(),
				useSelectedChains = true,
			),
		)

		result.content shouldHaveSize 1

		result.content[0].run {
			tokenAddress shouldBe "aaa".toAddress()
			tokenName shouldBe "CLEEVIO TOKEN 1"
			tokenSymbol shouldBe "CLT1"
			tokenImageUrl shouldBe null
			change24h!!.shouldBeEqualComparingTo(0.0.toBigDecimal())
			volume24hUsd shouldBeEqualComparingTo 0.4.toBigDecimal()
			priceUsd shouldBeEqualComparingTo 0.00000003.toBigDecimal()
		}

		// 24h change and USD price are supposed to differ in DB
		hotTokenRepository.findAll().first().run {
			tokenAddress shouldBe "aaa".toAddress()
			tokenName shouldBe "CLEEVIO TOKEN 1"
			tokenSymbol shouldBe "CLT1"
			volume24hUsd shouldBeEqualComparingTo 0.4.toBigDecimal()
		}
	}

	@Test
	fun `should get all EVM hot tokens with 1d change from DEX and price from router with cache`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID(), selectedChains = setOf(Chain.EVM_ARBITRUM_ONE))
		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = 10.toAddress(),
			chain = Chain.EVM_ARBITRUM_ONE,
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "ccc".toAddress(),
			chainId = Chain.EVM_ARBITRUM_ONE.evmId,
			signedTx = "0x000000000000",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(100_000_000_000_000),
						amountOut = BigInteger.valueOf(100_000_000_000_000_000),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
						instructionAmount = BigInteger.ZERO,
					),
					exchangeRateUsd = BigDecimal("2000.000"),
				)
			},
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "ccc".toAddress(),
			chainId = Chain.EVM_ARBITRUM_ONE.evmId,
			signedTx = "0x000000000001",
			type = TransactionType.SELL,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(100_000_000_000_000_000),
						amountOut = BigInteger.valueOf(100_000_000_000_000),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
						instructionAmount = BigInteger.ZERO,
					),
					exchangeRateUsd = BigDecimal("2000.000"),
				)
			},
		)

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************"]
				"0xe016d3ab" +
					"0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000001" +
					"000000000000000000000000ccc0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returnsMany listOf(
			// mock for hot tokens refresh call - 25000000 in hex
			"0x" +
				"0000000000000000000000000000000000000000000000000000000000000040" +
				"0000000000000000000000000000000000000000000000000000000000000080" +
				"0000000000000000000000000000000000000000000000000000000000000001" +
				"000000000000000000000000ccc0000000000000000000000000000000000000" +
				"0000000000000000000000000000000000000000000000000000000000000001" +
				"00000000000000000000000000000000000000000000000000000000017d7840",
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2000.00")

		integrationTestClock.setTo(Instant.now())
		hotTokenRefreshTrigger.trigger()

		// advance time to expire GetTokenPricesEVM cache
		integrationTestClock.advanceBy(91, ChronoUnit.SECONDS)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = "ccc".toAddress(),
			chain = Chain.EVM_ARBITRUM_ONE,
			exchangeRateUsd = BigDecimal("2000.00"),
			priceWei = BigInteger("15000000"),
			validAt = integrationTestClock.currentTime().minus(24, ChronoUnit.HOURS),
		)

		val result = queryBus(
			GetHotTokensQuery.asSignedUser(
				infiniteScroll = InfiniteScrollDesc.BigDecimal(size = 10),
				userId = 1.toUUID(),
				useSelectedChains = true,
			),
		)

		result.content shouldHaveSize 1

		result.content[0].run {
			tokenAddress shouldBe "ccc".toAddress()
			tokenName shouldBe "CLEEVIO TOKEN 3"
			tokenSymbol shouldBe "CLT3"
			tokenImageUrl shouldBe null
			// Change from 15000000 -> 25000000
			change24h!!.shouldBeEqualComparingTo("0.666666666666666666666666666666667".toBigDecimal())
			volume24hUsd shouldBeEqualComparingTo 0.4.toBigDecimal()
			priceUsd shouldBeEqualComparingTo 0.00000005.toBigDecimal()
		}

		// second call - should load previous hot token from cache and fetch data for new hot token
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = "ddd".toAddress(),
			name = "CLEEVIO TOKEN 4",
			symbol = "CLT4",
			chain = Chain.EVM_ARBITRUM_ONE,
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			tokenAddress = "ddd".toAddress(),
			chainId = Chain.EVM_ARBITRUM_ONE.evmId,
			signedTx = "0x000000000002",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(100_000_000_000_000),
						amountOut = BigInteger.valueOf(100_000_000_000_000_000),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
						instructionAmount = BigInteger.ZERO,
					),
					exchangeRateUsd = BigDecimal("2000.000"),
				)
			},
		)

		// this time only mock bbb get token prices call, because aaa is still cached
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************"]
				"0xe016d3ab" +
					"0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000001" +
					"000000000000000000000000ddd0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returnsMany listOf(
			// mock for hot tokens refresh call - 10000000 in hex for bbb
			"0x" +
				"0000000000000000000000000000000000000000000000000000000000000040" +
				"0000000000000000000000000000000000000000000000000000000000000080" +
				"0000000000000000000000000000000000000000000000000000000000000001" +
				"000000000000000000000000ddd0000000000000000000000000000000000000" +
				"0000000000000000000000000000000000000000000000000000000000000001" +
				"0000000000000000000000000000000000000000000000000000000000989680",
		)

		// Refresh should add 'ddd' token to hot tokenns
		hotTokenRefreshTrigger.trigger()

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = "ddd".toAddress(),
			chain = Chain.EVM_ARBITRUM_ONE,
			exchangeRateUsd = BigDecimal("2500.00"),
			priceWei = BigInteger("20000000"),
			validAt = integrationTestClock.currentTime().minus(24, ChronoUnit.HOURS),
		)

		val result2 = queryBus(
			GetHotTokensQuery.asSignedUser(
				infiniteScroll = InfiniteScrollDesc.BigDecimal(size = 10),
				userId = 1.toUUID(),
				useSelectedChains = true,
			),
		)

		result2.content shouldHaveSize 2

		result2.content[0].run {
			tokenAddress shouldBe "ccc".toAddress()
			tokenName shouldBe "CLEEVIO TOKEN 3"
			tokenSymbol shouldBe "CLT3"
			tokenImageUrl shouldBe null
			change24h!!.shouldBeEqualComparingTo("0.666666666666666666666666666666667".toBigDecimal())
			volume24hUsd shouldBeEqualComparingTo 0.4.toBigDecimal()
			priceUsd shouldBeEqualComparingTo 0.00000005.toBigDecimal()
		}
		result2.content[1].run {
			tokenAddress shouldBe "ddd".toAddress()
			tokenName shouldBe "CLEEVIO TOKEN 4"
			tokenSymbol shouldBe "CLT4"
			tokenImageUrl shouldBe null
			// Price Change from 20000000 -> 10000000
			// Rate change from 2500 -> 2000
			change24h!!.shouldBeEqualComparingTo("-0.6".toBigDecimal())
			volume24hUsd shouldBeEqualComparingTo 0.2.toBigDecimal()
			priceUsd shouldBeEqualComparingTo 0.00000002.toBigDecimal()
		}
	}

	@Test
	fun `should create hot tokens with multiple chains`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID(), selectedChains = setOf(Chain.EVM_MAINNET))

		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = 10.toAddress(),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getWallet(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			address = 10.toAddress(),
			chain = Chain.EVM_BASE,
		)
		integrationTestHelper.getWallet(
			id = 30.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper("Va6i2rYHoWExMWZrpjF5mvMabQW4v25MvfKky4WqQ7c"),
			chain = Chain.SOLANA,
		)

		// Utility for marking Transaction successful. Only used here as the exact values are not considered.
		val defaultMarkAsSuccessModifier: (Transaction) -> Unit = {
			it.markAsSuccess(
				result = BuySellTransactionSuccess(
					amountIn = BigInteger.valueOf(100),
					amountOut = BigInteger.valueOf(100_000),
					fee = BigInteger.valueOf(1),
					referralFee = BigInteger.ZERO,
					referralRewardRecipient = ZERO_UUID,
					balanceChange = BaseAmount.ZERO,
					instructionAmount = BigInteger.ZERO,
				),
				exchangeRateUsd = BigDecimal("2000.001"),
			)
		}

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			chainId = Chain.EVM_MAINNET.evmId,
			tokenAddress = "aaa".toAddress(),
			signedTx = "0x000000000000",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			chainId = Chain.EVM_MAINNET.evmId,
			tokenAddress = "aaa".toAddress(),
			signedTx = "0x000000000010",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		integrationTestHelper.getTransaction(
			walletId = 20.toUUID(),
			chainId = Chain.EVM_BASE.evmId,
			tokenAddress = "aaa".toAddress(),
			signedTx = "0x000000000001",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		integrationTestHelper.getTransaction(
			walletId = 20.toUUID(),
			chainId = Chain.EVM_BASE.evmId,
			tokenAddress = "aaa".toAddress(),
			signedTx = "0x000000000002",
			type = TransactionType.SELL,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		integrationTestHelper.getTransaction(
			walletId = 20.toUUID(),
			chainId = Chain.EVM_BASE.evmId,
			tokenAddress = "aaa".toAddress(),
			signedTx = "0x000000000003",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		integrationTestHelper.getTransaction(
			walletId = 10.toUUID(),
			chainId = Chain.EVM_MAINNET.evmId,
			tokenAddress = "bbb".toAddress(),
			signedTx = "0x000000000004",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		integrationTestHelper.getTransaction(
			walletId = 20.toUUID(),
			chainId = Chain.EVM_BASE.evmId,
			tokenAddress = "ccc".toAddress(),
			signedTx = "0x000000000005",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifier,
		)

		integrationTestHelper.getTransactionSolana(
			walletId = 30.toUUID(),
			tokenAddress = AddressWrapper("DeiZk6A4eqD8iANsdEwP2EyCHzAdWfWyYQcgNGiXpump"),
			signedTx = "0x000000000000",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifier,
		)
		// Call on chain 1
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************","******************************************"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000002000000000000000000000000aaa0000000000000000000000000000000000000000000000000000000000000bbb0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000a0" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"000000000000000000000000bbb0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"0000000000000000000000000000000000000000000000000DE0B6B3A7640000" + // 1 ETH
			"000000000000000000000000000000000000000000000000016345785D8A0000" // 0.1ETH

		// Call on chain 8453
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************","******************************************"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000002000000000000000000000000aaa0000000000000000000000000000000000000000000000000000000000000ccc0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000a0" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"000000000000000000000000ccc0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"00000000000000000000000000000000000000000000000006F05B59D3B20000" + // 0.5 ETH
			"0000000000000000000000000000000000000000000000001BC16D674EC80000" // 2 ETH

		every {
			rpcApi.getMultipleAccounts(
				listOf(PublicKey("********************************************")),
				mapOf("commitment" to Commitment.PROCESSED),
			)
		} returns listOf(
			mockk {
				every { data } returns listOf(
					"F7f4N2DYrGCi761NxmEDAM4okN8HAAAAolebATVjAgDOfGzjAAAAAACAxqR+jQMAAG2eomTfS7Qfnjvlo+c70ZXfhRnB6bnFkUNMqQjGnbT2",
					"base64",
				)
			},
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("3000")
		every { dexScreenerConnector.getTokenPairsInfoByAddress(any()) } returns GetTokenPairsInfoResponse(listOf())

		integrationTestClock.setTo(Instant.now())
		hotTokenRefreshTrigger.trigger()

		val mainnetResult = queryBus(
			GetHotTokensQuery.asSignedUser(
				infiniteScroll = InfiniteScrollDesc.BigDecimal(size = 10),
				userId = 1.toUUID(),
				useSelectedChains = true,
			),
		)

		integrationTestHelper.setSelectedChains(userId = 1.toUUID(), setOf(Chain.EVM_BASE))

		val baseResult = queryBus(
			GetHotTokensQuery.asSignedUser(
				infiniteScroll = InfiniteScrollDesc.BigDecimal(size = 10),
				userId = 1.toUUID(),
				useSelectedChains = true,
			),
		)

		integrationTestHelper.setSelectedChains(userId = 1.toUUID(), setOf(Chain.EVM_MAINNET, Chain.EVM_BASE))

		val baseAndMainnetResult = queryBus(
			GetHotTokensQuery.asSignedUser(
				infiniteScroll = InfiniteScrollDesc.BigDecimal(size = 10),
				userId = 1.toUUID(),
				useSelectedChains = true,
			),
		)

		// None selected
		integrationTestHelper.setSelectedChains(userId = 1.toUUID(), selectedChains = emptySet())

		val allChainsResult = queryBus(
			GetHotTokensQuery.asSignedUser(
				infiniteScroll = InfiniteScrollDesc.BigDecimal(size = 10),
				userId = 1.toUUID(),
				useSelectedChains = false,
			),
		)

		/*
		Number of transactions:

		'aaa', Base -> 3
		'ccc', Base -> 1
		'aaa', Mainnet -> 2
		'bbb', Mainnet -> 1
		"DeiZk6A4eqD8iANsdEwP2EyCHzAdWfWyYQcgNGiXpump", Solana -> 1

		All EVM Txns have the same volume per transaction
		Solana has the highest volume, as it has 9 decimals and we treat it like like EVM with 18
		 */

		with(mainnetResult.content) {
			size shouldBe 2

			this[0].tokenAddress shouldBe "aaa".toAddress()
			this[0].priceUsd shouldBeEqualIgnoringScale BigDecimal("3000")

			this[1].tokenAddress shouldBe "bbb".toAddress()
			this[1].priceUsd shouldBeEqualIgnoringScale BigDecimal("300")
		}

		with(baseResult.content) {
			size shouldBe 2

			this[0].tokenAddress shouldBe "aaa".toAddress()
			this[0].priceUsd shouldBeEqualIgnoringScale BigDecimal("1500")

			this[1].tokenAddress shouldBe "ccc".toAddress()
			this[1].priceUsd shouldBeEqualIgnoringScale BigDecimal("6000")
		}

		with(baseAndMainnetResult.content) {
			size shouldBe 4

			this[0].tokenAddress shouldBe "aaa".toAddress()
			this[0].priceUsd shouldBeEqualIgnoringScale BigDecimal("1500")
			this[0].tokenChain shouldBe Chain.EVM_BASE

			this[1].tokenAddress shouldBe "aaa".toAddress()
			this[1].priceUsd shouldBeEqualIgnoringScale BigDecimal("3000")
			this[1].tokenChain shouldBe Chain.EVM_MAINNET

			this[2].tokenAddress shouldBe "bbb".toAddress()
			this[2].priceUsd shouldBeEqualIgnoringScale BigDecimal("300")
			this[2].tokenChain shouldBe Chain.EVM_MAINNET

			this[3].tokenAddress shouldBe "ccc".toAddress()
			this[3].priceUsd shouldBeEqualIgnoringScale BigDecimal("6000")
			this[3].tokenChain shouldBe Chain.EVM_BASE
		}

		with(allChainsResult.content) {
			size shouldBe 5

			this[0].tokenAddress shouldBe AddressWrapper("DeiZk6A4eqD8iANsdEwP2EyCHzAdWfWyYQcgNGiXpump")
			this[0].tokenChain shouldBe Chain.SOLANA
			this[0].priceUsd shouldBeEqualComparingTo BigDecimal("0.000105")

			this[1].tokenAddress shouldBe "aaa".toAddress()
			this[1].priceUsd shouldBeEqualIgnoringScale BigDecimal("1500")
			this[1].tokenChain shouldBe Chain.EVM_BASE

			this[2].tokenAddress shouldBe "aaa".toAddress()
			this[2].priceUsd shouldBeEqualIgnoringScale BigDecimal("3000")
			this[2].tokenChain shouldBe Chain.EVM_MAINNET

			this[3].tokenAddress shouldBe "bbb".toAddress()
			this[3].priceUsd shouldBeEqualIgnoringScale BigDecimal("300")
			this[3].tokenChain shouldBe Chain.EVM_MAINNET

			this[4].tokenAddress shouldBe "ccc".toAddress()
			this[4].priceUsd shouldBeEqualIgnoringScale BigDecimal("6000")
			this[4].tokenChain shouldBe Chain.EVM_BASE
		}
	}

	@Test
	fun `should compute solana hot tokens`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper("Va6i2rYHoWExMWZrpjF5mvMabQW4v25MvfKky4WqQ7c"),
			chain = Chain.SOLANA,
		)

		// Utility for marking Transaction successful. Only used here as the exact values are not considered.
		val defaultMarkAsSuccessModifierOnBuy: (Transaction) -> Unit = {
			it.markAsSuccess(
				result = BuySellTransactionSuccess(
					amountIn = BigInteger.valueOf(1000000000), // 1 SOL
					amountOut = BigInteger.valueOf(1000000),
					fee = BigInteger.valueOf(0),
					referralFee = BigInteger.ZERO,
					referralRewardRecipient = ZERO_UUID,
					balanceChange = BaseAmount.ZERO,
					instructionAmount = BigInteger.ZERO,
				),
				exchangeRateUsd = BigDecimal("100"),
			)
		}

		val defaultMarkAsSuccessModifierOnSell: (Transaction) -> Unit = {
			it.markAsSuccess(
				result = BuySellTransactionSuccess(
					amountIn = BigInteger.valueOf(1000000),
					amountOut = BigInteger.valueOf(1000000000), // 1 SOL (on sells, amount out is native currency)
					fee = BigInteger.valueOf(0),
					referralFee = BigInteger.ZERO,
					referralRewardRecipient = ZERO_UUID,
					balanceChange = BaseAmount.ZERO,
					instructionAmount = BigInteger.ZERO,
				),
				exchangeRateUsd = BigDecimal("100"),
			)
		}

		integrationTestHelper.getTransactionSolana(
			walletId = 10.toUUID(),
			tokenAddress = AddressWrapper("DeiZk6A4eqD8iANsdEwP2EyCHzAdWfWyYQcgNGiXpump"),
			signedTx = "0x000000000000",
			signature = "sig0",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifierOnBuy,
		)

		integrationTestHelper.getTransactionSolana(
			walletId = 10.toUUID(),
			tokenAddress = AddressWrapper("6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN"),
			signedTx = "0x000000000001",
			signature = "sig1",
			type = TransactionType.BUY,
			entityModifier = defaultMarkAsSuccessModifierOnBuy,
		)

		integrationTestHelper.getTransactionSolana(
			walletId = 10.toUUID(),
			tokenAddress = AddressWrapper("6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN"),
			signedTx = "0x000000000002",
			signature = "sig2",
			type = TransactionType.SELL,
			entityModifier = defaultMarkAsSuccessModifierOnSell,
		)

		integrationTestClock.setTo(Instant.now())
		hotTokenRefreshTrigger.trigger()

		val hotTokens = hotTokenRepository.findAll()

		hotTokens.size shouldBe 2
		hotTokens.sumOf { it.volume24hUsd } shouldBeEqualIgnoringScale BigDecimal("300")
	}
}
