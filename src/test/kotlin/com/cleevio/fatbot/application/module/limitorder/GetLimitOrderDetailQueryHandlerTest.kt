package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.limitorder.query.GetLimitOrderDetailQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.shouldBeEqualComparingTo
import com.cleevio.fatbot.toBigInteger
import com.cleevio.fatbot.toSolanaAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetLimitOrderDetailQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return buy limit order detail`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			id = 10.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = "a".toSolanaAddress(),
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getLimitOrder(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			walletId = 2.toUUID(),
			tokenAddress = "a".toSolanaAddress(),
			limitPrice = 0.1e9.toBigInteger(),
			initialAmount = 1e9.toBigInteger(),
			type = LimitOrderType.BUY,
			chain = Chain.SOLANA,
		)

		// when
		val result = queryBus(GetLimitOrderDetailQuery(userId = 1.toUUID(), limitOrderId = 3.toUUID()))

		// then
		result.id shouldBe 3.toUUID()
		result.walletId shouldBe 2.toUUID()
		result.tokenAddress shouldBe "a".toSolanaAddress()
		result.chain shouldBe Chain.SOLANA
		result.type shouldBe LimitOrderType.BUY
		result.isLocked shouldBe false
		result.limitPrice shouldBeEqualComparingTo 0.1.toBigDecimal()
		result.remainingAmount shouldBeEqualComparingTo 1.toBigDecimal()
		result.filledAmount shouldBeEqualComparingTo 0.toBigDecimal()
	}

	@Test
	fun `should return sell limit order detail`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			id = 10.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = "a".toSolanaAddress(),
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getLimitOrder(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			walletId = 2.toUUID(),
			tokenAddress = "a".toSolanaAddress(),
			limitPrice = 0.1e9.toBigInteger(),
			initialAmount = 1e6.toBigInteger(),
			type = LimitOrderType.SELL,
			chain = Chain.SOLANA,
		)

		// when
		val result = queryBus(GetLimitOrderDetailQuery(userId = 1.toUUID(), limitOrderId = 3.toUUID()))

		// then
		result.id shouldBe 3.toUUID()
		result.walletId shouldBe 2.toUUID()
		result.tokenAddress shouldBe "a".toSolanaAddress()
		result.chain shouldBe Chain.SOLANA
		result.type shouldBe LimitOrderType.SELL
		result.isLocked shouldBe false
		result.limitPrice shouldBeEqualComparingTo 0.1.toBigDecimal()
		result.remainingAmount shouldBeEqualComparingTo 1.toBigDecimal()
		result.filledAmount shouldBeEqualComparingTo 0.toBigDecimal()
	}
}
