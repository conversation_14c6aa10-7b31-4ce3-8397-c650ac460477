package com.cleevio.fatbot.application.module.botdraft

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.botdraft.command.DeleteBotDraftCommand
import com.cleevio.fatbot.domain.botdraft.BotDraftRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class DeleteBotDraftCommandHandlerTest(
	@Autowired private val botDraftRepository: BotDraftRepository,
	@Autowired private val commandBus: CommandBus,
) : IntegrationTest() {

	@Test
	fun `should delete bot draft`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getBotDraft(id = 2.toUUID(), userId = 1.toUUID())

		botDraftRepository.count() shouldBe 1

		commandBus(DeleteBotDraftCommand(userId = 1.toUUID(), botDraftId = 2.toUUID()))

		botDraftRepository.count() shouldBe 0
	}
}
