package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.user.command.UpdateLanguageCommand
import com.cleevio.fatbot.domain.user.FirebaseUserRepository
import com.cleevio.fatbot.domain.user.Language
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class UpdateLanguageCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val firebaseUserRepository: FirebaseUserRepository,
) : IntegrationTest() {

	@Test
	fun `should update user language`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID(), language = Language.ENGLISH)

		user.language shouldBe Language.ENGLISH

		commandBus(UpdateLanguageCommand(userId = 1.toUUID(), language = Language.CZECH))

		with(firebaseUserRepository.findAll().first()) {
			language shouldBe Language.CZECH
		}
	}
}
