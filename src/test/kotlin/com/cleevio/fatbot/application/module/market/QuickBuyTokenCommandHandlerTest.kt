package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.adapter.out.dexscreener.response.GetTokenPairsInfoResponse
import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector
import com.cleevio.fatbot.adapter.out.evm.model.FeesPerGas
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.command.QuickBuyTokenCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant

@Suppress("ktlint:standard:max-line-length")
class QuickBuyTokenCommandHandlerTest(@Autowired private val commandBus: CommandBus) : IntegrationTest() {

	@BeforeEach
	fun setup() {
		every {
			bitqueryApiV2Connector.getEvmTokenPrice(any(), any(), any(), any(), any(), any(), any(), any())
		} returns emptyList()

		every {
			dexScreenerConnector.getTokenPairsInfoByAddress(AddressWrapper("******************************************"))
		} returns GetTokenPairsInfoResponse(
			pairs = listOf(
				getDefaultTokenPairResponse(pairCreatedAt = Instant.now()),
			),
		)

		every {
			etherscanConnector.getSourceCode(any(), any())
		} returns EtherscanConnector.GetSourceCodeResponse(
			listOf(
				EtherscanConnector.GetSourceCodeResultResponse(
					sourceCode = "sourceCode",
					proxy = "0",
					abi = "[ABI]",
					implementation = "",
				),
			),
		)

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0xe016d3ab" +
					"0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000001" +
					"0000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			// 3084000000000000 decimal in Wei, which is 0.003084 ETH
			"000000000000000000000000000000000000000000000000000AF4E1B47CC000"

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0x313ce567",
				DefaultBlockParameterName.PENDING,
			)
		} returns "0x0000012" // hex of decimal 18
		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0xddca3f43", // UniswapV3 pool fee selector
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00064" // hex of V3Pool fee 100

		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getWalletBalance(any()) } returns BigInteger("1000000000000000000000")
		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()
	}

	@Test
	fun `should buy token for default quickBuyAmountUsd by default wallet`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// these will not be default wallets
		integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id, address = 1.toAddress(), walletCount = 2)
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = user.id, address = 2.toAddress(), walletCount = 1)

		// this will be default wallet as it has wallet count set to 0 (so it's as if new wallet on chain is created)
		integrationTestHelper.getWallet(id = 3.toUUID(), userId = user.id, walletCount = 0)

		/*
		Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0x4f5904a62f813c8f5533ce330b002ac53f0580a497cf2ac956a64835bbf508ea",
			  "nonce": "1",
			  "gasLimit": "28635",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "01",
			  "r": "3f6e49c01f29684f41d82a31d66ca9eab41b6871021e0524e6bc1f9a02968185",
			  "s": "1dbc71962e30fe3ab80e7f71fb82115301e9723da80907584abcfd59efad17c7",
			  "value": "4166664930556278",
			  "input": "0x83ec4e1f000000000000000000000000000000000000000000000000000ecd8f471579760000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb000000000000000000000000000000000000000000000000000000000000006400000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000",
			  "functionHash": "0x83ec4e1f",
			  "functionName": "buyV3",
			  "decodedInputs": {
				"amountIn": "4166664930556278",
				"token": "******************************************",
				"to": "******************************************",
				"fee": "100",
				"uuid": "00000000-0000-0000-0000-000000000000",
				"feePercentage": "100",
				"referralFeePercentage": "0",
				 "amountOutMin": "117333334"
			  }
			}
		 */
		every {
			titanbuilderConnector.sendPrivateEthTransaction(
				signedTx = SignedTx(
					"0x02f901d70101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba13870ecd8f47157976b901646406df81000000000000000000000000000000000000000000000000000ecd8f471579760000000000000000000000000000000000000000000000000000000006fe5d560000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb0000000000000000000000000000000000000000000000000000000000000064000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c080a07068939298d6e30dd63effc2ed6272e300ed8d41f72579eada5fdf85ab71ec26a026a1d9b3315a7ab4801b16cb66a7e789d590644ae2da2d29bee97b6300a7c996",
				),
				maxAllowedBlockNumberToInclude = BigInteger.TWO,
			)
		} just Runs

		every {
			beaverbuildConnector.sendPrivateEthTransaction(
				signedTx = SignedTx(
					"0x02f901d70101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba13870ecd8f47157976b901646406df81000000000000000000000000000000000000000000000000000ecd8f471579760000000000000000000000000000000000000000000000000000000006fe5d560000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb0000000000000000000000000000000000000000000000000000000000000064000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c080a07068939298d6e30dd63effc2ed6272e300ed8d41f72579eada5fdf85ab71ec26a026a1d9b3315a7ab4801b16cb66a7e789d590644ae2da2d29bee97b6300a7c996",
				),
			)
		} just Runs

		// Amount out call
		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0x55658993000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f9840000000000000000000000000000000000000000000000000000000000000064000000000000000000000000000000000000000000000000000ecd8f47157976",
				any(),
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000075BCD15"

		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getBlockNumber() } returns BigInteger.ZERO
		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()

		val result = commandBus(
			QuickBuyTokenCommand(
				userId = user.id,
				chain = Chain.EVM_MAINNET,
				tokenAddress = AddressWrapper("******************************************"),
			),
		)

		result.txHash.txHash shouldBe "0x4f5904a62f813c8f5533ce330b002ac53f0580a497cf2ac956a64835bbf508ea"

		verify(exactly = 1) {
			titanbuilderConnector.sendPrivateEthTransaction(
				signedTx = SignedTx(
					"0x02f901d70101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba13870ecd8f47157976b901646406df81000000000000000000000000000000000000000000000000000ecd8f471579760000000000000000000000000000000000000000000000000000000006fe5d560000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb0000000000000000000000000000000000000000000000000000000000000064000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c080a07068939298d6e30dd63effc2ed6272e300ed8d41f72579eada5fdf85ab71ec26a026a1d9b3315a7ab4801b16cb66a7e789d590644ae2da2d29bee97b6300a7c996",
				),
				maxAllowedBlockNumberToInclude = BigInteger.TWO,
			)
			beaverbuildConnector.sendPrivateEthTransaction(
				signedTx = SignedTx(
					"0x02f901d70101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba13870ecd8f47157976b901646406df81000000000000000000000000000000000000000000000000000ecd8f471579760000000000000000000000000000000000000000000000000000000006fe5d560000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb0000000000000000000000000000000000000000000000000000000000000064000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c080a07068939298d6e30dd63effc2ed6272e300ed8d41f72579eada5fdf85ab71ec26a026a1d9b3315a7ab4801b16cb66a7e789d590644ae2da2d29bee97b6300a7c996",
				),
			)
		}
	}

	@Test
	fun `should buy token for custom quickBuyAmountUsd`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID(), quickBuyAmountUsd = BigDecimal("42.69"))
		integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)

		/*
		Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0x0ff605d97684671b05f4e951ab03012d3c185506ccd1e0e6f98e3357d7adc21a",
			  "nonce": "1",
			  "gasLimit": "28635",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "00",
			  "r": "121a8e0d24e5e25f13444252d45c128978a040d4083bdbc9056c9a8496c762d5",
			  "s": "57278b423e530bcf492337c1850c4b9c52e56b7b65d0312e501d51b8c3861d6b",
			  "value": "17787492588544754",
			  "input": "0x83ec4e1f000000000000000000000000000000000000000000000000003f31a1592f62f20000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb000000000000000000000000000000000000000000000000000000000000006400000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000",
			  "functionHash": "0x83ec4e1f",
			  "functionName": "buyV3",
			  "decodedInputs": {
				"amountIn": "17787492588544754",
				"token": "******************************************",
				"to": "******************************************",
				"fee": "100",
				"uuid": "00000000-0000-0000-0000-000000000000",
				"feePercentage": "100",
				"referralFeePercentage": "0",
				 "amountOutMin": "117333334"
			  }
			}
		 */
		every {
			titanbuilderConnector.sendPrivateEthTransaction(
				signedTx = SignedTx(
					"0x02f901d70101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba13873f31a1592f62f2b901646406df81000000000000000000000000000000000000000000000000003f31a1592f62f20000000000000000000000000000000000000000000000000000000006fe5d560000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb0000000000000000000000000000000000000000000000000000000000000064000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c001a0a4332e291a9ed1eb2ee7615e15e58f69f606f00ee38fdfb01dce7ecc5b99c753a0657d12c4055784e7c5a00355399f4c038264fdd9cd780a35464dac442c3883e7",
				),
				maxAllowedBlockNumberToInclude = BigInteger.TWO,
			)
		} just Runs
		every {
			beaverbuildConnector.sendPrivateEthTransaction(
				signedTx = SignedTx(
					"0x02f901d70101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba13873f31a1592f62f2b901646406df81000000000000000000000000000000000000000000000000003f31a1592f62f20000000000000000000000000000000000000000000000000000000006fe5d560000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb0000000000000000000000000000000000000000000000000000000000000064000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c001a0a4332e291a9ed1eb2ee7615e15e58f69f606f00ee38fdfb01dce7ecc5b99c753a0657d12c4055784e7c5a00355399f4c038264fdd9cd780a35464dac442c3883e7",
				),
			)
		} just Runs

		// Amount out call
		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0x55658993000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f9840000000000000000000000000000000000000000000000000000000000000064000000000000000000000000000000000000000000000000003f31a1592f62f2",
				any(),
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000075BCD15"

		every { web3jWrapper.getBlockNumber() } returns BigInteger.ZERO
		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()

		val result = commandBus(
			QuickBuyTokenCommand(
				userId = user.id,
				chain = Chain.EVM_MAINNET,
				tokenAddress = AddressWrapper("******************************************"),
			),
		)

		result.txHash.txHash shouldBe "0x0ff605d97684671b05f4e951ab03012d3c185506ccd1e0e6f98e3357d7adc21a"

		verify(exactly = 1) {
			titanbuilderConnector.sendPrivateEthTransaction(
				signedTx = SignedTx(
					"0x02f901d70101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba13873f31a1592f62f2b901646406df81000000000000000000000000000000000000000000000000003f31a1592f62f20000000000000000000000000000000000000000000000000000000006fe5d560000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb0000000000000000000000000000000000000000000000000000000000000064000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c001a0a4332e291a9ed1eb2ee7615e15e58f69f606f00ee38fdfb01dce7ecc5b99c753a0657d12c4055784e7c5a00355399f4c038264fdd9cd780a35464dac442c3883e7",
				),
				maxAllowedBlockNumberToInclude = BigInteger.TWO,
			)
			beaverbuildConnector.sendPrivateEthTransaction(
				signedTx = SignedTx(
					"0x02f901d70101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba13873f31a1592f62f2b901646406df81000000000000000000000000000000000000000000000000003f31a1592f62f20000000000000000000000000000000000000000000000000000000006fe5d560000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb0000000000000000000000000000000000000000000000000000000000000064000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c001a0a4332e291a9ed1eb2ee7615e15e58f69f606f00ee38fdfb01dce7ecc5b99c753a0657d12c4055784e7c5a00355399f4c038264fdd9cd780a35464dac442c3883e7",
				),
			)
		}
	}

	private fun getDefaultTokenPairResponse(pairCreatedAt: Instant) = GetTokenPairsInfoResponse.Pair(
		chainId = "ethereum",
		dexId = "uniswap",
		url = "https://dexscreener.com/ethereum/******************************************",
		pairAddress = AddressWrapper("******************************************").getAddressString(),
		baseToken = GetTokenPairsInfoResponse.Pair.Token(
			address = AddressWrapper("******************************************"),
			name = "Uniswap",
			symbol = "UNI",
		),
		quoteToken = GetTokenPairsInfoResponse.Pair.Token(
			address = AddressWrapper("******************************************"),
			name = "Wrapper Ether",
			symbol = "WETH",
		),
		labels = listOf("v3"),
		volume = GetTokenPairsInfoResponse.Pair.TimeIntervals(
			hours24 = BigDecimal("9183404.54"),
			hours06 = BigDecimal("5982758.78"),
			hours01 = BigDecimal("288870.23"),
			minutes05 = BigDecimal("12778.35"),
		),
		priceChange = GetTokenPairsInfoResponse.Pair.TimeIntervals(
			hours24 = BigDecimal("6.94"),
			hours06 = BigDecimal("2.52"),
			hours01 = BigDecimal("0.18"),
			minutes05 = BigDecimal("0.09"),
		),
		fullyDilutedValue = BigDecimal("8126763163"),
		marketCap = BigDecimal("6126023505"),
		liquidity = GetTokenPairsInfoResponse.Pair.Liquidity(
			usd = BigDecimal("27458979.31"),
			base = BigDecimal("2358945"),
			quote = BigDecimal("3145.4293"),
		),
		transactions = GetTokenPairsInfoResponse.Pair.Transactions(
			hours24 = GetTokenPairsInfoResponse.Pair.Transactions.BuySellInfo(
				buys = 218,
				sells = 198,
			),
			hours06 = GetTokenPairsInfoResponse.Pair.Transactions.BuySellInfo(
				buys = 78,
				sells = 95,
			),
			hours01 = GetTokenPairsInfoResponse.Pair.Transactions.BuySellInfo(
				buys = 10,
				sells = 8,
			),
			minutes05 = GetTokenPairsInfoResponse.Pair.Transactions.BuySellInfo(
				buys = 1,
				sells = 0,
			),
		),
		pairCreatedAtMillis = pairCreatedAt.toEpochMilli(),
		info = null,
	)
}
