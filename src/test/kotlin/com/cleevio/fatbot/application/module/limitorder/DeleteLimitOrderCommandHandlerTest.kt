package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.limitorder.command.DeleteLimitOrderCommand
import com.cleevio.fatbot.application.module.limitorder.exception.LimitOrderLockedException
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderRepository
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.toSolanaAddress
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigInteger

class DeleteLimitOrderCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val limitOrderRepository: LimitOrderRepository,
) : IntegrationTest() {

	@Test
	fun `should delete limit order`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getLimitOrder(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			walletId = 2.toUUID(),
			tokenAddress = "a".toSolanaAddress(),
			limitPrice = BigInteger.valueOf(1000000000000000L),
			initialAmount = BigInteger.valueOf(1000000000000000000L),
			type = LimitOrderType.BUY,
			chain = Chain.SOLANA,
		)

		// when
		commandBus(
			DeleteLimitOrderCommand(
				userId = 1.toUUID(),
				limitOrderId = 3.toUUID(),
			),
		)

		// then
		limitOrderRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should throw when deleting locked limit order`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getLimitOrder(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			walletId = 2.toUUID(),
			tokenAddress = "a".toSolanaAddress(),
			limitPrice = BigInteger.valueOf(1000000000000000L),
			initialAmount = BigInteger.valueOf(1000000000000000000L),
			type = LimitOrderType.BUY,
			chain = Chain.SOLANA,
			beforeSave = { lock() },
		)

		shouldThrow<LimitOrderLockedException> {
			commandBus(
				DeleteLimitOrderCommand(
					userId = 1.toUUID(),
					limitOrderId = 3.toUUID(),
				),
			)
		}
	}
}
