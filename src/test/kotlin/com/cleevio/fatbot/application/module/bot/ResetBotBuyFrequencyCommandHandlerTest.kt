package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.bot.command.ResetBotBuyFrequencyCommand
import com.cleevio.fatbot.domain.bot.BotRepository
import com.cleevio.fatbot.setAndReturnPrivateProperty
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import kotlin.test.Test

class ResetBotBuyFrequencyCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val botRepository: BotRepository,
) : IntegrationTest() {
	@Test
	fun `should reset remaining buy frequency for bot`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 20.toUUID())

		integrationTestHelper.getBot(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 20.toUUID(),
			profitTargetFraction = BigDecimal("0.30"),
			stopLossFraction = BigDecimal("0.15"),
			tradeAmount = BigDecimal("500.4"),
			buyFrequency = 15,
			beforeSave = { setAndReturnPrivateProperty("remainingBuyFrequency", 10) },
		)

		// when
		commandBus(ResetBotBuyFrequencyCommand(userId = 1.toUUID(), botId = 2.toUUID()))

		// then
		val bots = botRepository.findAll()
		bots.size shouldBe 1

		val updatedBot = bots.first()
		updatedBot.id shouldBe 2.toUUID()
		updatedBot.remainingBuyFrequency shouldBe 15
	}
}
