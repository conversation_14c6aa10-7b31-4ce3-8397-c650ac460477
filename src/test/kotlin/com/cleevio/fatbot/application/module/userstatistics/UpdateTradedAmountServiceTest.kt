package com.cleevio.fatbot.application.module.userstatistics

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.module.userstatistics.service.UpdateTradedAmountService
import com.cleevio.fatbot.domain.userfattycard.UserFattyCardRepository
import com.cleevio.fatbot.domain.userstatistics.StreakState
import com.cleevio.fatbot.domain.userstatistics.UserStatisticsRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class UpdateTradedAmountServiceTest(
	@Autowired private val underTest: UpdateTradedAmountService,
	@Autowired private val userStatisticsRepository: UserStatisticsRepository,
	@Autowired private val userFattyCardRepository: UserFattyCardRepository,
) : IntegrationTest() {

	@Test
	fun `should update traded amount and create fatty cards`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(userId = 1.toUUID())
		integrationTestHelper.generateFattyCards()

		underTest.update(1.toUUID(), 10000.toBigDecimal())

		userStatisticsRepository.findByUserId(1.toUUID())!!.run {
			tradedAmountInDay shouldBeEqualComparingTo 10000.toBigDecimal()
			leaderboardDonuts shouldBeEqualComparingTo 10000.toBigDecimal()
			leagueDonuts shouldBeEqualComparingTo 10000.toBigDecimal()
		}
		// Verify fatty cards were created - one card for 100 traded amount
		userFattyCardRepository.findAll().size shouldBe 10
	}

	@Test
	fun `should accumulate traded amounts and create multiple fatty cards`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(userId = 1.toUUID())
		integrationTestHelper.generateFattyCards() // Generate available fatty cards

		underTest.update(1.toUUID(), BigDecimal(2000))

		underTest.update(1.toUUID(), BigDecimal(1003))

		userStatisticsRepository.findByUserId(1.toUUID())!!.run {
			tradedAmountInDay shouldBeEqualComparingTo 3003.toBigDecimal()
			leaderboardDonuts shouldBeEqualComparingTo 3003.toBigDecimal()
			leagueDonuts shouldBeEqualComparingTo 3003.toBigDecimal()
			streakState shouldBe StreakState.IN_PROGRESS
			daysInStreak shouldBe 1
			lastStreakStartedAt shouldNotBe null
		}
		// Verify multiple fatty cards were created
		userFattyCardRepository.findAll().size shouldBe 3
	}

	@Test
	fun `should handle zero traded amount`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(userId = 1.toUUID())
		integrationTestHelper.generateFattyCards() // Generate available fatty cards

		underTest.update(1.toUUID(), BigDecimal.ZERO)

		userStatisticsRepository.findByUserId(1.toUUID())!!.run {
			tradedAmountInDay shouldBeEqualComparingTo BigDecimal.ZERO
			leaderboardDonuts shouldBeEqualComparingTo BigDecimal.ZERO
			leagueDonuts shouldBeEqualComparingTo BigDecimal.ZERO
		}
		userFattyCardRepository.findAll().size shouldBe 0
	}
}
