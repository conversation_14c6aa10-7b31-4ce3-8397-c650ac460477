package com.cleevio.fatbot.application.module.wallet.service

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.p2p.solanaj.core.PublicKey
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigInteger
import java.util.Optional

class WalletBalanceServiceTest(
	@Autowired private val walletBalanceService: WalletBalanceService,
) : IntegrationTest() {

	@Test
	fun `should get correct balance for ETH wallet`() {
		every {
			web3jWrapper.getWalletBalance(AddressWrapper("******************************************"))
		} returns BigInteger("************000000000")

		val balance = walletBalanceService.getBalance(
			address = AddressWrapper("******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		balance shouldBe BigInteger("************000000000").asBaseAmount()
	}

	@Test
	fun `should get correct balances for ETH wallets`() {
		every {
			web3jWrapper.getWalletBalances(
				listOf(
					AddressWrapper("******************************************"),
					AddressWrapper("******************************************"),
					AddressWrapper("******************************************"),
				),
			)
		} returns listOf(
			BigInteger("************000000000"),
			BigInteger("5324123"),
			BigInteger("3210000000000000000"),
		)

		val inputAddress1 = AddressWrapper("******************************************").toChainAddress(Chain.EVM_MAINNET)
		val inputAddress2 = AddressWrapper("******************************************").toChainAddress(Chain.EVM_MAINNET)
		val inputAddress3 = AddressWrapper("******************************************").toChainAddress(Chain.EVM_MAINNET)
		val balances = walletBalanceService.getBalances(listOf(inputAddress1, inputAddress2, inputAddress3))

		balances shouldBe mapOf(
			inputAddress1 to BigInteger("************000000000").asBaseAmount(),
			inputAddress2 to BigInteger("5324123").asBaseAmount(),
			inputAddress3 to BigInteger("3210000000000000000").asBaseAmount(),
		)
	}

	// currently there is no client for getting solana wallet balance
	@Test
	fun `should get balance for SOL wallet`() {
		every { rpcApi.getBalance(PublicKey("HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH")) } returns ************L

		val balance = walletBalanceService.getBalance(
			address = AddressWrapper(addressString = "HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH"),
			chain = Chain.SOLANA,
		)

		balance shouldBe BigInteger("************").asBaseAmount()
	}

	@Test
	fun `should get balances for SOL and EVM wallets`() {
		every {
			rpcApi.getMultipleAccountsOptional(
				listOf(
					PublicKey("HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH"),
					PublicKey("9rP1kBpqtSp9d78j9UMtpyEgMbnySVhrJMgJRabxZDtC"),
				),
				any(),
			)
		} returns listOf(
			Optional.of(mockk { every { lamports } returns 1010.0 }),
			Optional.of(mockk { every { lamports } returns 2010.0 }),
		)

		every {
			web3jWrapper.getWalletBalances(listOf(AddressWrapper("******************************************")))
		} returns listOf(BigInteger("************000000000"))

		val sol1 = AddressWrapper("HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH").toChainAddress(Chain.SOLANA)
		val sol2 = AddressWrapper("9rP1kBpqtSp9d78j9UMtpyEgMbnySVhrJMgJRabxZDtC").toChainAddress(Chain.SOLANA)
		val evm1 = AddressWrapper("******************************************").toChainAddress(Chain.EVM_MAINNET)

		val inputs = listOf(sol1, sol2, evm1)
		val balances = walletBalanceService.getBalances(inputs)

		balances shouldBe mapOf(
			sol1 to BigInteger("1010").asBaseAmount(),
			sol2 to BigInteger("2010").asBaseAmount(),
			evm1 to BigInteger("************000000000").asBaseAmount(),
		)
	}

	@Test
	fun `should return 0 non-existing accounts`() {
		every {
			rpcApi.getMultipleAccountsOptional(
				listOf(
					PublicKey("HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH"),
					PublicKey("9rP1kBpqtSp9d78j9UMtpyEgMbnySVhrJMgJRabxZDtC"),
				),
				any(),
			)
		} returns listOf(
			Optional.of(mockk { every { lamports } returns 1010.0 }),
			Optional.empty(),
		)

		val sol1 = AddressWrapper("HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH").toChainAddress(Chain.SOLANA)
		val sol2 = AddressWrapper("9rP1kBpqtSp9d78j9UMtpyEgMbnySVhrJMgJRabxZDtC").toChainAddress(Chain.SOLANA)

		val inputs = listOf(sol1, sol2)
		val balances = walletBalanceService.getBalances(inputs)

		balances shouldBe mapOf(
			sol1 to BigInteger("1010").asBaseAmount(),
			sol2 to BigInteger("0").asBaseAmount(),
		)
	}
}
