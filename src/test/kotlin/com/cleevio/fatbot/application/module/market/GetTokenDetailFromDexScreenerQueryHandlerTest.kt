package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.adapter.out.dexscreener.response.GetTokenPairsInfoResponse
import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector
import com.cleevio.fatbot.adapter.out.evm.context.UniswapV3Fee
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.query.GetTokenDetailQuery
import com.cleevio.fatbot.application.module.token.event.NewEvmTokenInfoCreatedEvent
import com.cleevio.fatbot.application.module.token.exception.TokenNotFoundException
import com.cleevio.fatbot.application.module.tokenaudit.port.out.PerformTokenAudit
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.token.TokenPairInfoRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit

class GetTokenDetailFromDexScreenerQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val integrationTestClock: IntegrationTestClock,
	@Autowired private val tokenPairInfoRepository: TokenPairInfoRepository,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		// Capture EvmTokenInfoCreatedEvent -> Stops backfilling of prices
		every { emitApplicationEvent<NewEvmTokenInfoCreatedEvent>() } just Runs
	}

	@Test
	fun `should add correct price from fatbotUtil contract to response`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		every {
			dexScreenerConnector.getTokenPairsInfoByAddress(AddressWrapper("******************************************"))
		} returns GetTokenPairsInfoResponse(
			pairs = listOf(getDefaultTokenPairResponse()),
		)

		every {
			etherscanConnector.getSourceCode(any(), any())
		} returns EtherscanConnector.GetSourceCodeResultResponse(
			sourceCode = "sourceCode",
			proxy = "0",
			abi = "[ABI]",
			implementation = "",
		)

		every {
			openAIConnector.responses(any(), PerformTokenAudit.Result::class.java)
		} returns PerformTokenAudit.Result(
			issues = listOf(PerformTokenAudit.AuditIssue("test", "test", PerformTokenAudit.IssueSeverity.MEDIUM)),
			riskFactor = PerformTokenAudit.RiskFactor.RED,
			riskFactorReason = "Test",
		)

		every {
			etherscanConnector.getSourceCode(any(), any())
		} returns EtherscanConnector.GetSourceCodeResultResponse(
			sourceCode = "sourceCode",
			proxy = "0",
			abi = "[ABI]",
			implementation = "",
		)

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0xe016d3ab" +
					"0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000001" +
					"0000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			// 3084000000000000 decimal in Wei, which is 0.003084 ETH
			"000000000000000000000000000000000000000000000000000AF4E1B47CC000"

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0x313ce567",
				DefaultBlockParameterName.PENDING,
			)
		} returns "0x0000012" // hex of decimal 18

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0xddca3f43", // UniswapV3 pool fee selector
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00064" // hex of V3Pool fee 100

		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		val result = queryBus(
			GetTokenDetailQuery.asSignedUser(
				chain = Chain.EVM_MAINNET,
				userId = user.id,
				tokenAddress = AddressWrapper("******************************************"),
			),
		)

		with(result.tokenInfo as GetTokenDetailQuery.DexDetail) {
			dexInfo.priceNative shouldBe BigDecimal("0.003084000000000000")
			dexInfo.priceUsd shouldBe BigDecimal("7.401603084000000000000")
		}
	}

	@Test
	fun `should work with anonymous user`() {
		every {
			dexScreenerConnector.getTokenPairsInfoByAddress(AddressWrapper("******************************************"))
		} returns GetTokenPairsInfoResponse(pairs = listOf(getDefaultTokenPairResponse()))

		every {
			etherscanConnector.getSourceCode(any(), any())
		} returns EtherscanConnector.GetSourceCodeResultResponse(
			sourceCode = "sourceCode",
			proxy = "0",
			abi = "[ABI]",
			implementation = "",
		)

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0x313ce567",
				DefaultBlockParameterName.PENDING,
			)
		} returns "0x0000012" // hex of decimal 18

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0xddca3f43", // UniswapV3 pool fee selector
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00064" // hex of V3Pool fee 100

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0xe016d3ab" +
					"0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000001" +
					"0000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			// 3084000000000000 decimal in Wei, which is 0.003084 ETH
			"000000000000000000000000000000000000000000000000000AF4E1B47CC000"

		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		every {
			etherscanConnector.getSourceCode(any(), any())
		} returns EtherscanConnector.GetSourceCodeResultResponse(
			sourceCode = "sourceCode",
			proxy = "0",
			abi = "[ABI]",
			implementation = "",
		)

		every {
			openAIConnector.responses(any(), PerformTokenAudit.Result::class.java)
		} returns PerformTokenAudit.Result(
			issues = listOf(PerformTokenAudit.AuditIssue("test", "test", PerformTokenAudit.IssueSeverity.MEDIUM)),
			riskFactor = PerformTokenAudit.RiskFactor.RED,
			riskFactorReason = "Test",
		)

		queryBus(
			GetTokenDetailQuery.asAnonymousUser(
				chain = Chain.EVM_MAINNET,
				tokenAddress = AddressWrapper("******************************************"),
			),
		)
	}

	@Test
	fun `should get token info and cache it for 1 minute and create token pair info`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		every {
			dexScreenerConnector.getTokenPairsInfoByAddress(AddressWrapper("******************************************"))
		} returns GetTokenPairsInfoResponse(
			pairs = listOf(getDefaultTokenPairResponse()),
		)

		every {
			etherscanConnector.getSourceCode(any(), any())
		} returns EtherscanConnector.GetSourceCodeResultResponse(
			sourceCode = "sourceCode",
			proxy = "0",
			abi = "[ABI]",
			implementation = "",
		)

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0x313ce567",
				DefaultBlockParameterName.PENDING,
			)
		} returns "0x12" // hex of decimal 18

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0xddca3f43", // UniswapV3 pool fee selector
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00064" // hex of V3Pool fee 100

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"******************************************",
				"0xe016d3ab" +
					"0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000001" +
					"0000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000001f9840a85d5af5bf1d1762f925bdaddc4201f984" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			// 3084000000000000 decimal in Wei, which is 0.003084 ETH
			"000000000000000000000000000000000000000000000000000AF4E1B47CC000"

		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		every {
			etherscanConnector.getSourceCode(any(), any())
		} returns EtherscanConnector.GetSourceCodeResultResponse(
			sourceCode = "sourceCode",
			proxy = "0",
			abi = "[ABI]",
			implementation = "",
		)

		every {
			openAIConnector.responses(any(), PerformTokenAudit.Result::class.java)
		} returns PerformTokenAudit.Result(
			issues = listOf(PerformTokenAudit.AuditIssue("test", "test", PerformTokenAudit.IssueSeverity.MEDIUM)),
			riskFactor = PerformTokenAudit.RiskFactor.RED,
			riskFactorReason = "Test",
		)

		queryBus(
			GetTokenDetailQuery.asSignedUser(
				chain = Chain.EVM_MAINNET,
				user.id,
				AddressWrapper("******************************************"),
			),
		)
		queryBus(
			GetTokenDetailQuery.asSignedUser(
				chain = Chain.EVM_MAINNET,
				user.id,
				AddressWrapper("******************************************"),
			),
		)

		verify(exactly = 1) { dexScreenerConnector.getTokenPairsInfoByAddress(any()) }

		integrationTestClock.advanceBy(amountToAdd = 1, ChronoUnit.MINUTES)

		queryBus(
			GetTokenDetailQuery.asSignedUser(
				chain = Chain.EVM_MAINNET,
				user.id,
				AddressWrapper("******************************************"),
			),
		)
		queryBus(
			GetTokenDetailQuery.asSignedUser(
				chain = Chain.EVM_MAINNET,
				user.id,
				AddressWrapper("******************************************"),
			),
		)

		verify(exactly = 2) { dexScreenerConnector.getTokenPairsInfoByAddress(any()) }

		val tokenPairInfos = tokenPairInfoRepository.findAll()
		tokenPairInfos.size shouldBe 1

		with(tokenPairInfos.first()) {
			chain shouldBe Chain.EVM_MAINNET
			dexType shouldBe GetDex.Dex.UNISWAP_V3
			uniswapV3Fee shouldBe UniswapV3Fee.FEE_100
			pairAddress shouldBe AddressWrapper("******************************************")
			tokenAddress shouldBe AddressWrapper("******************************************")
		}
	}

	@Test
	fun `should cache null result of getting token pairs for 1 minute`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		every {
			dexScreenerConnector.getTokenPairsInfoByAddress(AddressWrapper("******************************************"))
		} returns GetTokenPairsInfoResponse(pairs = null)

		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		shouldThrow<TokenNotFoundException> {
			queryBus(
				GetTokenDetailQuery.asSignedUser(
					chain = Chain.EVM_MAINNET,
					user.id,
					AddressWrapper("******************************************"),
				),
			)
		}
		shouldThrow<TokenNotFoundException> {
			queryBus(
				GetTokenDetailQuery.asSignedUser(
					chain = Chain.EVM_MAINNET,
					user.id,
					AddressWrapper("******************************************"),
				),
			)
		}

		verify(exactly = 1) { dexScreenerConnector.getTokenPairsInfoByAddress(any()) }
		integrationTestClock.advanceBy(amountToAdd = 1, ChronoUnit.MINUTES)

		shouldThrow<TokenNotFoundException> {
			queryBus(
				GetTokenDetailQuery.asSignedUser(
					chain = Chain.EVM_MAINNET,
					userId = user.id,
					tokenAddress = AddressWrapper("******************************************"),
				),
			)
		}
		shouldThrow<TokenNotFoundException> {
			queryBus(
				GetTokenDetailQuery.asSignedUser(
					chain = Chain.EVM_MAINNET,
					userId = user.id,
					tokenAddress = AddressWrapper("******************************************"),
				),
			)
		}

		verify(exactly = 2) { dexScreenerConnector.getTokenPairsInfoByAddress(any()) }
	}

	@Test
	fun `should ignore irrelevant token pairs, caching no result for 1 minute and return default info`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		every {
			dexScreenerConnector.getTokenPairsInfoByAddress(AddressWrapper("******************************************"))
		} returns GetTokenPairsInfoResponse(
			pairs = listOf(
				getDefaultTokenPairResponse().copy(chainId = "polygon"),
				getDefaultTokenPairResponse().copy(dexId = "9mm"),
				getDefaultTokenPairResponse().copy(
					quoteToken = GetTokenPairsInfoResponse.Pair.Token(
						address = AddressWrapper("******************************************"),
						name = "Wrapped BTC",
						symbol = "WBTC",
					),
				),
			),
		)

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0x313ce567", // decimals selector
				DefaultBlockParameterName.PENDING,
			)
		} returns "0x0000012" // hex of decimal 18

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0x18160ddd", // total supply selector
				DefaultBlockParameterName.PENDING,
			)
		} returns "0x0000010" // hex of decimal 16

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0x8da5cb5b", // owner selector
				DefaultBlockParameterName.PENDING,
			)
		} returns "0x00000000000000000000000090892282A68DAd38ac77Cec3e80D008210830b5B" // address

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0x06fdde03", // name selector
				DefaultBlockParameterName.PENDING,
			)
		} returns "0x0000000000000000000000000000000000000000000000000000000000000020" +
			"0000000000000000000000000000000000000000000000000000000000000010" +
			"467265642054686520526163636f6f6e00000000000000000000000000000000"

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0x95d89b41", // symbol selector
				DefaultBlockParameterName.PENDING,
			)
		} returns "0x0000000000000000000000000000000000000000000000000000000000000020" +
			"0000000000000000000000000000000000000000000000000000000000000004" +
			"4652454400000000000000000000000000000000000000000000000000000000"

		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		val result = queryBus(
			GetTokenDetailQuery.asSignedUser(
				chain = Chain.EVM_MAINNET,
				userId = user.id,
				tokenAddress = AddressWrapper("******************************************"),
			),
		)
		queryBus(
			GetTokenDetailQuery.asSignedUser(
				chain = Chain.EVM_MAINNET,
				user.id,
				AddressWrapper("******************************************"),
			),
		)

		with(result.tokenInfo as GetTokenDetailQuery.NonDexDetail) {
			isOnDex shouldBe false
			nonDexInfo shouldBe GetTokenDetailQuery.NonDexInfo(
				name = "Fred The Raccoon",
				symbol = "FRED",
			)
		}

		verify(exactly = 1) { dexScreenerConnector.getTokenPairsInfoByAddress(any()) }
	}

	@Test
	fun `should throw when address is not of token contract`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		every {
			dexScreenerConnector.getTokenPairsInfoByAddress(AddressWrapper("******************************************"))
		} returns GetTokenPairsInfoResponse(pairs = null)

		every {
			readOnlyTransactionManager.sendCall(
				"0x000000000000000000000000000000000000dEaD",
				"0x313ce567", // decimals selector
				DefaultBlockParameterName.PENDING,
			)
		} throws Exception()

		shouldThrow<TokenNotFoundException> {
			queryBus(
				GetTokenDetailQuery.asSignedUser(
					chain = Chain.EVM_MAINNET,
					userId = user.id,
					tokenAddress = AddressWrapper("******************************************"),
				),
			)
		}

		verify(exactly = 1) { dexScreenerConnector.getTokenPairsInfoByAddress(any()) }
	}

	@Test
	fun `should return NonDexDetail if token has been saved in the database`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("2kHJG52SfrNouYDwTj6SmL3gSoMbe9NNdc8WCcdLpump"),
			name = "SavedToken",
			symbol = "ST",
		)

		every {
			dexScreenerConnector.getTokenPairsInfoByAddress(
				AddressWrapper("2kHJG52SfrNouYDwTj6SmL3gSoMbe9NNdc8WCcdLpump"),
			)
		} returns GetTokenPairsInfoResponse(pairs = emptyList())

		val result = queryBus(
			GetTokenDetailQuery.asSignedUser(
				chain = Chain.SOLANA,
				userId = user.id,
				tokenAddress = AddressWrapper("2kHJG52SfrNouYDwTj6SmL3gSoMbe9NNdc8WCcdLpump"),
			),
		)

		val tokenInfo = result.tokenInfo as? GetTokenDetailQuery.NonDexDetail
		tokenInfo shouldNotBe null

		tokenInfo!!.run {
			isOnDex shouldBe false
			nonDexInfo.name shouldBe "SavedToken"
			nonDexInfo.symbol shouldBe "ST"
		}
	}
}

private fun getDefaultTokenPairResponse() = GetTokenPairsInfoResponse.Pair(
	chainId = "ethereum",
	dexId = "uniswap",
	url = "https://dexscreener.com/ethereum/******************************************",
	pairAddress = AddressWrapper("******************************************").getAddressString(),
	baseToken = GetTokenPairsInfoResponse.Pair.Token(
		address = AddressWrapper("******************************************"),
		name = "Uniswap",
		symbol = "UNI",
	),
	quoteToken = GetTokenPairsInfoResponse.Pair.Token(
		address = AddressWrapper("******************************************"),
		name = "Wrapper Ether",
		symbol = "WETH",
	),
	labels = listOf("v3"),
	volume = GetTokenPairsInfoResponse.Pair.TimeIntervals(
		hours24 = BigDecimal("9183404.54"),
		hours06 = BigDecimal("5982758.78"),
		hours01 = BigDecimal("288870.23"),
		minutes05 = BigDecimal("12778.35"),
	),
	priceChange = GetTokenPairsInfoResponse.Pair.TimeIntervals(
		hours24 = BigDecimal("6.94"),
		hours06 = BigDecimal("2.52"),
		hours01 = BigDecimal("0.18"),
		minutes05 = BigDecimal("0.09"),
	),
	fullyDilutedValue = BigDecimal("8126763163"),
	marketCap = BigDecimal("6126023505"),
	liquidity = GetTokenPairsInfoResponse.Pair.Liquidity(
		usd = BigDecimal("27458979.31"),
		base = BigDecimal("2358945"),
		quote = BigDecimal("3145.4293"),
	),
	transactions = GetTokenPairsInfoResponse.Pair.Transactions(
		hours24 = GetTokenPairsInfoResponse.Pair.Transactions.BuySellInfo(
			buys = 218,
			sells = 198,
		),
		hours06 = GetTokenPairsInfoResponse.Pair.Transactions.BuySellInfo(
			buys = 78,
			sells = 95,
		),
		hours01 = GetTokenPairsInfoResponse.Pair.Transactions.BuySellInfo(
			buys = 10,
			sells = 8,
		),
		minutes05 = GetTokenPairsInfoResponse.Pair.Transactions.BuySellInfo(
			buys = 1,
			sells = 0,
		),
	),
	pairCreatedAtMillis = Instant.parse("2024-08-29T10:00:01Z").toEpochMilli(),
	info = null,
)
