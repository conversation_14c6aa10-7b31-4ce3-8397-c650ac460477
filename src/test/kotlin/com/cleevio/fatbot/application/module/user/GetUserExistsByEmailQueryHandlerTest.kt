package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.user.query.GetUserExistsByEmailQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetUserExistsByEmailQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return true for existing user`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID(), email = "<EMAIL>")

		// when
		val result = queryBus(GetUserExistsByEmailQuery(email = "<EMAIL>"))

		// then
		result.exists shouldBe true
	}

	@Test
	fun `should return false for not existing user`() {
		// given and when
		val result = queryBus(GetUserExistsByEmailQuery(email = "<EMAIL>"))

		// then
		result.exists shouldBe false
	}
}
