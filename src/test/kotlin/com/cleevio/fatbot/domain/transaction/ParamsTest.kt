package com.cleevio.fatbot.domain.transaction

import com.cleevio.fatbot.application.common.crypto.Wei
import com.cleevio.fatbot.application.common.crypto.asSignedTx
import com.cleevio.fatbot.application.common.crypto.asWei
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailureReason
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.setAndReturnPrivateProperty
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.platform.commons.annotation.Testable
import java.util.UUID

@Testable
class ParamsTest {

	@Test
	fun `should throw exception because current status is not PENDING`() {
		val transaction = createTransaction(status = TransactionStatus.SUCCESS)
		transaction.status shouldBe TransactionStatus.SUCCESS

		shouldThrow<IllegalArgumentException> {
			transaction.markAsFailed(failReason = TransactionFailureReason.UNDEFINED)
		}

		transaction.status shouldBe TransactionStatus.SUCCESS
	}

	@Test
	fun `should increment verificationCount because new status is null`() {
		val transaction = createTransaction()
		transaction.status shouldBe TransactionStatus.PENDING
		transaction.verificationCount shouldBe 0

		transaction.increaseTryCount(verificationLimit = 20)

		transaction.status shouldBe TransactionStatus.PENDING
		transaction.verificationCount shouldBe 1
	}

	@Test
	fun `should update status to FAILED because verificationCount was exceeded`() {
		val transaction = createTransaction()
		transaction.status shouldBe TransactionStatus.PENDING
		transaction.verificationCount shouldBe 0

		transaction.increaseTryCount(verificationLimit = 1)
		transaction.status shouldBe TransactionStatus.PENDING
		transaction.verificationCount shouldBe 1

		transaction.increaseTryCount(verificationLimit = 1)
		transaction.status shouldBe TransactionStatus.FAILED
		transaction.verificationCount shouldBe 2
	}
}

private fun createTransaction(
	type: TransactionType = TransactionType.BUY,
	nonce: Long = 0L,
	ethAmount: Wei = 10000000000000000L.asWei(),
	signedTx: String = "dummySignedTx",
	tokenAddress: AddressWrapper = AddressWrapper("******************************************"),
	chainId: Long = 1,
	entityModifier: (Transaction) -> Unit = {},
	status: TransactionStatus? = null,
) = Transaction.ofEvm(
	walletId = UUID.randomUUID(),
	type = type,
	nonce = nonce,
	baseValue = ethAmount,
	signedTx = signedTx.asSignedTx(),
	tokenAddress = tokenAddress,
	chainId = chainId,
	limitOrderId = null,
).also {
	status?.let { status -> it.setAndReturnPrivateProperty("status", status) }
	entityModifier(it)
}
