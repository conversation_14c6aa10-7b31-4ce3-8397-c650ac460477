package com.cleevio.fatbot

import com.cleevio.fatbot.infrastructure.config.FatbotDispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestCoroutineScheduler
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary

@TestConfiguration
class IntegrationTestCoroutineConfig {

	private val scheduler = TestCoroutineScheduler()

	@Bean
	fun testCoroutineScheduler() = scheduler

	@OptIn(ExperimentalCoroutinesApi::class)
	@Bean
	@Primary
	fun testFatbotDispatchers(): FatbotDispatchers = FatbotDispatchers(
		IO = StandardTestDispatcher(scheduler),
		Caffeine = UnconfinedTestDispatcher(scheduler),
	)
}
