package com.cleevio.fatbot

import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import okhttp3.internal.toHexString
import org.springframework.context.PayloadApplicationEvent
import org.springframework.core.ResolvableType
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.util.AopTestUtils
import org.springframework.test.web.servlet.MockHttpServletRequestDsl
import org.springframework.test.web.servlet.MockMvcResultMatchersDsl
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.ReflectionUtils
import java.math.BigDecimal
import java.util.UUID
import kotlin.reflect.KClass

internal fun MockHttpServletRequestDsl.addBearerAuthHeader(accessToken: String) =
	header(HttpHeaders.AUTHORIZATION, "Bearer $accessToken")

internal fun MockHttpServletRequestDsl.addAcceptHeader(value: String) = header(HttpHeaders.ACCEPT, value)

internal fun MockHttpServletRequestDsl.addJsonContent(value: String) {
	this.contentType = MediaType.APPLICATION_JSON
	this.content = value
}

internal fun MockHttpServletRequestDsl.addParams(params: LinkedMultiValueMap<String, String>) {
	this.params = params
}

internal fun MockMvcResultMatchersDsl.jsonContent(
	jsonContent: String,
	contentType: String = ApiVersion.VERSION_1_JSON,
	strict: Boolean = false,
) {
	this.content {
		contentType(contentType)
		json(jsonContent.trimIndent(), strict)
	}
}

internal fun String.urlEncode() = this.replace("?", "%3F")

internal fun Int.toUUID(): UUID {
	assert(this >= 0)
	return createUuidFromNumber(this.toLong())
}

private fun createUuidFromNumber(number: Long): UUID {
	val uuidString = String.format("00000000-000-0000-0000-%012d", number)
	return UUID.fromString(uuidString.replaceRange(9, 12, "000"))
}

internal fun Int.toAddress(): AddressWrapper {
	assert(this >= 0)
	return AddressWrapper(createAddressHexStringFromNumber(this))
}

internal fun String.toAddress(): AddressWrapper {
	return (AddressWrapper("0x" + this.padEnd(length = 40, padChar = '0')))
}

internal fun String.toSolanaAddress(): AddressWrapper {
	val hex = this.padEnd(64, '0')
	val bytes = hex.chunked(2).map { it.toInt(16).toByte() }.toByteArray()
	return AddressWrapper(bytes)
}

private fun createAddressHexStringFromNumber(number: Int): String {
	return "0x" + number.toHexString().padStart(length = 40, padChar = '0')
}

internal fun Double.toBigInteger() = this.toBigDecimal().toBigInteger()

internal infix fun NativeAmount.shouldBeEqualComparingTo(value: BigDecimal) = this.amount shouldBeEqualComparingTo value

internal fun <T : Any> T.setAndReturnPrivateProperty(variableName: String, data: Any?): Any? {
	val field = ReflectionUtils.findField(javaClass, variableName) ?: throw Exception("No such field $variableName")
	return field.let {
		it.isAccessible = true
		it.set(this, data)
		return@let it.get(this)
	}
}

internal fun <T : Any> T.setAndReturnPrivatePropertyOfProxy(variableName: String, data: Any?): Any? {
	val proxy = AopTestUtils.getTargetObject<T>(this)

	return proxy.setAndReturnPrivateProperty(variableName, data)
}

internal fun KClass<*>.asEventPayload() =
	ResolvableType.forClassWithGenerics(PayloadApplicationEvent::class.java, this.java)
