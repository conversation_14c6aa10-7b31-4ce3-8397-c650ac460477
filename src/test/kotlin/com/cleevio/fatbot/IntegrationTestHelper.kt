package com.cleevio.fatbot

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.adapter.out.evm.context.UniswapV3Fee
import com.cleevio.fatbot.application.common.crypto.Lamport
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.Wei
import com.cleevio.fatbot.application.common.crypto.asLamport
import com.cleevio.fatbot.application.common.crypto.asSignedTx
import com.cleevio.fatbot.application.common.crypto.asTxHash
import com.cleevio.fatbot.application.common.crypto.asWei
import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.domain.bot.Bot
import com.cleevio.fatbot.domain.bot.BotRepository
import com.cleevio.fatbot.domain.botdraft.BotDraft
import com.cleevio.fatbot.domain.botdraft.BotDraftRepository
import com.cleevio.fatbot.domain.botleaderboard.BotLeaderboardSnapshot
import com.cleevio.fatbot.domain.botleaderboard.BotLeaderboardSnapshotRepository
import com.cleevio.fatbot.domain.botmarket.BotMarketPosition
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionRepository
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeState
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateRepository
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateType
import com.cleevio.fatbot.domain.botportfolio.BotPortfolioValueSnapshot
import com.cleevio.fatbot.domain.botportfolio.BotPortfolioValueSnapshotRepository
import com.cleevio.fatbot.domain.bottransaction.BotTransaction
import com.cleevio.fatbot.domain.bottransaction.BotTransactionRepository
import com.cleevio.fatbot.domain.botwallet.BotWallet
import com.cleevio.fatbot.domain.botwallet.BotWalletRepository
import com.cleevio.fatbot.domain.exchangerate.ExchangeRateSnapshot
import com.cleevio.fatbot.domain.exchangerate.ExchangeRateSnapshotRepository
import com.cleevio.fatbot.domain.fattycard.FattyCard
import com.cleevio.fatbot.domain.fattycard.FattyCardRepository
import com.cleevio.fatbot.domain.fattyleagueseason.FattyLeagueSeason
import com.cleevio.fatbot.domain.fattyleagueseason.FattyLeagueSeasonRepository
import com.cleevio.fatbot.domain.file.File
import com.cleevio.fatbot.domain.file.FileRepository
import com.cleevio.fatbot.domain.gassnapshot.GasInfoSnapshot
import com.cleevio.fatbot.domain.gassnapshot.GasInfoSnapshotRepository
import com.cleevio.fatbot.domain.limitorder.LimitOrder
import com.cleevio.fatbot.domain.limitorder.LimitOrderRepository
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.domain.market.MarketPosition
import com.cleevio.fatbot.domain.market.MarketPositionRefresh
import com.cleevio.fatbot.domain.market.MarketPositionRefreshRepository
import com.cleevio.fatbot.domain.market.MarketPositionRepository
import com.cleevio.fatbot.domain.market.MarketPositionSnapshot
import com.cleevio.fatbot.domain.market.MarketPositionSnapshotRepository
import com.cleevio.fatbot.domain.referral.ReferralReward
import com.cleevio.fatbot.domain.referral.ReferralRewardRepository
import com.cleevio.fatbot.domain.token.BotTokenInfo
import com.cleevio.fatbot.domain.token.BotTokenInfoRepository
import com.cleevio.fatbot.domain.token.EvmTokenInfo
import com.cleevio.fatbot.domain.token.EvmTokenInfoRepository
import com.cleevio.fatbot.domain.token.RaydiumAmmMarketData
import com.cleevio.fatbot.domain.token.RaydiumCPMMMarketData
import com.cleevio.fatbot.domain.token.TokenPairInfo
import com.cleevio.fatbot.domain.token.TokenPairInfoRepository
import com.cleevio.fatbot.domain.tokenaudit.TokenAudit
import com.cleevio.fatbot.domain.tokenaudit.TokenAuditRepository
import com.cleevio.fatbot.domain.tokenprice.TokenPriceRepository
import com.cleevio.fatbot.domain.tokenprice.TokenPriceSnapshot
import com.cleevio.fatbot.domain.transaction.Transaction
import com.cleevio.fatbot.domain.transaction.TransactionRepository
import com.cleevio.fatbot.domain.user.FirebaseUser
import com.cleevio.fatbot.domain.user.FirebaseUserRepository
import com.cleevio.fatbot.domain.user.Language
import com.cleevio.fatbot.domain.userfattycard.UserFattyCard
import com.cleevio.fatbot.domain.userfattycard.UserFattyCardRepository
import com.cleevio.fatbot.domain.userfattyleagueseason.UserFattyLeagueSeason
import com.cleevio.fatbot.domain.userfattyleagueseason.UserFattyLeagueSeasonRepository
import com.cleevio.fatbot.domain.userleaderboad.UserLeaderBoardRepository
import com.cleevio.fatbot.domain.userleaderboad.UserLeaderboard
import com.cleevio.fatbot.domain.userstatistics.UserStatistics
import com.cleevio.fatbot.domain.userstatistics.UserStatisticsRepository
import com.cleevio.fatbot.domain.wallet.Wallet
import com.cleevio.fatbot.domain.wallet.WalletRepository
import com.cleevio.fatbot.domain.walletposition.WalletCurrencyPosition
import com.cleevio.fatbot.domain.walletposition.WalletCurrencyPositionRepository
import com.cleevio.fatbot.tables.references.FIREBASE_USER
import org.jooq.DSLContext
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

@Service
class IntegrationTestHelper(
	private val firebaseUserRepository: FirebaseUserRepository,
	private val walletRepository: WalletRepository,
	private val transactionRepository: TransactionRepository,
	private val evmTokenInfoRepository: EvmTokenInfoRepository,
	private val marketPositionRepository: MarketPositionRepository,
	private val marketPositionSnapshotRepository: MarketPositionSnapshotRepository,
	private val referralRewardRepository: ReferralRewardRepository,
	private val gasInfoSnapshotRepository: GasInfoSnapshotRepository,
	private val fileRepository: FileRepository,
	private val tokenPriceRepository: TokenPriceRepository,
	private val walletCurrencyPositionRepository: WalletCurrencyPositionRepository,
	private val exchangeRateSnapshotRepository: ExchangeRateSnapshotRepository,
	private val botDraftRepository: BotDraftRepository,
	private val botRepository: BotRepository,
	private val botWalletRepository: BotWalletRepository,
	private val botTransactionRepository: BotTransactionRepository,
	private val marketPositionRefreshRepository: MarketPositionRefreshRepository,
	private val tokenPairInfoRepository: TokenPairInfoRepository,
	private val botMarketPositionRepository: BotMarketPositionRepository,
	private val botTokenInfoRepository: BotTokenInfoRepository,
	private val botPortfolioValueSnapshotRepository: BotPortfolioValueSnapshotRepository,
	private val userStatisticsRepository: UserStatisticsRepository,
	private val userLeaderBoardRepository: UserLeaderBoardRepository,
	private val fattyCardRepository: FattyCardRepository,
	private val userFattyCardRepository: UserFattyCardRepository,
	private val botLeaderboardSnapshotRepository: BotLeaderboardSnapshotRepository,
	private val botMarketPositionTradeStateRepository: BotMarketPositionTradeStateRepository,
	private val tokenAuditRepository: TokenAuditRepository,
	private val fattyLeagueSeasonRepository: FattyLeagueSeasonRepository,
	private val userFattyLeagueSeasonRepository: UserFattyLeagueSeasonRepository,
	private val limitOrderRepository: LimitOrderRepository,
	private val dslContext: DSLContext,
) {

	fun getFirebaseUser(
		id: UUID,
		email: String = "<EMAIL>",
		referralCode: String? = null,
		referredByUserId: UUID? = null,
		quickBuyAmountUsd: BigDecimal = BigDecimal.TEN,
		selectedChains: Set<Chain> = Chain.entries.mapToSet { it },
		language: Language = Language.ENGLISH,
	) = firebaseUserRepository.save(
		FirebaseUser(
			id = id,
			email = email,
			referralCode = referralCode,
			referredByUserId = referredByUserId,
			quickBuyAmountUsd = quickBuyAmountUsd,
			selectedChains = selectedChains,
			language = language,
		),
	)

	fun getWallet(
		id: UUID = UUIDv7(),
		userId: UUID,
		address: AddressWrapper = AddressWrapper(addressString = "******************************************"),
		privateKey: String = "547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
		chain: Chain = Chain.EVM_MAINNET,
		walletCount: Int = 0,
		beforeSave: Wallet.() -> Unit = {},
	) = walletRepository.save(
		Wallet(
			id = id,
			userId = userId,
			address = address,
			privateKey = privateKey,
			chain = chain,
			walletCount = walletCount,
		)
			.also {
				// TODO: Make it so this is not needed e.g. change tests
				// This is unfortunate, but all tests were written with protection default set to FALSE
				// so changing it to TRUE broke couple of them
				if (chain.type == ChainType.EVM) {
					it.patchSettings(
						buyAntiMevProtection = false,
						sellAntiMevProtection = false,
						customName = null,
					)
				}
			}
			.also(beforeSave),
	)

	fun getSolanaWallet(
		id: UUID = UUIDv7(),
		userId: UUID,
		address: AddressWrapper = AddressWrapper(addressString = "2PEfNJ82FGTCyZuyFF4wisSenPWQCyJnt8nNnp3bDdHJ"),
		privateKey: String = "3AiTW34LJjXMSrSvmUgu95bXR11VbzzszdHEq3iC5tLSqjE1gE7xxpFVuTV7gdPjjYHxqWLPWR7A3iAcqtaR2tJt",
		walletCount: Int = 0,
		beforeSave: Wallet.() -> Unit = {},
	) = walletRepository.save(
		Wallet(
			id = id,
			userId = userId,
			address = address,
			privateKey = privateKey,
			chain = Chain.SOLANA,
			walletCount = walletCount,
		).also(beforeSave),
	)

	fun getTrackingTransaction(
		id: UUID = UUIDv7(),
		walletId: UUID,
		type: TransactionType = TransactionType.BUY,
		baseValue: BigInteger = 10000000000000000L.toBigInteger(),
		tokenAddress: AddressWrapper = AddressWrapper("******************************************"),
		chainId: Long = 1L,
		entityModifier: (Transaction) -> Unit = {},
		status: TransactionStatus? = null,
		verificationCount: Int? = null,
		beforeSave: Transaction.() -> Unit = {},
	) = transactionRepository.save(
		Transaction.ofTracking(
			id = id,
			walletId = walletId,
			type = type,
			baseValue = baseValue,
			tokenAddress = tokenAddress,
			chain = Chain.ofEVM(chainId),
		),
	).apply(beforeSave).also {
		status?.let { status -> it.setAndReturnPrivateProperty("status", status) }
		verificationCount?.let { verificationCount ->
			it.setAndReturnPrivateProperty("verificationCount", verificationCount)
		}
		entityModifier(it)
	}

	fun getTransaction(
		id: UUID = UUIDv7(),
		walletId: UUID,
		type: TransactionType = TransactionType.BUY,
		nonce: Long = 0L,
		ethAmount: Wei = 10000000000000000L.asWei(),
		signedTx: String = "dummySignedTx",
		tokenAddress: AddressWrapper? = AddressWrapper("******************************************"),
		chainId: Long = 1L,
		entityModifier: (Transaction) -> Unit = {},
		status: TransactionStatus? = null,
		verificationCount: Int? = null,
		limitOrderId: UUID? = null,
		beforeSave: Transaction.() -> Unit = {},
	) = transactionRepository.save(
		Transaction.ofEvm(
			id = id,
			walletId = walletId,
			type = type,
			nonce = nonce,
			baseValue = ethAmount,
			signedTx = signedTx.asSignedTx(),
			tokenAddress = tokenAddress,
			chainId = chainId,
			limitOrderId = limitOrderId,
		).apply(beforeSave).also {
			status?.let { status -> it.setAndReturnPrivateProperty("status", status) }
			verificationCount?.let { verificationCount ->
				it.setAndReturnPrivateProperty("verificationCount", verificationCount)
			}
			entityModifier(it)
		},
	)

	fun getTransactionSolana(
		id: UUID = UUIDv7(),
		walletId: UUID,
		type: TransactionType = TransactionType.BUY,
		lamportAmount: Lamport = 100000000L.asLamport(),
		signedTx: String = "dummySignedTx",
		signature: String = "dummySignature",
		tokenAddress: AddressWrapper? = AddressWrapper("******************************************"),
		entityModifier: (Transaction) -> Unit = {},
		status: TransactionStatus? = null,
		verificationCount: Int? = null,
		limitOrderId: UUID? = null,
		beforeSave: Transaction.() -> Unit = {},
	) = transactionRepository.save(
		Transaction.ofSvm(
			id = id,
			walletId = walletId,
			type = type,
			baseValue = lamportAmount,
			signedTx = signedTx.asSignedTx(),
			tokenAddress = tokenAddress,
			signature = signature,
			limitOrderId = limitOrderId,
		).apply(beforeSave).also {
			status?.let { status -> it.setAndReturnPrivateProperty("status", status) }
			verificationCount?.let { verificationCount ->
				it.setAndReturnPrivateProperty("verificationCount", verificationCount)
			}
			entityModifier(it)
		},
	)

	fun getEvmTokenInfo(
		id: UUID = UUIDv7(),
		chain: Chain = Chain.EVM_MAINNET,
		tokenAddress: AddressWrapper,
		tokenDecimals: BigInteger = BigInteger.valueOf(18),
		name: String = "CLEEVIO TOKEN",
		symbol: String = "CLEE",
		isToken2022: Boolean = false,
		isVerified: Boolean = true,
		imageFileId: UUID? = null,
	) = evmTokenInfoRepository.save(
		EvmTokenInfo(
			chain = chain,
			id = id,
			address = tokenAddress,
			decimals = tokenDecimals,
			name = name,
			symbol = symbol,
			isToken2022 = isToken2022,
			imageFileId = imageFileId,
			isVerified = isVerified,
		),
	)

	fun getTokenAudit(tokenAddress: AddressWrapper, chain: Chain, entityModifier: (TokenAudit) -> Unit = {}) =
		tokenAuditRepository.save(
			TokenAudit(
				tokenAddress = tokenAddress,
				chain = chain,
			).also(entityModifier),
		)

	fun getBotTokenInfo(
		id: UUID = UUIDv7(),
		chain: Chain = Chain.SOLANA,
		tokenAddress: AddressWrapper,
		tokenDecimals: BigInteger = BigInteger.valueOf(9),
		name: String = "BOT TOKEN",
		symbol: String = "BOT",
		isToken2022: Boolean = true,
		imageFileId: UUID? = null,
		creatorAddress: AddressWrapper = AddressWrapper("AmamPAoP3MwMfGigDiftyLvfLffZjwWGXAWDAcrWFybn"),
	) = botTokenInfoRepository.save(
		BotTokenInfo(
			chain = chain,
			id = id,
			address = tokenAddress,
			decimals = tokenDecimals,
			name = name,
			symbol = symbol,
			isToken2022 = isToken2022,
			creatorAddress = creatorAddress,
		).also {
			it.setAndReturnPrivateProperty("imageFileId", imageFileId)
		},
	)

	fun getMarketPosition(
		id: UUID = UUIDv7(),
		walletId: UUID,
		chain: Chain = Chain.EVM_MAINNET,
		tokenAddress: AddressWrapper,
		beforeSave: MarketPosition.() -> Unit = {},
	) = marketPositionRepository.save(
		MarketPosition(
			id = id,
			walletId = walletId,
			chain = chain,
			tokenAddress = tokenAddress,
		).apply(beforeSave),
	)

	fun getMarketPositionSnapshot(
		walletId: UUID = UUIDv7(),
		chain: Chain = Chain.EVM_MAINNET,
		tokenAddress: AddressWrapper,
		totalTokenAmountBought: BigInteger,
		totalTokenAmountSold: BigInteger,
		totalTokenAcquisitionCostInWei: BigInteger,
		totalTokenAcquisitionCostUsd: BigDecimal,
		totalTokenDispositionCostInWei: BigInteger,
		snapshotMadeAt: Instant = Instant.now(),
	) = marketPositionSnapshotRepository.save(
		MarketPositionSnapshot(
			walletId = walletId,
			chain = chain,
			tokenAddress = tokenAddress,
			snapshotMadeAt = snapshotMadeAt,
			totalTokenAmountBought = totalTokenAmountBought,
			totalTokenAmountSold = totalTokenAmountSold,
			totalTokenAcquisitionCostBaseAmount = totalTokenAcquisitionCostInWei,
			totalTokenDispositionCostBaseAmount = totalTokenDispositionCostInWei,
			totalTokenAcquisitionCostUsd = totalTokenAcquisitionCostUsd,
		),
	)

	fun getReferralReward(
		id: UUID = UUIDv7(),
		userId: UUID,
		txHash: TxHash = TxHash("0xff12d78e8e2d4b53cf14d2daeba5bc5bad47df617178d96949c4e755bbd9346a"),
		chain: Chain = Chain.EVM_MAINNET,
		baseAmount: BigInteger = BigInteger("1000000000000"),
		beforeSave: ReferralReward.() -> Unit = {},
	) = referralRewardRepository.save(
		ReferralReward(
			id = id,
			userId = userId,
			txHash = txHash,
			chain = chain,
			baseAmount = baseAmount,
		).apply(beforeSave),
	)

	fun getGasInfoSnapshot(chainId: Long, maxFeePerGas: BigInteger, maxPriorityFeePerGas: BigInteger) =
		gasInfoSnapshotRepository.save(
			GasInfoSnapshot(
				chainId = chainId,
				maxPriorityFeePerGas = maxPriorityFeePerGas,
				maxFeePerGas = maxFeePerGas,
			),
		)

	fun getFile(id: UUID = UUIDv7(), extension: String = "png") = fileRepository.save(
		File(
			id = id,
			extension = extension,
		),
	)

	fun getTokenPriceSnapshot(
		id: UUID = UUIDv7(),
		tokenAddress: AddressWrapper,
		chain: Chain = Chain.EVM_MAINNET,
		priceWei: BigInteger,
		exchangeRateUsd: BigDecimal,
		validAt: Instant,
	) = tokenPriceRepository.save(
		TokenPriceSnapshot(
			id = id,
			tokenAddress = tokenAddress,
			chain = chain,
			priceWei = priceWei,
			exchangeRateUsd = exchangeRateUsd,
			validAt = validAt,
		),
	)

	fun getWalletCurrencyPosition(
		id: UUID = UUIDv7(),
		walletId: UUID,
		totalBought: BigInteger,
		totalSold: BigInteger,
		totalAcquisitionCostUsd: BigDecimal,
		// Hack to be able to change createdAt, which is first assigned by DB
		afterSave: WalletCurrencyPosition.() -> Unit = {},
	) = walletCurrencyPositionRepository.save(
		WalletCurrencyPosition(
			id = id,
			walletId = walletId,
			totalBought = totalBought,
			totalSold = totalSold,
			totalAcquisitionCostUsd = totalAcquisitionCostUsd,
		),
	).run {
		walletCurrencyPositionRepository.save(apply(afterSave))
	}

	fun getExchangeRateSnapshot(
		id: UUID = UUIDv7(),
		currency: CryptoCurrency,
		exchangeRateUsd: BigDecimal,
		validAt: Instant,
	) = exchangeRateSnapshotRepository.save(
		ExchangeRateSnapshot(
			id = id,
			currency = currency,
			exchangeRateUsd = exchangeRateUsd,
			validAt = validAt,
		),
	)

	fun getBotDraft(id: UUID = UUIDv7(), userId: UUID) = botDraftRepository.save(BotDraft(id = id, userId = userId))

	fun getBot(
		id: UUID = UUIDv7(),
		userId: UUID,
		isActive: Boolean = true,
		name: String = "testBot",
		avatarFileId: UUID,
		tradeAmount: BigDecimal = 1.toBigDecimal(),
		buyFrequency: Long = 10,
		profitTargetFraction: BigDecimal = 0.1.toBigDecimal(),
		stopLossFraction: BigDecimal = 0.05.toBigDecimal(),
		marketCapFromUsd: BigDecimal? = null,
		marketCapToUsd: BigDecimal? = null,
		liquidityFromUsd: BigDecimal? = null,
		liquidityToUsd: BigDecimal? = null,
		dailyVolumeFromUsd: BigDecimal? = null,
		dailyVolumeToUsd: BigDecimal? = null,
		numberOfHoldersFrom: Long? = null,
		numberOfHoldersTo: Long? = null,
		buyVolume: BigDecimal? = null,
		sellVolume: BigDecimal? = null,
		sellTransactionFraction: BigDecimal? = null,
		buyTransactionFraction: BigDecimal? = null,
		tokenTickerCopyIsChecked: Boolean = false,
		creatorHighBuyIsChecked: Boolean = false,
		bundledBuysDetectedIsChecked: Boolean = false,
		suspiciousWalletsDetectedIsChecked: Boolean = false,
		singleHighBuyIsChecked: Boolean = false,
		buyTokensAliveAtLeastFor: Duration? = null,
		shouldAutoSellAfterHoldTime: Boolean = false,
		beforeSave: Bot.() -> Unit = {},
	) = botRepository.save(
		Bot(
			id = id,
			isActive = isActive,
			userId = userId,
			name = name,
			avatarFileId = avatarFileId,
			tradeAmount = tradeAmount,
			buyFrequency = buyFrequency,
			profitTargetFraction = profitTargetFraction,
			stopLossFraction = stopLossFraction,
			marketCapFromUsd = marketCapFromUsd,
			marketCapToUsd = marketCapToUsd,
			liquidityFromUsd = liquidityFromUsd,
			liquidityToUsd = liquidityToUsd,
			dailyVolumeFromUsd = dailyVolumeFromUsd,
			dailyVolumeToUsd = dailyVolumeToUsd,
			numberOfHoldersFrom = numberOfHoldersFrom,
			numberOfHoldersTo = numberOfHoldersTo,
			buyVolume = buyVolume,
			sellVolume = sellVolume,
			sellTransactionFraction = sellTransactionFraction,
			buyTransactionFraction = buyTransactionFraction,
			tokenTickerCopyIsChecked = tokenTickerCopyIsChecked,
			creatorHighBuyIsChecked = creatorHighBuyIsChecked,
			bundledBuysDetectedIsChecked = bundledBuysDetectedIsChecked,
			suspiciousWalletsDetectedIsChecked = suspiciousWalletsDetectedIsChecked,
			singleHighBuyIsChecked = singleHighBuyIsChecked,
			buyTokensAliveAtLeastFor = buyTokensAliveAtLeastFor,
			shouldAutoSellAfterHoldTime = shouldAutoSellAfterHoldTime,
		)
			.apply { recalculateSellToBuyTransactionRatio() }
			.apply(beforeSave),
	)

	fun getBotWallet(
		id: UUID = UUIDv7(),
		botId: UUID,
		address: AddressWrapper = AddressWrapper("CAJM1r7SjSFxv99tRjpKUVQUPyq3CwtASyYLQU8EPrEA"),
		privateKey: String = "61WRRmLxGN16RjSH2LY6My6pt6kpFT8xGQdteu6Bu5jZfMaSCnYnbn8epjTNruD5o9HfWdef2ngCtpuEUnBvnNsY",
		chain: Chain = Chain.SOLANA,
		beforeSave: BotWallet.() -> Unit = {},
	) = botWalletRepository.save(
		BotWallet(
			id = id,
			botId = botId,
			address = address,
			chain = chain,
			privateKey = privateKey,
		).apply(beforeSave),
	)

	fun getBotTransaction(
		id: UUID = UUIDv7(),
		botWalletId: UUID,
		type: BotTransactionType = BotTransactionType.BUY,
		status: TransactionStatus = TransactionStatus.SUCCESS,
		nonce: Long? = 0,
		signedTx: String? = "signedTx",
		txHash: TxHash = "txHash".asTxHash(),
		tokenAddress: AddressWrapper? = AddressWrapper("GYCiYAtstSznrYaTi2hMkKJzqRhPBcfeRUqWJWtabUL"),
		baseValue: BigInteger = 100.toBigInteger(),
		exchangeRateUsd: BigDecimal = 250.toBigDecimal(),
		createdAt: Instant = Instant.now(),
		amountIn: BigInteger? = null,
		amountOut: BigInteger? = null,
		percentageOf: BigDecimal? = null,
		beforeSave: BotTransaction.() -> Unit = {},
		entityModifier: (BotTransaction) -> Unit = {},
	): BotTransaction {
		val botTransaction = BotTransaction(
			id = id,
			botWalletId = botWalletId,
			type = type,
			nonce = nonce,
			signedTx = signedTx,
			txHash = txHash,
			tokenAddress = tokenAddress,
			baseValue = baseValue,
			exchangeRateUsd = exchangeRateUsd,
			percentageOf = percentageOf,
		).apply(beforeSave).also {
			it.setAndReturnPrivateProperty("status", status)
			it.setAndReturnPrivateProperty("amountIn", amountIn)
			it.setAndReturnPrivateProperty("amountOut", amountOut)
		}

		botTransactionRepository.save(botTransaction)

		// have to save once again to override @CreationTimestamp
		return botTransactionRepository.save(
			botTransaction.also {
				it.setAndReturnPrivateProperty("createdAt", createdAt)
			},
		)
	}

	fun getMarketPositionRefresh(userId: UUID, lastViewedAt: Instant) = marketPositionRefreshRepository.save(
		MarketPositionRefresh(
			userId = userId,
			initialLastViewedAt = lastViewedAt,
		),
	)

	fun getTokenPairInfo(
		chain: Chain,
		dexType: GetDex.Dex,
		uniswapV3Fee: UniswapV3Fee?,
		tokenAddress: AddressWrapper,
		pairAddress: AddressWrapper,
		tokenDecimals: BigInteger,
		raydiumPoolType: GetDex.PoolType? = null,
		raydiumAmmMarketData: RaydiumAmmMarketData? = null,
		raydiumCpmmMarketData: RaydiumCPMMMarketData? = null,
		creatorAddress: AddressWrapper? = null,
	) = tokenPairInfoRepository.save(
		TokenPairInfo(
			id = UUIDv7(),
			chain = chain,
			dexType = dexType,
			uniswapV3Fee = uniswapV3Fee,
			tokenAddress = tokenAddress,
			pairAddress = pairAddress,
			tokenDecimals = tokenDecimals,
			raydiumAmmMarketData = raydiumAmmMarketData,
			raydiumCpmmMarketData = raydiumCpmmMarketData,
			raydiumPoolType = raydiumPoolType,
			creatorAddress = creatorAddress,
		),
	)

	fun getBotMarketPosition(
		id: UUID = UUIDv7(),
		botWalletId: UUID,
		chain: Chain = Chain.SOLANA,
		tokenAddress: AddressWrapper,
		profitTargetFraction: BigDecimal,
		stopLossFraction: BigDecimal,
		assumedBuyPrice: BigDecimal,
		blockSlot: Int,
		beforeSave: BotMarketPosition.() -> Unit = {},
	) = botMarketPositionRepository.save(
		BotMarketPosition(
			id = id,
			botWalletId = botWalletId,
			chain = chain,
			tokenAddress = tokenAddress,
			blockSlot = blockSlot,
			profitTargetFraction = profitTargetFraction,
			stopLossFraction = stopLossFraction,
			assumedBuyPrice = assumedBuyPrice,
		).apply(beforeSave),
	)

	fun getBotMarketPositionTradeState(
		id: UUID = UUIDv7(),
		botMarketPositionId: UUID,
		type: BotMarketPositionTradeStateType,
		marketCapUsd: BigDecimal,
		liquidityUsd: BigDecimal,
		volumeUsd: BigDecimal,
		numOfAccountHolders: Long,
		buyVolume: BigDecimal,
		sellVolume: BigDecimal,
		fractionOfSellTransactions: BigDecimal,
	) = botMarketPositionTradeStateRepository.save(
		BotMarketPositionTradeState(
			id = id,
			botMarketPositionId = botMarketPositionId,
			type = type,
			marketCapUsd = marketCapUsd,
			liquidityUsd = liquidityUsd,
			volumeUsd = volumeUsd,
			numOfAccountHolders = numOfAccountHolders,
			buyVolume = buyVolume,
			sellVolume = sellVolume,
			fractionOfSellTransactions = fractionOfSellTransactions,
		),
	)

	fun getBotPortfolioValueSnapshot(
		id: UUID = UUIDv7(),
		botId: UUID,
		chain: Chain = Chain.SOLANA,
		snapshotMadeAt: Instant,
		portfolioValueUsd: BigDecimal,
		acquisitionValueUsd: BigDecimal,
	) = botPortfolioValueSnapshotRepository.save(
		BotPortfolioValueSnapshot(
			id = id,
			botId = botId,
			chain = chain,
			snapshotMadeAt = snapshotMadeAt,
			portfolioValueUsd = portfolioValueUsd,
			acquisitionValueUsd = acquisitionValueUsd,
		),
	)

	fun getUserStatistics(
		id: UUID = UUIDv7(),
		userId: UUID,
		daysInStreak: Int = 0,
		lastManualTradeAt: LocalDate? = null,
		beforeSave: UserStatistics.() -> Unit = {},
	) = userStatisticsRepository.save(
		UserStatistics(
			id = id,
			userId = userId,
			daysInStreak = daysInStreak,
			lastManualTradeAt = lastManualTradeAt,
		).apply(beforeSave),
	)

	fun getUserLeaderboard(
		id: UUID = UUIDv7(),
		userId: UUID,
		rank: Int,
		donutMultiplier: BigDecimal = BigDecimal.TEN,
		donutGainedSnapshot: BigDecimal = BigDecimal.TEN,
	) = userLeaderBoardRepository.save(
		UserLeaderboard(
			id = id,
			userId = userId,
			rank = rank,
			donutMultiplier = donutMultiplier,
			donutGainedSnapshot = donutGainedSnapshot,
		),
	)

	fun getFattyCard(
		id: UUID = UUIDv7(),
		rarity: String = "COMMON",
		avatarFileId: UUID = UUID.randomUUID(),
		probability: BigDecimal = BigDecimal.valueOf(50),
		donutReward: BigDecimal = BigDecimal.valueOf(100),
	) = fattyCardRepository.save(
		FattyCard(
			id = id,
			avatarFileId = avatarFileId,
			rarity = rarity,
			probability = probability,
			donutReward = donutReward,
		),
	)

	fun getUserFattyCard(
		id: UUID = UUIDv7(),
		userId: UUID,
		fattyCardId: UUID = UUIDv7(),
		claimed: Boolean = false,
		displayed: Boolean = false,
	) = userFattyCardRepository.save(
		UserFattyCard(
			id = id,
			userId = userId,
			fattyCardId = fattyCardId,
		).also {
			if (claimed) it.claim()
			if (displayed) it.display()
		},
	)

	fun generateFattyCards(): List<FattyCard> {
		val file = getFile(id = 1.toUUID())
		return listOf(
			FattyCard(
				id = 0.toUUID(),
				rarity = "Epic",
				probability = 0.01.toBigDecimal(),
				donutReward = 200000.toBigDecimal(),
				avatarFileId = file.id,
			),
			FattyCard(
				id = 1.toUUID(),
				rarity = "Mythical",
				probability = 0.29.toBigDecimal(),
				donutReward = 20000.toBigDecimal(),
				avatarFileId = file.id,
			),
			FattyCard(
				id = 2.toUUID(),
				rarity = "Legendary",
				probability = 2.7.toBigDecimal(),
				donutReward = 5000.toBigDecimal(),
				avatarFileId = file.id,
			),
			FattyCard(
				id = 3.toUUID(),
				rarity = "Rare",
				probability = 12.toBigDecimal(),
				donutReward = 1000.toBigDecimal(),
				avatarFileId = file.id,
			),
			FattyCard(
				id = 4.toUUID(),
				rarity = "Uncommon",
				probability = 35.toBigDecimal(),
				donutReward = 300.toBigDecimal(),
				avatarFileId = file.id,
			),
			FattyCard(
				id = 5.toUUID(),
				rarity = "Common",
				probability = 50.toBigDecimal(),
				donutReward = 100.toBigDecimal(),
				avatarFileId = file.id,
			),
		).also { fattyCardRepository.saveAll(it) }
	}

	fun getBotLeaderboardSnapshot(
		id: UUID = UUIDv7(),
		botId: UUID,
		profitValueUsd: BigDecimal,
		profitValueFraction: BigDecimal,
		balanceUsd: BigDecimal,
		snapshotMadeAt: Instant = Instant.now(),
	) = botLeaderboardSnapshotRepository.save(
		BotLeaderboardSnapshot(
			id = id,
			botId = botId,
			profitValueUsd = profitValueUsd,
			profitValueFraction = profitValueFraction,
			balanceUsd = balanceUsd,
			snapshotMadeAt = snapshotMadeAt,
		),
	)

	fun setSelectedChains(userId: UUID, selectedChains: Set<Chain>) {
		dslContext
			.update(FIREBASE_USER)
			.set(FIREBASE_USER.SELECTED_CHAINS, selectedChains.toTypedArray())
			.where(FIREBASE_USER.ID.eq(userId))
			.execute()
	}

	fun createFattyLeagueSeason(
		id: UUID,
		from: Instant,
		name: String,
		initialReleasePercentage: BigDecimal,
		donutsToFattyTokensRatio: BigDecimal,
		claimCooldownPeriodInMinutes: Long,
		remainingUnlockPeriodInMonths: Long,
		remainingUnlockPeriodParts: Long,
		seasonNumber: Int,
		beforeSave: FattyLeagueSeason.() -> Unit = {},
	): FattyLeagueSeason {
		val season = FattyLeagueSeason(
			id = id,
			startAt = from,
			name = name,
			seasonNumber = seasonNumber,
			claimCooldownPeriodInMinutes = claimCooldownPeriodInMinutes,
			donutsToFattyTokensRatio = donutsToFattyTokensRatio,
			initialReleasePercentage = initialReleasePercentage,
			remainingUnlockPeriodInMonths = remainingUnlockPeriodInMonths,
			remainingUnlockPeriodParts = remainingUnlockPeriodParts,
		).also(beforeSave)
		return fattyLeagueSeasonRepository.save(season)
	}

	fun createUserFattyLeagueSeason(
		id: UUID,
		userId: UUID,
		fattyLeagueSeasonId: UUID,
		totalTokens: BigDecimal,
		lastClaimedAt: Instant? = null,
		beforeSave: UserFattyLeagueSeason.() -> Unit = {},
	): UserFattyLeagueSeason {
		val userFattyLeagueSeason = UserFattyLeagueSeason(
			id = id,
			userId = userId,
			fattyLeagueSeasonId = fattyLeagueSeasonId,
			totalTokens = totalTokens,
		).also(beforeSave)

		return userFattyLeagueSeasonRepository.save(userFattyLeagueSeason)
	}

	fun getLimitOrder(
		id: UUID,
		userId: UUID,
		walletId: UUID,
		tokenAddress: AddressWrapper,
		chain: Chain,
		limitPrice: BigInteger,
		type: LimitOrderType,
		initialAmount: BigInteger,
		beforeSave: LimitOrder.() -> Unit = {},
	) = limitOrderRepository.save(
		LimitOrder(
			id = id,
			userId = userId,
			walletId = walletId,
			limitPrice = limitPrice,
			initialAmount = initialAmount,
			type = type,
			tokenAddress = tokenAddress,
			chain = chain,
		).apply(beforeSave),
	)
}
