package com.cleevio.fatbot.application.common.crypto

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.constant.O_X
import org.bitcoinj.core.Base58
import org.bouncycastle.crypto.generators.ECKeyPairGenerator
import org.bouncycastle.crypto.params.ECDomainParameters
import org.bouncycastle.crypto.params.ECKeyGenerationParameters
import org.bouncycastle.crypto.params.ECPrivateKeyParameters
import org.bouncycastle.crypto.params.ECPublicKeyParameters
import org.bouncycastle.jce.spec.ECNamedCurveParameterSpec
import org.bouncycastle.util.encoders.Hex
import org.p2p.solanaj.core.Account
import org.web3j.crypto.Hash
import java.security.SecureRandom
import java.util.Arrays

data class WalletDto(
	val address: AddressWrapper,
	val privateKey: String,
)

object WalletGenerator {

	private const val CURVE_NAME: String = "secp256k1"

	private val spec: ECNamedCurveParameterSpec = org.bouncycastle.jce.ECNamedCurveTable.getParameterSpec(CURVE_NAME)
	private val ecDomainParameters: ECDomainParameters = ECDomainParameters(spec.curve, spec.g, spec.n)
	private val generator: ECKeyPairGenerator = ECKeyPairGenerator()
	private val keygenParams: ECKeyGenerationParameters = ECKeyGenerationParameters(ecDomainParameters, SecureRandom())

	init {
		generator.init(keygenParams)
	}

	fun createWallet(chainType: ChainType): WalletDto {
		return when (chainType) {
			ChainType.EVM -> createEvmWallet()
			ChainType.SOLANA -> createSolanaWallet()
		}
	}

	private fun createEvmWallet(): WalletDto {
		val keypair = generator.generateKeyPair()
		val privateKey = keypair.private as ECPrivateKeyParameters
		val publicKey = keypair.public as ECPublicKeyParameters

		val pubKeyBytes: ByteArray = publicKey.q.getEncoded(false)
		// TODO: Is this equivalent to keccak256?
		val hashed: ByteArray = Hash.sha3(Arrays.copyOfRange(pubKeyBytes, 1, pubKeyBytes.size))
		val addressBytes = Arrays.copyOfRange(hashed, 12, 32)

		var hexPrivateKey: String = Hex.toHexString(privateKey.d.toByteArray())
		if (hexPrivateKey.length > 64) {
			val dif = hexPrivateKey.length - 64
			hexPrivateKey = hexPrivateKey.substring(dif)
		}

		return WalletDto(
			address = AddressWrapper(addressString = O_X + Hex.toHexString(addressBytes)),
			privateKey = hexPrivateKey,
		)
	}

	private fun createSolanaWallet(): WalletDto {
		val account = Account()
		val privateKeyBytes = account.secretKey
		val publicKeyBytes = account.publicKey.toByteArray()
		val privateKey = Base58.encode(privateKeyBytes)
		val publicKey = Base58.encode(publicKeyBytes)

		return WalletDto(
			address = AddressWrapper(publicKey),
			privateKey = privateKey,
		)
	}
}
