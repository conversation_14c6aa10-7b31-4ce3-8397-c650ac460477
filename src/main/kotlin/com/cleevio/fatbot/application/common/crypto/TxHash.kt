package com.cleevio.fatbot.application.common.crypto

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import io.swagger.v3.oas.annotations.media.Schema
import org.p2p.solanaj.utils.TransactionSignatureParserUtil
import org.web3j.crypto.Hash
import org.web3j.utils.Numeric

/**
 * Represents a unique identifier for a transaction on a blockchain.
 *
 * Some blockchains use different forms of transaction identifiers. For example, Ethereum
 * and EVM-compatible chains use simply a hash of a transaction, while Solana uses a signature,
 * which is essentially a signed transaction hash.
 *
 * To maintain consistency across different blockchains, this wrapper class is used
 * as a unified representation of a transaction identifier.
 */
@JvmInline
@Schema(type = "string")
value class TxHash(val txHash: String)

fun SignedTx.extractTxHash(chain: Chain) = when (chain.type) {
	ChainType.EVM -> this.hashToTxHash()
	ChainType.SOLANA -> this.extractFirstSignatureAsTxHash()
}

/**
 * Applies [Hash.sha3] and converts result to a Hex string.
 *
 * Note: Should only be used on `EVM` SignedTx, where the result corresponds to the Tx identifier (TxHash)
 */
fun SignedTx.hashToTxHash() = TxHash(Numeric.toHexString(Hash.sha3(Numeric.hexStringToByteArray(signedTx))))

/**
 * Use only for `SOLANA` SignedTx
 */
fun SignedTx.extractFirstSignatureAsTxHash() =
	TxHash(TransactionSignatureParserUtil.parseSignaturesFromTransaction(signedTx)[0])

fun String.asTxHash() = TxHash(this)
