package com.cleevio.fatbot.application.common.service

import org.slf4j.LoggerFactory
import java.util.concurrent.locks.ReentrantLock
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.withLock
import kotlin.concurrent.write

abstract class AbstractLiveDataService<V : Any> {

	private val logger = LoggerFactory.getLogger(this::class.java)

	private lateinit var value: V
	private val rwLock = ReentrantReadWriteLock()
	private val computationLock = ReentrantLock()

	internal abstract fun computeNewValue(): V

	fun get(): V {
		rwLock.read {
			if (::value.isInitialized) return value
		}

		return tryRefreshAndGet()
	}

	fun tryRefreshAndGet(): V {
		val isAcquired = computationLock.tryLock()
		if (!isAcquired) {
			return computationLock.withLock { value }
		}

		return try {
			// By default, we compute the value outside RW lock (might be expensive operation)
			// and only acquire it to set the new value
			val newValue = computeNewValue()
			rwLock.write {
				// At this point we are holding the write so it's safe to set new property
				value = newValue
				value
			}
		} finally {
			computationLock.unlock()
		}
	}
}
