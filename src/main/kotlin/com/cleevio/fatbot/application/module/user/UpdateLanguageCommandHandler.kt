package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.user.command.UpdateLanguageCommand
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateLanguageCommandHandler(
	private val firebaseUserFinderService: FirebaseUserFinderService,
) : CommandHandler<Unit, UpdateLanguageCommand> {

	override val command = UpdateLanguageCommand::class

	@Transactional
	override fun handle(command: UpdateLanguageCommand) {
		firebaseUserFinderService
			.getById(id = command.userId)
			.setLanguage(language = command.language)
	}
}
