package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.asLamport
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.extractFirstSignatureAsTxHash
import com.cleevio.fatbot.application.common.crypto.minus
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.module.bot.command.WithdrawFromBotPortfolioCommand
import com.cleevio.fatbot.application.module.bot.exception.WithdrawalBotInvalidStateException
import com.cleevio.fatbot.application.module.bot.locks.BOT_MODULE
import com.cleevio.fatbot.application.module.bot.locks.UPDATE_BOT
import com.cleevio.fatbot.application.module.bot.port.out.TransferSol
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.market.port.out.EstimateGas
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.market.port.out.ValidateDestinationWalletFundsForRent
import com.cleevio.fatbot.application.module.market.port.out.ValidateFundsForSvmTransaction
import com.cleevio.fatbot.application.module.market.port.out.estimate
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.application.module.wallet.service.WalletBalanceService
import com.cleevio.fatbot.domain.bottransaction.BotTransactionCreateService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.math.BigInteger

@Component
class WithdrawFromBotPortfolioCommandHandler(
	private val botFinderService: BotFinderService,
	private val botWalletFinderService: BotWalletFinderService,
	private val walletFinderService: WalletFinderService,
	private val estimateGas: List<EstimateGas>,
	private val walletBalanceService: WalletBalanceService,
	private val validateFundsForSvmTransaction: ValidateFundsForSvmTransaction,
	private val validateDestinationWalletFundsForRent: ValidateDestinationWalletFundsForRent,
	private val transferSol: TransferSol,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val botTransactionCreateService: BotTransactionCreateService,
) : CommandHandler<WithdrawFromBotPortfolioCommand.Result, WithdrawFromBotPortfolioCommand> {

	override val command = WithdrawFromBotPortfolioCommand::class

	@Transactional
	@Lock(module = BOT_MODULE, lockName = UPDATE_BOT)
	override fun handle(
		@LockFieldParameter("userId") command: WithdrawFromBotPortfolioCommand,
	): WithdrawFromBotPortfolioCommand.Result {
		val bot = botFinderService.getByIdAndUserId(id = command.botId, userId = command.userId)

		if (bot.isActive) {
			throw WithdrawalBotInvalidStateException("Bot with id: ${command.botId} cannot be active for withdrawal")
		}

		val botWallet = botWalletFinderService.getByBotId(botId = bot.id)
		val receivingWallet = walletFinderService.findByWalletAddress(
			userId = command.userId,
			address = command.recipientAddress,
		)
		val baseAmount = command.nativeAmount.asNativeAmount().toBase(botWallet.chain)

		val estimateGasResult = estimateGas.estimate(
			EstimateGas.TransferCurrencyInput(
				privateKey = botWallet.privateKey,
				chain = botWallet.chain,
				gasInfo = null,
				amount = baseAmount,
				toAddress = command.recipientAddress,
			),
		)

		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultSolana) error("Estimate gas must be of Solana type")

		// TODO: This is not ideal because same call is done in validateFundsForSvmTransaction
		//  but for now it works
		val walletBalance = walletBalanceService.getBalance(
			botWallet.address,
			botWallet.chain,
		)

		val balanceAfter = walletBalance - baseAmount
		// Handle special case when we are trying to send everything from the wallet
		val toTransferSafeBaseAmount = when (balanceAfter.amount) {
			BigInteger.ZERO -> baseAmount - estimateGasResult.feeBaseAmount
			else -> baseAmount
		}

		validateFundsForSvmTransaction(
			walletAddress = botWallet.address,
			transactionAmount = toTransferSafeBaseAmount,
			estimatedFees = estimateGasResult.feeBaseAmount,
		)

		validateDestinationWalletFundsForRent(
			destinationAddress = command.recipientAddress,
			toTransferBaseAmount = toTransferSafeBaseAmount,
		)

		val signedTx = transferSol(
			privateKey = botWallet.privateKey,
			amount = toTransferSafeBaseAmount.amount.asLamport(),
			destinationWalletAddress = command.recipientAddress,
		)

		val currentExchangeRate = getUsdExchangeRate(botWallet.chain.currency)

		botTransactionCreateService.createFromBotWithdrawalTransfer(
			botWalletId = botWallet.id,
			userWalletId = receivingWallet?.id,
			txHash = signedTx.extractFirstSignatureAsTxHash(),
			signedTx = signedTx,
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			tradeAmount = toTransferSafeBaseAmount.amount,
			tokenAddress = null,
			percentageOf = command.percentageOf,
			exchangeRateUsd = currentExchangeRate,
		)

		return WithdrawFromBotPortfolioCommand.Result(txHash = signedTx.extractFirstSignatureAsTxHash())
	}
}
