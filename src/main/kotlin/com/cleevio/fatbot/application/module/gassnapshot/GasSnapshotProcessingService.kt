package com.cleevio.fatbot.application.module.gassnapshot

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import com.cleevio.fatbot.infrastructure.coroutines.createJobScope
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.springframework.stereotype.Service

@Service
class GasSnapshotProcessingService(
	private val chainProperties: ChainProperties,
	private val evmChainContextFactory: EvmChainContextFactory,
	private val gasInfoSnapshotService: GasInfoSnapshotService,
) {

	private val scope = createJobScope()

	fun processGasSnapshots() {
		val chains = chainProperties.enabledEvmChains

		/*
		Prefer parallel execution for all chain to avoid delaying the scheduler
		 */
		val chainToDeferredGasInfo = chains.associateWith {
			scope.async {
				evmChainContextFactory.ofChainId(it.evmId) { client.getGasInfo() }
			}
		}

		val chainToGasInfo = runBlocking {
			chainToDeferredGasInfo.mapValues { (_, deferredGasInfo) -> deferredGasInfo.await() }
		}

		gasInfoSnapshotService.saveOrUpdateGasInfoSnapshots(chainToGasInfo)
	}
}
