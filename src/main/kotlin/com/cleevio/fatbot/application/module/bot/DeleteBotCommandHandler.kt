package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.bot.command.DeleteBotCommand
import com.cleevio.fatbot.application.module.bot.locks.BOT_MODULE
import com.cleevio.fatbot.application.module.bot.locks.UPDATE_BOT
import com.cleevio.fatbot.application.module.bot.port.out.DeleteAllBotMarketPositionTradeState
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.domain.bot.BotDeleteService
import com.cleevio.fatbot.domain.botleaderboard.BotLeaderboardSnapshotDeleteService
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionDeleteService
import com.cleevio.fatbot.domain.botportfolio.BotPortfolioValueSnapshotDeleteService
import com.cleevio.fatbot.domain.bottransaction.BotTransactionDeleteService
import com.cleevio.fatbot.domain.botwallet.BotWalletDeleteService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.math.BigInteger

@Component
class DeleteBotCommandHandler(
	private val botFinderService: BotFinderService,
	private val botWalletFinderService: BotWalletFinderService,
	private val deleteAllBotMarketPositionTradeState: DeleteAllBotMarketPositionTradeState,
	private val botLeaderboardSnapshotDeleteService: BotLeaderboardSnapshotDeleteService,
	private val botTransactionDeleteService: BotTransactionDeleteService,
	private val botWalletDeleteService: BotWalletDeleteService,
	private val botMarketPositionDeleteService: BotMarketPositionDeleteService,
	private val botPortfolioValueSnapshotDeleteService: BotPortfolioValueSnapshotDeleteService,
	private val botDeleteService: BotDeleteService,
) : CommandHandler<Unit, DeleteBotCommand> {

	override val command = DeleteBotCommand::class

	@Transactional
	@Lock(module = BOT_MODULE, lockName = UPDATE_BOT)
	override fun handle(@LockFieldParameter("userId") command: DeleteBotCommand) {
		val bot = botFinderService.getByIdAndUserId(command.botId, command.userId)
		val botWallet = botWalletFinderService.getByBotId(bot.id)

		// TODO: 1. Handle open market positions once implemented
		// TODO: 2. Descriptive fatbot errors once used for other than internal / testing purposes
		when {
			botWallet.balance != BigInteger.ZERO -> error("Cannot delete a bot with a non-empty wallet!")
			bot.isActive -> error("Cannot delete an active bot!")
		}

		deleteAllBotMarketPositionTradeState(botWalletId = botWallet.id)
		botTransactionDeleteService.deleteAllByBotWalletId(botWalletId = botWallet.id)
		botMarketPositionDeleteService.deleteAllByBotWalletId(botWalletId = botWallet.id)
		botWalletDeleteService.deleteById(id = botWallet.id)

		botLeaderboardSnapshotDeleteService.deleteAllByBotId(botId = bot.id)
		botPortfolioValueSnapshotDeleteService.deleteAllByBotId(botId = bot.id)
		botDeleteService.deleteById(id = bot.id)
	}
}
