package com.cleevio.fatbot.application.module.bot.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

interface GetMyBot {
	operator fun invoke(userId: UUID, botId: UUID): Result

	data class Result(
		val botId: UUID,
		val tradeAmount: BaseAmount,
		val walletAddress: AddressWrapper,
		val walletChain: Chain,
		val walletBalance: BaseAmount,
		val walletAcquisitionValueUsd: BigDecimal,
		val name: String,
		val isActive: Boolean,
		val numberOfActiveDays: Long,
		val botAvatarFileId: UUID,
		val buyFrequency: Long,
		val remainingBuyFrequency: Long,
		val buyFrequencyLastResetAt: Instant,
	)
}
