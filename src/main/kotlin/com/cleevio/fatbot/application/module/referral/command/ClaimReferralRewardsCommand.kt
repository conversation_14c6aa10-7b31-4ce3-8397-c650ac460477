package com.cleevio.fatbot.application.module.referral.command

import com.cleevio.fatbot.application.common.command.Command
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class ClaimReferralRewardsCommand(
	val userId: UUID,
	val chain: Chain,
) : Command<ClaimReferralRewardsCommand.Result> {

	@Schema(name = "ClaimReferralRewardsResult")
	data class Result(
		val claimedAmountNative: NativeAmount,
		val txHash: TxHash,
	)
}
