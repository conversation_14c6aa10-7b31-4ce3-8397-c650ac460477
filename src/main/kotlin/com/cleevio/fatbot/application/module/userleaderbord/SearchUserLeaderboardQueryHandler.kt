package com.cleevio.fatbot.application.module.userleaderbord

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.adapter.`in`.toInfiniteScrollSlice
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.userleaderbord.port.out.GetUserLeaderboardDonutsByRank
import com.cleevio.fatbot.application.module.userleaderbord.port.out.SearchUserLeaderboard
import com.cleevio.fatbot.application.module.userleaderbord.query.SearchUserLeaderboardQuery
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.RoundingMode

@Component
class SearchUserLeaderboardQueryHandler(
	private val searchUserLeaderboard: SearchUserLeaderboard,
	private val getUserLeaderboardDonutsByRank: GetUserLeaderboardDonutsByRank,
) : Query<PERSON>andler<InfiniteScrollSlice<SearchUserLeaderboardQuery.Result, Int>, SearchUserLeaderboardQuery> {

	override val query = SearchUserLeaderboardQuery::class

	override fun handle(query: SearchUserLeaderboardQuery): InfiniteScrollSlice<SearchUserLeaderboardQuery.Result, Int> {
		val userLeaderboards = searchUserLeaderboard(query.infiniteScroll)

		if (userLeaderboards.content.isEmpty()) {
			return InfiniteScrollSlice.empty()
		}

		val users = userLeaderboards.content.sortedBy { it.rank }.zipWithNext { nextRankUser, user ->
			val donutsNeededForNextThreshold = calculateDonutsNeededForNextThreshold(
				donutsGained = user.donutsGained,
				donutsInNextRank = nextRankUser.donutsGained,
			)

			user.toQueryResult(donutsNeededForNextThreshold = donutsNeededForNextThreshold)
		}

		val firstUser = userLeaderboards.content.first()
		val donutsInNextRank = if (firstUser.rank == 1) {
			BigDecimal.ZERO
		} else {
			getUserLeaderboardDonutsByRank(firstUser.rank - 1)
		}

		val donutsNeededForNextThreshold = calculateDonutsNeededForNextThreshold(
			donutsGained = firstUser.donutsGained,
			donutsInNextRank = donutsInNextRank,
		)

		val firstUserResult = firstUser.toQueryResult(donutsNeededForNextThreshold = donutsNeededForNextThreshold)
		val allUsers = listOf(firstUserResult) + users

		return allUsers.toInfiniteScrollSlice(
			infiniteScroll = query.infiniteScroll,
			idSelector = { rank },
		)
	}

	private fun SearchUserLeaderboard.Result.toQueryResult(
		donutsNeededForNextThreshold: BigDecimal,
	): SearchUserLeaderboardQuery.Result {
		val volumeNeededForNextThreshold = donutsNeededForNextThreshold.divide(
			multiplier,
			10,
			RoundingMode.HALF_DOWN,
		)

		return SearchUserLeaderboardQuery.Result(
			userId = userId,
			rank = rank,
			multiplier = multiplier,
			donutsGained = donutsGained,
			email = email,
			volumeNeededForNextThreshold = volumeNeededForNextThreshold,
			volume = volume,
		)
	}

	private fun calculateDonutsNeededForNextThreshold(donutsGained: BigDecimal, donutsInNextRank: BigDecimal): BigDecimal {
		if (donutsInNextRank == BigDecimal.ZERO) {
			return donutsInNextRank
		}

		return donutsInNextRank - donutsGained
	}
}
