package com.cleevio.fatbot.application.module.hottoken.scheduled

import com.cleevio.fatbot.application.module.hottoken.HotTokenRefreshService
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class HotTokenRefreshTrigger(
	private val hotTokenRefreshService: HotTokenRefreshService,
	private val chainProperties: ChainProperties,
) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.refresh-hot-tokens-cron")
	@Scheduled(cron = "\${fatbot.token.hot-token-refresh.cron}")
	@SchedulerLock(
		name = "REFRESH_HOT_TOKENS",
		lockAtMostFor = "\${fatbot.token.hot-token-refresh.lock-for}",
		lockAtLeastFor = "\${fatbot.token.hot-token-refresh.lock-for}",
	)
	fun trigger() {
		logger.info("REFRESH_HOT_TOKENS cron started")
		chainProperties.enabledChains.forEach {
			hotTokenRefreshService.refreshHotTokens(chain = it)
		}
		logger.info("REFRESH_HOT_TOKENS cron ended")
	}
}
