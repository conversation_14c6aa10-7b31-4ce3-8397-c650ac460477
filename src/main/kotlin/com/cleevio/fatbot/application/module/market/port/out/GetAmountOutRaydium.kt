package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.adapter.out.solana.model.PoolReserves
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.domain.token.RaydiumAmmMarketData
import com.cleevio.fatbot.domain.token.RaydiumCPMMMarketData

interface GetAmountOutRaydium {

	fun getAmountOutAMM(
		amount: BaseAmount,
		pairAddress: AddressWrapper,
		marketData: RaydiumAmmMarketData,
		isBuy: Boolean,
	): BaseAmount

	fun fromReservesAMM(amount: BaseAmount, poolReserves: PoolReserves, isBuy: Boolean): BaseAmount

	fun getReservesAMM(marketData: RaydiumAmmMarketData): PoolReserves

	fun getAmountOutCLMM(
		amount: BaseAmount,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		isBuy: Boolean,
	): BaseAmount

	fun getAmountOutCPMM(
		amount: BaseAmount,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		marketData: RaydiumCPMMMarketData,
		isBuy: Boolean,
	): BaseAmount

	fun fromReservesCPMM(
		amount: BaseAmount,
		poolReserves: PoolReserves,
		tradeFeeBp: BasisPoint,
		isBuy: Boolean,
	): BaseAmount

	fun getReservesCPMM(tokenAddress: AddressWrapper, pairAddress: AddressWrapper): PoolReserves
}
