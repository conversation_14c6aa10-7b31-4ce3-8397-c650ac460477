package com.cleevio.fatbot.application.module.token.constant

import java.time.Instant
import java.time.temporal.ChronoUnit

enum class TimeRange(
	val intervalCount: Int,
	val timeInterval: TimeInterval,
	val limit: Int,
) {
	HOUR(1, TimeInterval.MINUTE, 60),
	DAY(5, TimeInterval.MINUTE, 288),
	WEEK(15, TimeInterval.MINUTE, 672),
	MONTH(1, TimeInterval.HOUR, 744),
	YEAR(1, TimeInterval.DAY, 365),

	// Note: If we need more than 2 years of data, we should add it with pagination
	ALL(1, TimeInterval.DAY, 730),
}

fun TimeRange.toInstant(now: Instant): Instant = when (this) {
	TimeRange.HOUR -> now.minus(1, ChronoUnit.HOURS)
	TimeRange.DAY -> now.minus(1, ChronoUnit.DAYS)
	TimeRange.WEEK -> now.minus(7, ChronoUnit.DAYS)
	TimeRange.MONTH -> now.minus(31, ChronoUnit.DAYS)
	TimeRange.YEAR -> now.minus(365, ChronoUnit.DAYS)
	TimeRange.ALL -> Instant.EPOCH
}
