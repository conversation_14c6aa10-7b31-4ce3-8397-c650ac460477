package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.adapter.out.evm.simulation.GetTokenMaxBuy
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.token.query.GetTokenMaxBuyQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.finder.WalletCurrencyPositionFinderService
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.BigInteger

@Component
class GetTokenMaxBuyQueryHandler(
	private val getTokenMaxBuy: GetTokenMaxBuy,
	private val walletCurrencyPositionFinderService: WalletCurrencyPositionFinderService,
	private val walletFinderService: WalletFinderService,
) : QueryHandler<GetTokenMaxBuyQuery.Result, GetTokenMaxBuyQuery> {

	override val query = GetTokenMaxBuyQuery::class

	override fun handle(query: GetTokenMaxBuyQuery): GetTokenMaxBuyQuery.Result {
		val latestWalletPosition = walletCurrencyPositionFinderService.findLatestPositionByWalletId(query.walletId)
			?: return GetTokenMaxBuyQuery.Result(maxBuy = BigInteger.ZERO)

		val balance = latestWalletPosition.totalBought - latestWalletPosition.totalSold
		if (balance == BigInteger.ZERO) return GetTokenMaxBuyQuery.Result(maxBuy = BigInteger.ZERO)

		val wallet = walletFinderService.getByIdAndUserId(query.walletId, query.userId)
		val maxBuy = when (wallet.chain.type) {
			ChainType.EVM -> getTokenMaxBuy(
				targetAmount = balance,
				tokenAddress = query.tokenAddress,
				chainId = wallet.chain.evmId,
				dexPairInfo = query.dexPairInfo as GetDex.Evm,
			)
			// TODO: Implement solana, hotfix for now
			ChainType.SOLANA -> NativeAmount(BigDecimal("100")).toBase(Chain.SOLANA).amount
		}

		return GetTokenMaxBuyQuery.Result(maxBuy = maxBuy)
	}
}
