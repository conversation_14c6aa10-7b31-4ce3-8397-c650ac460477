package com.cleevio.fatbot.application.module.bot.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.net.URI
import java.time.Instant
import java.util.UUID

interface FindAllBotMarketPosition {
	operator fun invoke(
		userId: UUID?,
		botIds: Set<UUID>?,
		searchString: String?,
		isBotMarketPositionActive: Boolean?,
		fileToUrlMapper: FileToUrlMapper,
	): List<Result>

	data class Result(
		val id: UUID,
		val state: BotMarketPositionState,
		val tokenName: String,
		val tokenSymbol: String,
		val tokenImageUrl: URI?,
		val positionOpenedAt: Instant,
		val positionClosedAt: Instant?,
		override val buyExchangeRate: BigDecimal,
		override val sellExchangeRate: BigDecimal,
		override val botId: UUID,
		override val chain: Chain,
		override val tokenAddress: AddressWrapper,
		override val tokenDecimals: Int,
		override val tokenAmountBought: BaseAmount,
		override val tokenAcquisitionCost: BaseAmount,
		override val tokenAmountSold: BaseAmount,
		override val tokenDispositionCost: BaseAmount,
		override val tokenAcquisitionCostUsd: BigDecimal,
	) : BotMarketPositionCalculator.Input
}
