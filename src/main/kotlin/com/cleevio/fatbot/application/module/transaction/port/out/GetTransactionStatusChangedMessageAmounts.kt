package com.cleevio.fatbot.application.module.transaction.port.out

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import java.math.BigDecimal
import java.util.UUID

interface GetTransactionStatusChangedMessageAmounts {

	data class Result(
		val userId: UUID,
		val tokenNativeAmount: NativeAmount?,
		val currencyNativeAmount: NativeAmount?,
		val currencyAmountUsd: BigDecimal?,
	)

	/**
	 * Returns a map of Transaction ID to Result object
	 */
	operator fun invoke(transactionIds: Set<UUID>): Map<UUID, Result>
}
