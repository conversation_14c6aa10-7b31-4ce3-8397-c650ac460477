package com.cleevio.fatbot.application.module.userleaderbord.port.out

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollAsc
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import java.math.BigDecimal
import java.util.UUID

interface SearchUserLeaderboard {

	operator fun invoke(infiniteScroll: InfiniteScrollAsc.Integer): InfiniteScrollSlice<Result, Int>

	data class Result(
		val userId: UUID,
		val rank: Int,
		val multiplier: BigDecimal,
		val donutsGained: BigDecimal,
		val email: String,
		val volume: BigDecimal,
	)
}
