package com.cleevio.fatbot.application.module.bottransaction.service

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.adapter.out.solana.GetTransactionResultsSVM
import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalances
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.plus
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.getAssociatedTokenAddress
import com.cleevio.fatbot.application.common.util.mapNotNullToSet
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.util.receiveChunk
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.botmarket.finder.BotMarketPositionFinderService
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.bottransaction.finder.BotTransactionFinderService
import com.cleevio.fatbot.application.module.bottransaction.locks.BOT_TRANSACTION_MODULE
import com.cleevio.fatbot.application.module.bottransaction.locks.PROCESS_BOT_TRANSACTION_STATUSES
import com.cleevio.fatbot.application.module.bottransaction.locks.PROCESS_TX
import com.cleevio.fatbot.application.module.bottransaction.port.out.BroadcastBotTransactionCreated
import com.cleevio.fatbot.application.module.bottransaction.port.out.FindAllBotMarketPositionIdsByTokenAddressAndBotWalletId
import com.cleevio.fatbot.application.module.bottransaction.port.out.FindAllBotTokenInfo
import com.cleevio.fatbot.application.module.bottransaction.port.out.FindAllBotWalletIdToBotId
import com.cleevio.fatbot.application.module.bottransaction.port.out.FindAllPendingBotTransactions
import com.cleevio.fatbot.application.module.bottransaction.port.out.UpdateHeartBeat
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.heartbeat.finder.HeartBeatFinderService
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_BOTS
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_MAKING_MODULE
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailure
import com.cleevio.fatbot.application.module.transaction.constant.TransactionPending
import com.cleevio.fatbot.application.module.transaction.constant.TransactionResult
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransferCurrencyTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransferTokenTransactionSuccess
import com.cleevio.fatbot.application.module.userstatistics.service.UpdateTradedAmountService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.botmarket.BotMarketPosition
import com.cleevio.fatbot.domain.bottransaction.BotTransaction
import com.cleevio.fatbot.domain.heartbeat.HeartBeatCreateService
import com.cleevio.fatbot.domain.heartbeat.HeartBeatType
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.TryLock
import io.sentry.spring.jakarta.tracing.SentrySpan
import io.sentry.spring.jakarta.tracing.SentryTransaction
import jakarta.annotation.PreDestroy
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Clock
import java.time.Instant
import java.util.UUID
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

private const val BOT_TRANSACTION_VERIFICATION_LIMIT = 10
private val BOT_TRANSACTION_GRACE_PERIOD = 15.seconds.toJavaDuration()
private const val POSITION_REOPEN_RETRY_LIMIT = 1

@Service
class BotTransactionProcessingService(
	private val findAllPendingBotTransactions: FindAllPendingBotTransactions,
	private val heartBeatFinderService: HeartBeatFinderService,
	private val heartBeatCreateService: HeartBeatCreateService,
	private val updateHeartBeat: UpdateHeartBeat,
	private val botTransactionFinderService: BotTransactionFinderService,
	private val getTransactionResultsSVM: GetTransactionResultsSVM,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val findAllBotMarketPositionIdsByTokenAddressAndBotWalletId:
	FindAllBotMarketPositionIdsByTokenAddressAndBotWalletId,
	private val findAllBotWalletIdToBotId: FindAllBotWalletIdToBotId,
	private val botMarketPositionFinderService: BotMarketPositionFinderService,
	private val botWalletFinderService: BotWalletFinderService,
	private val broadcastBotTransactionCreated: BroadcastBotTransactionCreated,
	private val findAllBotTokenInfo: FindAllBotTokenInfo,
	private val fileUrlMapper: FileUrlMapper,
	private val getTokenPrices: GetTokenPrices,
	private val updateTradedAmountService: UpdateTradedAmountService,
	private val rpcClient: RpcClient,
	private val clock: Clock,
) {

	private val logger = logger()
	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	@Lazy
	@Autowired
	private lateinit var self: BotTransactionProcessingService

	val pendingTransactionIdChannel = Channel<UUID>(capacity = Channel.UNLIMITED)
	val pendingTransactionIdsChunkedChannel = Channel<List<UUID>>(capacity = Channel.UNLIMITED)

	@Transactional(transactionManager = "matchMakingTransactionManager")
	@TryLock(module = BOT_TRANSACTION_MODULE, lockName = PROCESS_TX)
	fun tryStartProcessor() {
		val heartBeat = heartBeatFinderService.findByType(type = HeartBeatType.BOT_TRANSACTION)
			?: heartBeatCreateService.create(type = HeartBeatType.BOT_TRANSACTION)

		// If it's active, early return
		val now = Instant.now(clock)
		if (heartBeat.isActive(now = now)) return

		logger.info("$PROCESS_BOT_TRANSACTION_STATUSES cron started")

		// Update heart beat
		heartBeat.beat(now)

		// Launch processor
		scope.launch {
			// periodically updates hearth beat, to not let other cron launch this
			launch { heartBeat() }

			// periodically polls DB and pulls new bot transaction ids into the loop
			launch { botTransactionDbPoller() }

			// takes channel of ids and batches them to a list
			launch { botTransactionIdBatching() }

			// does the actual processing of transactions
			launch { botTransactionStatusProcessor() }
		}

		logger.info("$PROCESS_BOT_TRANSACTION_STATUSES cron launched tx processing on this pod")
	}

	suspend fun heartBeat() {
		while (true) {
			val now = Instant.now(clock)
			updateHeartBeat(now = now, type = HeartBeatType.BOT_TRANSACTION)
			delay(1.seconds)
		}
	}

	suspend fun botTransactionDbPoller() {
		var lastId: UUID? = null
		while (true) {
			val pending = findAllPendingBotTransactions(greaterThanId = lastId)
			pending.forEach {
				pendingTransactionIdChannel.send(it)
				lastId = it
			}

			// Delay by cca. Solana block creation
			delay(400)
		}
	}

	suspend fun botTransactionIdBatching() {
		val maxChunkSize = 100
		while (true) {
			val chunkedIds = pendingTransactionIdChannel.receiveChunk(maxSize = maxChunkSize)
			if (chunkedIds.size == maxChunkSize) {
				// we hit the max chunk size so we immediately try next chunk, no delay
				// assuming there are more ids to be processed
				pendingTransactionIdsChunkedChannel.send(chunkedIds)
			} else if (chunkedIds.isNotEmpty()) {
				// if received ids are not empty we send them for processing, however for next batch we will wait a bit
				// as it's highly likely that channel does not contain enough ids
				pendingTransactionIdsChunkedChannel.send(chunkedIds)
				delay(200)
			} else {
				// nothing was received, so we wait longer
				delay(400)
			}
		}
	}

	suspend fun botTransactionStatusProcessor() {
		for (chunk in pendingTransactionIdsChunkedChannel) {
			runCatching {
				logger.info("Going to check status of ${chunk.size} bot tx")
				self.process(pendingTransactionIds = chunk)
			}
				.onSuccess { stillPending -> stillPending.forEach { pendingTransactionIdChannel.send(it) } }
				.onFailure {
					delay(400) // backoff here for a bit
					pendingTransactionIdsChunkedChannel.send(chunk) // resend for another try
					logger.error("Error while processing bot tx chunk: ", it)
				}
		}
	}

	/**
	 * Returns list of transaction ids that are still pending
	 */
	@Transactional(transactionManager = "matchMakingTransactionManager")
	@SentryTransaction(operation = "scheduled.process-bot-transaction-statuses")
	fun process(pendingTransactionIds: List<UUID>): List<UUID> {
		val pending = botTransactionFinderService
			.getAllByIds(pendingTransactionIds.toSet())
			.filter { it.status == TransactionStatus.PENDING }
			.sortedBy { it.createdAt }

		if (pending.isEmpty()) return emptyList()

		val txHashToTransaction = pending.associateBy { it.txHash }
		val txHashToResult = getTransactionResultsSVM(txHashToTransaction.keys)

		val tokensOfBuyTransactions = getTokensFromSuccessfulBuyTransactions(txHashToResult, txHashToTransaction)
		val tokenToPrice = getTokenPrices(tokens = tokensOfBuyTransactions)

		val tokenAddresses = pending.mapNotNullToSet { it.tokenAddress }
		val tokenAddressToInfo = findAllBotTokenInfo(tokenAddresses, fileUrlMapper::map).associateBy { it.tokenAddress }

		val botWalletIds = pending.mapToSet { it.botWalletId }

		val exchangeRate = getUsdExchangeRate(cryptoCurrency = CryptoCurrency.SOL)

		val marketPositionIds = findAllBotMarketPositionIdsByTokenAddressAndBotWalletId(
			input = pending
				.filter { it.tokenAddress != null }
				.map {
					FindAllBotMarketPositionIdsByTokenAddressAndBotWalletId.Input(
						tokenAddress = it.tokenAddress!!,
						botWalletId = it.botWalletId,
					)
				},
		)

		val botWalletIdToBotId = findAllBotWalletIdToBotId(botWalletIds = botWalletIds)

		// Note: We have lock for whole bot trading acquired, and everything done after this is blocking trades
		// be very cautious adding anything here!
		// 	All updates to bot market position must be done under global matchmaking lock, otherwise optimistic lock
		// exceptions will start to happen.
		// 	Bot transactions can be acquired before, as they are updated only by this function (so no optimistic lock
		// can happen)
		return self.updateState(
			pending = pending,
			marketPositionIds = marketPositionIds.toSet(),
			txHashToResult = txHashToResult,
			txHashToTransaction = txHashToTransaction,
			exchangeRate = exchangeRate,
			tokenAddressToInfo = tokenAddressToInfo,
			tokenToPrice = tokenToPrice,
			botWalletIdToBotId = botWalletIdToBotId,
		)
	}

	// Warning: This function acquires lock that blocks whole bot trading to not create conflict, so make sure it's fast
	@SentrySpan
	@Lock(module = MATCH_MAKING_MODULE, lockName = MATCH_BOTS)
	fun updateState(
		pending: List<BotTransaction>,
		marketPositionIds: Set<UUID>,
		txHashToResult: Map<TxHash, TransactionResult>,
		txHashToTransaction: Map<TxHash, BotTransaction>,
		exchangeRate: BigDecimal,
		tokenAddressToInfo: Map<AddressWrapper, FindAllBotTokenInfo.Result>,
		tokenToPrice: Map<ChainAddress, BaseAmount>,
		botWalletIdToBotId: Map<UUID, UUID>,
	): List<UUID> {
		val botMarketPositionIdToBotMarketPosition = botMarketPositionFinderService
			.getAllByIds(marketPositionIds)
			.associateBy { it.botWalletId to it.tokenAddress }

		val now = Instant.now(clock)

		val txHashToBalanceUpdate = txHashToResult.mapValues { (txHash, result) ->
			val tx = txHashToTransaction.getValue(txHash)
			when (result) {
				is TransactionPending -> handlePending(
					tx = tx,
					botMarketPositionIdToBotMarketPosition = botMarketPositionIdToBotMarketPosition,
					now = now,
				)
				is TransactionFailure -> handleFailure(
					tx = tx,
					botMarketPositionIdToBotMarketPosition = botMarketPositionIdToBotMarketPosition,
					balanceChange = result.balanceChange,
					isZeroTokenSell = result.isNoTokensOwnedErrorHint!!, // on Solana hint is always present
					exchangeRateUsd = exchangeRate,
				)

				is TransferCurrencyTransactionSuccess -> {
					tx.markAsTransferSuccess(result, exchangeRate)
					logger.info("Transfer transaction with type: ${tx.type} and transaction result value: $result")
					when (tx.type) {
						BotTransactionType.DEPOSIT -> BaseAmount(result.value)
						BotTransactionType.PORTFOLIO_WITHDRAW -> BaseAmount((result.value + result.fee).negate())
						else -> error("Not supported for transfer transactions.")
					}
				}

				is BuySellTransactionSuccess -> {
					val botTokenInfo = tokenAddressToInfo.getValue(tx.tokenAddress!!)
					tx.markAsSuccess(result, exchangeRate)
					val frozenBalanceRefund = updateMarketPosition(
						botMarketPositionIdToBotMarketPosition = botMarketPositionIdToBotMarketPosition,
						botWalletId = tx.botWalletId,
						botTokenInfo = botTokenInfo,
						type = tx.type,
						amountIn = result.amountIn,
						amountOut = result.amountOut,
						fee = result.fee + result.referralFee,
						exchangeRate = exchangeRate,
						botWalletIdToBotId = botWalletIdToBotId,
						tokenToPrice = tokenToPrice,
					)

					// TODO: Hotfix for now, we were sending lamports in
					val amountOfSolanaTraded = when (tx.type) {
						BotTransactionType.BUY -> tx.amountIn!!.asBaseAmount().toNative(Chain.SOLANA) +
							tx.fatbotFee!!.asBaseAmount().toNative(Chain.SOLANA)

						BotTransactionType.SELL -> tx.amountOut!!.asBaseAmount().toNative(Chain.SOLANA)
						else -> error("Can't happen")
					}

					// TODO: This is very inefficient, as many trades are being updated one by one
					//  also on every @Lock it seems that Hibernate flushes state into DB before executing lock query
// 					updateTradedAmountService.update(
// 						userId = walletIdToUserId.getValue(tx.botWalletId),
// 						tradeUsdAmount = tx.exchangeRateUsd!!.multiply(amountOfSolanaTraded.amount),
// 					)

					result.balanceChange + frozenBalanceRefund
				}

				is TransferTokenTransactionSuccess -> error("Not supported for bots")
				is TransactionSuccess -> error("Not supported for bots")
			}
		}

		val (nonPendingTransactions, pendingTransactions) = pending.partition { it.status != TransactionStatus.PENDING }
		val stillPendingTransactionIds = pendingTransactions.map { it.id }

		if (nonPendingTransactions.isEmpty()) return stillPendingTransactionIds

		val nonPendingTransferTransactions = nonPendingTransactions.filter {
			it.type == BotTransactionType.DEPOSIT || it.type == BotTransactionType.PORTFOLIO_WITHDRAW
		}

		val botWalletIdToBalanceChange = nonPendingTransactions
			.groupBy { it.botWalletId }
			.mapValues { (_, walletTransactions) ->
				walletTransactions.sumOf { tx -> txHashToBalanceUpdate.getValue(tx.txHash).amount }
			}

		val botWalletIdToAcquisitionAmountChange = nonPendingTransferTransactions
			.groupBy { it.botWalletId }
			.mapValues { (_, walletTransactions) ->
				walletTransactions.sumOf { tx -> calculateAcquisitionAmountChange(tx) }
			}

		updateWalletBalances(
			botWalletIdToBalanceChange = botWalletIdToBalanceChange,
			botWalletIdToAcquisitionAmountChange = botWalletIdToAcquisitionAmountChange,
		)

		return stillPendingTransactionIds
	}

	private fun getTokensFromSuccessfulBuyTransactions(
		txHashToResult: Map<TxHash, TransactionResult>,
		txHashToTransaction: Map<TxHash, BotTransaction>,
	) = txHashToResult.mapNotNull { (txHash, result) ->
		val tx = txHashToTransaction.getValue(txHash)
		when (result is BuySellTransactionSuccess && tx.type == BotTransactionType.BUY) {
			true -> tx.tokenAddress!!.toChainAddress(chain = Chain.SOLANA)
			false -> null
		}
	}.toSet()

	private fun calculateAcquisitionAmountChange(tx: BotTransaction): BigDecimal {
		return when (tx.type) {
			BotTransactionType.DEPOSIT -> {
				tx.amountIn!!.asBaseAmount().toNative(Chain.SOLANA).amount * tx.exchangeRateUsd!!
			}

			BotTransactionType.PORTFOLIO_WITHDRAW -> {
				if (tx.status != TransactionStatus.SUCCESS) return BigDecimal.ZERO
				tx.amountOut!!.negate().asBaseAmount().toNative(Chain.SOLANA).amount * tx.exchangeRateUsd!!
			}

			else -> error("Transaction type ${tx.type} is not supported for acquisition amount calculation")
		}
	}

	private fun handlePending(
		tx: BotTransaction,
		botMarketPositionIdToBotMarketPosition: Map<Pair<UUID, AddressWrapper>, BotMarketPosition>,
		now: Instant,
	): BaseAmount {
		tx.increaseTryCount(
			verificationLimit = BOT_TRANSACTION_VERIFICATION_LIMIT,
			now = now,
			gracePeriod = BOT_TRANSACTION_GRACE_PERIOD,
		)

		val frozenBalanceRefund = if (tx.status == TransactionStatus.NOT_LANDED) {
			when (tx.type) {
				BotTransactionType.BUY -> {
					val marketPosition = botMarketPositionIdToBotMarketPosition.getValue(
						tx.botWalletId to tx.tokenAddress!!,
					)
					logger.warn(
						"Bot market position ${marketPosition.id} BUY tx has not landed! " +
							"BotWalletId: ${marketPosition.botWalletId} " +
							"Token: ${marketPosition.tokenAddress.getAddressString()}",
					)

					val frozenBalanceRefund = when (marketPosition.state == BotMarketPositionState.OPENED) {
						// when sell transaction did not start yet, all funds are refunded
						true -> marketPosition.returnAllFrozenBalance().asBaseAmount()
						// when sell transaction is already created, we can only refund frozen amount for buy
						false -> marketPosition.returnBuyAmountFrozen().asBaseAmount()
					}

					marketPosition.markAsOpenNotLanded()

					frozenBalanceRefund
				}

				BotTransactionType.SELL -> {
					val marketPosition = botMarketPositionIdToBotMarketPosition.getValue(
						tx.botWalletId to tx.tokenAddress!!,
					)
					logger.warn(
						"Bot market position ${marketPosition.id} SELL tx has not landed! " +
							"BotWalletId: ${marketPosition.botWalletId} " +
							"Token: ${marketPosition.tokenAddress.getAddressString()}",
					)

					val frozenBalanceRefund = marketPosition.returnFeeToSellFrozen().asBaseAmount()

					if (marketPosition.state in BotMarketPositionState.openStates) {
						marketPosition.reopenFromSellNotLanding()
					}

					frozenBalanceRefund
				}

				BotTransactionType.DEPOSIT -> BaseAmount.ZERO // do nothing
				BotTransactionType.PORTFOLIO_WITHDRAW -> {
					// TODO: What if withdraw from portfolio does not land on chain?
					//  for now just don't do nothing, let user repeat it
					BaseAmount.ZERO
				}

				BotTransactionType.APPROVE -> error("Not supported for bots")
			}
		} else {
			BaseAmount.ZERO
		}

		return BaseAmount.ZERO + frozenBalanceRefund
	}

	fun handleFailure(
		tx: BotTransaction,
		botMarketPositionIdToBotMarketPosition: Map<Pair<UUID, AddressWrapper>, BotMarketPosition>,
		balanceChange: BaseAmount,
		isZeroTokenSell: Boolean,
		exchangeRateUsd: BigDecimal,
	): BaseAmount {
		tx.markAsFailed(exchangeRateUsd = exchangeRateUsd)

		val frozenBalanceRefund = when (tx.type) {
			BotTransactionType.BUY -> {
				val marketPosition = botMarketPositionIdToBotMarketPosition.getValue(
					tx.botWalletId to tx.tokenAddress!!,
				)
				logger.warn(
					"Bot market position ${marketPosition.id} BUY tx has failed! " +
						"BotWalletId: ${marketPosition.botWalletId} " +
						"Token: ${marketPosition.tokenAddress.getAddressString()}",
				)

				// When error happened already in the buy phase, all the frozen fees were already returned
				val frozenBalanceRefund = when (marketPosition.state == BotMarketPositionState.OPENED) {
					// when sell transaction did not start yet, all funds are refunded
					true -> marketPosition.returnAllFrozenBalance().asBaseAmount()
					// when sell transaction is already created, we can only refund frozen amount for buy
					false -> marketPosition.returnBuyAmountFrozen().asBaseAmount()
				}

				marketPosition.markAsOpenFailed()

				frozenBalanceRefund
			}

			BotTransactionType.SELL -> {
				val marketPosition = botMarketPositionIdToBotMarketPosition.getValue(
					tx.botWalletId to tx.tokenAddress!!,
				)
				logger.warn(
					"Bot market position ${marketPosition.id} SELL tx has failed! " +
						"BotWalletId: ${marketPosition.botWalletId} " +
						"Token: ${marketPosition.tokenAddress.getAddressString()}",
				)

				// Note: It might be dangerous to reopen position in here due to fact
				// that if there is error e.g. in contract pumpfun sell code, we could siphon the whole bot
				// balance via repeated reopening and failing of transactions. For that case,
				// they rather stay in pending close position

				val botWalletAddress = botWalletFinderService.getById(marketPosition.botWalletId).address
				val tokenAccount = getAssociatedTokenAddress(
					mint = marketPosition.tokenAddress.getSolanaPublicKey(),
					owner = botWalletAddress.getSolanaPublicKey(),
					programId = SolanaConstants.TOKEN_PROGRAM_ID,
				)

				// Note 2: It can still happen, that even if the sell transaction failed, buy transaction is still
				// pending and eventually will be processed. That would mean, that at this point we do not find any
				// tokens on the account.

				if (marketPosition.state in BotMarketPositionState.closedStates) {
					// Even if this transaction failed, the position is already closed at this point so
					// there is no reason for trying to reopen it

					// This can happen in rare occasion, if we sell token, then token graduates, we immediately try to
					// sell token on pumpswap pair, the first sell is successful and so the sell on pumpswap fails on
					// zero tokens
				} else if (marketPosition.state in BotMarketPositionState.openingErrorStates) {
					// Open failed so no point in trying to reopen
				} else if (isZeroTokenSell && marketPosition.retryCount == 0) {
					// If this is first time error happened and the error is that we are selling zero tokens, we
					// re-open this position without checking the token amount

					// This solves an error, when we first buy token, buy is pending, we then sell token, sell tx lands
					// however as buy is still pending, there is zero token sell error (or if buy and sell are
					// in the same block but sell is before buy)
					marketPosition.reopenFromSellFailed()
				} else {
					// if it's any other type of error, or it's second time it happened,
					// we check if wallet owns some tokens
					val tokenAccountBalance = rpcClient
						.getTokenAccountBalances(listOf(tokenAccount), Commitment.CONFIRMED)
						.sumOf { it ?: BigInteger.ZERO }

					if (tokenAccountBalance > BigInteger.ZERO && marketPosition.retryCount < POSITION_REOPEN_RETRY_LIMIT) {
						marketPosition.reopenFromSellFailed()
					} else {
						marketPosition.markAsUnknownError()
					}
				}

				when (marketPosition.state) {
					// When we reopen the position, we want to keep all sell fees frozen still, as new sell is expected
					BotMarketPositionState.OPENED -> BaseAmount.ZERO
					else -> marketPosition.returnFeeToSellFrozen().asBaseAmount()
				}
			}

			BotTransactionType.DEPOSIT -> BaseAmount.ZERO
			BotTransactionType.PORTFOLIO_WITHDRAW -> BaseAmount.ZERO
			BotTransactionType.APPROVE -> error("Not supported for bots")
		}

		return balanceChange + frozenBalanceRefund
	}

	private fun updateMarketPosition(
		botMarketPositionIdToBotMarketPosition: Map<Pair<UUID, AddressWrapper>, BotMarketPosition>,
		botWalletId: UUID,
		botTokenInfo: FindAllBotTokenInfo.Result,
		type: BotTransactionType,
		amountIn: BigInteger,
		amountOut: BigInteger,
		fee: BigInteger,
		exchangeRate: BigDecimal,
		botWalletIdToBotId: Map<UUID, UUID>,
		tokenToPrice: Map<ChainAddress, BaseAmount>,
	): BaseAmount {
		val marketPosition = botMarketPositionIdToBotMarketPosition.getValue(
			botWalletId to botTokenInfo.tokenAddress,
		)
		val botId = botWalletIdToBotId.getValue(botWalletId)

		val amountFrozen = when (type) {
			BotTransactionType.BUY -> {
				marketPosition.setBuyDataFromChain(
					amountOut = amountOut,
					amountIn = amountIn,
					fee = fee,
					exchangeRate = exchangeRate,
				)

				val marketPositionWithTokenInfo = marketPosition.toCalculatorInput(
					botId = botWalletId,
					tokenName = botTokenInfo.tokenName,
					tokenSymbol = botTokenInfo.tokenSymbol,
					tokenDecimals = botTokenInfo.tokenDecimals,
					tokenImageUrl = botTokenInfo.tokenImageUrl,
				)

				broadcastBotTransactionCreated(
					transactionType = BotTransactionType.BUY,
					botId = botId,
					botMarketPosition = marketPosition,
					botTokenInfo = botTokenInfo,
					positionUsdValue = BotMarketPositionCalculator.toBotMarketPositionResult(
						botMarketPosition = marketPositionWithTokenInfo,
						tokenAddressToTokenPrice = tokenToPrice,
						currentExchangeRate = exchangeRate,
					),
				)

				marketPosition.returnBuyAmountFrozen()
			}

			BotTransactionType.SELL -> {
				marketPosition.setSellDataFromChain(
					amountOfTokensSold = amountIn,
					amountOfBaseCurrencyReceived = amountOut,
					fee = fee,
					exchangeRate = exchangeRate,
				)

				val marketPositionWithTokenInfo = marketPosition.toCalculatorInput(
					botId = botId,
					tokenName = botTokenInfo.tokenName,
					tokenSymbol = botTokenInfo.tokenSymbol,
					tokenDecimals = botTokenInfo.tokenDecimals,
					tokenImageUrl = botTokenInfo.tokenImageUrl,
				)

				broadcastBotTransactionCreated(
					transactionType = BotTransactionType.SELL,
					botId = botId,
					botMarketPosition = marketPosition,
					botTokenInfo = botTokenInfo,
					positionUsdValue = BotMarketPositionCalculator.toBotMarketPositionResult(
						botMarketPosition = marketPositionWithTokenInfo,
						// Sell position is always a closed position - no need for fetching token prices
						tokenAddressToTokenPrice = mapOf(),
						currentExchangeRate = exchangeRate,
					),
				)

				marketPosition.returnFeeToSellFrozen()
			}

			BotTransactionType.DEPOSIT -> TODO("Not supported yet")
			BotTransactionType.PORTFOLIO_WITHDRAW -> TODO("Not supported yet")
			BotTransactionType.APPROVE -> error("Not supported on Solana")
		}

		return amountFrozen.asBaseAmount()
	}

	fun updateWalletBalances(
		botWalletIdToBalanceChange: Map<UUID, BigInteger>,
		botWalletIdToAcquisitionAmountChange: Map<UUID, BigDecimal>,
	) {
		val allWalletIds = botWalletIdToBalanceChange.keys + botWalletIdToAcquisitionAmountChange.keys
		val walletById = botWalletFinderService.getAllByIds(allWalletIds).associateBy { it.id }

		allWalletIds.forEach { walletId ->
			val wallet = walletById.getValue(walletId)

			botWalletIdToBalanceChange[walletId]?.let { balanceChange ->
				wallet.increaseBalance(balanceChange)
			}

			botWalletIdToAcquisitionAmountChange[walletId]?.let { acquisitionChange ->
				logger.info("Bot wallet with id: $walletId with acquisition amount: $acquisitionChange")
				wallet.increaseAcquisitionValue(acquisitionChange)
			}
		}
	}

	@PreDestroy
	fun preDestroy() {
		logger.info("Pre destroy signal received")
		scope.cancel()
		logger.info("Processor scope cancelled")
	}
}
