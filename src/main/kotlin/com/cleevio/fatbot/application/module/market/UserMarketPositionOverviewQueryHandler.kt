package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.module.exchangerate.port.out.GetLatestExchangeRateSnapshots
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator
import com.cleevio.fatbot.application.module.market.query.UserMarketPositionOverviewQuery
import com.cleevio.fatbot.application.module.market.service.AggregateMarketPositionsService
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.port.out.GetLatestWalletCurrencyPositions
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.BigInteger
import java.math.MathContext
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit

/*
TODO: If business decides WalletPositions should remain in the portfolio overview
  this Query should be renamed to PortfolioOverview
 */
@Component
class UserMarketPositionOverviewQueryHandler(
	private val aggregateMarketPositionsService: AggregateMarketPositionsService,
	private val clock: Clock,
	private val getLatestWalletCurrencyPositions: GetLatestWalletCurrencyPositions,
	private val usdConverter: UsdConverter,
	private val getLatestExchangeRateSnapshots: GetLatestExchangeRateSnapshots,
	private val firebaseUserFinderService: FirebaseUserFinderService,
	private val properties: ChainProperties,
) : QueryHandler<UserMarketPositionOverviewQuery.Result, UserMarketPositionOverviewQuery> {

	override val query = UserMarketPositionOverviewQuery::class

	@SentrySpan
	override fun handle(query: UserMarketPositionOverviewQuery): UserMarketPositionOverviewQuery.Result {
		val now = Instant.now(clock)

		val chains = when (query.useSelectedChains) {
			true -> firebaseUserFinderService.getById(id = query.userId).selectedChains
			false -> properties.enabledChains
		}

		val marketPositionSumNow = aggregateMarketPositionsService.byChainNow(
			userId = query.userId,
			chains = chains,
			walletId = query.filter.walletId,
		)

		val marketPositionsValueUsd24hAgo = aggregateMarketPositionsService.totalValueUsd24hAgo(
			userId = query.userId,
			walletId = query.filter.walletId,
			chains = chains,
			now = now,
		)

		val walletCurrencyPositionsNow = getLatestWalletCurrencyPositions(
			userId = query.userId,
			walletId = query.filter.walletId,
			chains = chains,
			createdBefore = null,
		)

		val walletCurrencyPositions24hAgo = getLatestWalletCurrencyPositions(
			userId = query.userId,
			walletId = query.filter.walletId,
			chains = chains,
			createdBefore = now.minus(24, ChronoUnit.HOURS),
		)

		// Current value
		val walletValueUsdNow = walletCurrencyPositionsNow.sumOf {
			val balance = it.totalBought - it.totalSold

			usdConverter.baseToUsd(balance.asBaseAmount(), it.chain)
		}
		val marketValueUsdNow = marketPositionSumNow.sumOfInUsd { it.currentValue }
		val totalValueUsdNow = marketValueUsdNow + walletValueUsdNow

		// Current PnL Fraction
		val walletValueAcquisitionCostUsdNow = walletCurrencyPositionsNow.sumOf {
			if (it.totalBought == BigInteger.ZERO) return@sumOf BigDecimal.ZERO
			val averageBuy = it.totalAcquisitionCostUsd.divide(it.totalBought.toBigDecimal(), MathContext.DECIMAL128)

			val balance = it.totalBought - it.totalSold

			balance.toBigDecimal() * averageBuy
		}

		val totalMarketValueAcquisitionCostUsd = marketPositionSumNow.values.sumOf { it.acquisitionValueOwnedTokenUsd }
		val totalAcquisitionUsdNow = totalMarketValueAcquisitionCostUsd + walletValueAcquisitionCostUsdNow

		val pnlFractionNow = calculateFractionChange(totalValueUsdNow, totalAcquisitionUsdNow)

		// Current PnL USD
		val walletPnlUsdNow = walletValueUsdNow - walletValueAcquisitionCostUsdNow
		val marketPnlUsdNow = marketPositionSumNow.values.sumOf { it.currentPnlUsd }
		val totalPnlUsdNow = marketPnlUsdNow + walletPnlUsdNow

		val currencyToRateUsd24h = getLatestExchangeRateSnapshots(
			currencies = CryptoCurrency.entries.toSet(),
			validBefore = now.minus(24, ChronoUnit.HOURS),
		)

		// oneDay change amount Usd
		val walletPositionsValue24hUsd = walletCurrencyPositions24hAgo.sumOf {
			val balance = it.totalBought - it.totalSold
			val exchangeRate = currencyToRateUsd24h[it.chain.currency]

			usdConverter.baseToUsd(balance.asBaseAmount(), it.chain, exchangeRate)
		}

		val totalValue24hUsd = walletPositionsValue24hUsd + marketPositionsValueUsd24hAgo

		val oneDayChangeUsd = totalValueUsdNow - totalValue24hUsd
		val oneDayChangeFraction = calculateFractionChange(totalValueUsdNow, totalValue24hUsd)

		return UserMarketPositionOverviewQuery.Result(
			totalValueAmountUsd = totalValueUsdNow,
			totalPnlAmountUsd = totalPnlUsdNow,
			totalPnlAmountFraction = pnlFractionNow,
			oneDayChangeAmountUsd = oneDayChangeUsd,
			oneDayChangeFraction = oneDayChangeFraction,
		)
	}

	private fun Map<Chain, MarketPositionCalculator.Result>.sumOfInUsd(
		selector: (MarketPositionCalculator.Result) -> NativeAmount,
	): BigDecimal {
		return this
			.entries
			.sumOf { (chain, marketPositionResult) ->
				usdConverter.nativeToUsd(nativeAmount = selector(marketPositionResult), chain = chain)
			}
	}
}
