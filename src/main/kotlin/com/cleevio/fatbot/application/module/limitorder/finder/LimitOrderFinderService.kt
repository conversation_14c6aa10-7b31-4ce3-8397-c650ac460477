package com.cleevio.fatbot.application.module.limitorder.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.module.limitorder.exception.LimitOrderNotFoundException
import com.cleevio.fatbot.domain.limitorder.LimitOrder
import com.cleevio.fatbot.domain.limitorder.LimitOrderRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class LimitOrderFinderService(
	private val limitOrderRepository: LimitOrderRepository,
) : BaseFinderService<LimitOrder>(limitOrderRepository) {

	override fun errorBlock(message: String) = throw LimitOrderNotFoundException(message)

	override fun getEntityType() = LimitOrder::class

	@Transactional(readOnly = true)
	fun getByIdAndUserId(id: UUID, userId: UUID) = limitOrderRepository.findByIdAndUserId(
		id = id,
		userId = userId,
	) ?: errorBlock("Limit order with id: $id not found.")
}
