package com.cleevio.fatbot.application.module.file.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.domain.file.File
import com.cleevio.fatbot.domain.file.FileNotFoundException
import com.cleevio.fatbot.domain.file.FileRepository
import org.springframework.stereotype.Service

@Service
class FileFinderService(fileRepository: FileRepository) : BaseFinderService<File>(fileRepository) {
	override fun errorBlock(message: String) = throw FileNotFoundException(message)
	override fun getEntityType() = File::class
}
