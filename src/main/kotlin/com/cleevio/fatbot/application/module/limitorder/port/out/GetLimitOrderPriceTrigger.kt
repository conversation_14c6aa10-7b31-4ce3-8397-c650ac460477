package com.cleevio.fatbot.application.module.limitorder.port.out

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper

interface GetLimitOrderPriceTrigger {

	data class Result(
		/**
		 * Represents the highest price of a Buy order.
		 * If a trade with lower price is detected, at least this limit order should match
		 */
		val maxBuyPrice: NativeAmount?,
		/**
		 * Represents the lowest price of a Sell order.
		 * If a trade with higher price is detected, at least this limit order should match
		 */
		val minSellPrice: NativeAmount?,
	)

	operator fun invoke(): Map<AddressWrapper, Result>
}
