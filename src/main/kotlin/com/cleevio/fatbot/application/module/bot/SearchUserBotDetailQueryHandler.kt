package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.compareTo
import com.cleevio.fatbot.application.common.crypto.plus
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.bot.constant.BotStatus
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotMarketPosition
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotPortfolioValueSnapshot
import com.cleevio.fatbot.application.module.bot.port.out.GetMyBot
import com.cleevio.fatbot.application.module.bot.query.CompareBotsQuery.BotPortfolioPastValue
import com.cleevio.fatbot.application.module.bot.query.SearchUserBotDetailQuery
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator
import com.cleevio.fatbot.application.module.botmarket.query.SearchBotMarketPositionQuery
import com.cleevio.fatbot.application.module.bottransaction.port.out.SearchBotTransaction
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.token.constant.toInstant
import com.cleevio.fatbot.application.module.transaction.port.out.GetTxDetailUri
import org.springframework.stereotype.Component
import java.net.URI
import java.time.Clock
import java.time.Instant

@Component
class SearchUserBotDetailQueryHandler(
	private val getMyBot: GetMyBot,
	private val findAllBotMarketPosition: FindAllBotMarketPosition,
	private val fileUrlMapper: FileUrlMapper,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val getTokenPrices: GetTokenPrices,
	private val clock: Clock,
	private val searchBotTransaction: SearchBotTransaction,
	private val findAllBotPortfolioValueSnapshot: FindAllBotPortfolioValueSnapshot,
	private val getTxDetailUri: GetTxDetailUri,
	private val usdConverter: UsdConverter,
) : QueryHandler<SearchUserBotDetailQuery.Result, SearchUserBotDetailQuery> {

	override val query = SearchUserBotDetailQuery::class

	override fun handle(query: SearchUserBotDetailQuery): SearchUserBotDetailQuery.Result {
		val bot = getMyBot(userId = query.userId, botId = query.filter.botId)

		val botIdSet = setOf(query.filter.botId)

		// TODO: This can be optimized, so we only fetch all open positions (could be covered by index)
		//  and last 8 closed positions. Then it would be only necessary to fetch count of open / closed
		//  market positions instead of loading them all and counting in memory
		val allBotMarketPositions = findAllBotMarketPosition(
			userId = query.userId,
			botIds = botIdSet,
			searchString = null,
			isBotMarketPositionActive = null,
			fileToUrlMapper = fileUrlMapper::map,
		)

		val openPositions = allBotMarketPositions.filter { it.positionClosedAt == null }
		val openPositionTokens = openPositions.mapToSet { it.tokenAddress.toChainAddress(it.chain) }
		val tokenAddressToTokenPrice = getTokenPrices(tokens = openPositionTokens)

		val exchangeRate = getUsdExchangeRate(bot.walletChain.currency)

		val marketPositionsSum = BotMarketPositionCalculator.sum(
			positions = openPositions,
			tokenAddressToTokenPrice = tokenAddressToTokenPrice,
			chainToExchangeRate = mapOf(bot.walletChain to exchangeRate),
		)

		val marketPositionsValueUsd = marketPositionsSum.currentValueUsd
		val rentLockedInOpenPositionsUsd = marketPositionsSum.rentUsd

		val walletBalanceUsd = usdConverter.baseToUsd(bot.walletBalance, bot.walletChain, exchangeRate)
		val totalValueUsd = marketPositionsValueUsd + walletBalanceUsd + rentLockedInOpenPositionsUsd

		val now = Instant.now(clock)
		val from = query.filter.timeRange.toInstant(now)
		val positionsResults = allBotMarketPositions
			.map { position ->
				val positionUsdValue = BotMarketPositionCalculator.toBotMarketPositionResult(
					botMarketPosition = position,
					tokenAddressToTokenPrice = tokenAddressToTokenPrice,
					currentExchangeRate = exchangeRate,
				)

				SearchBotMarketPositionQuery.Result(
					id = position.id,
					state = position.state,
					tokenAddress = position.tokenAddress,
					tokenDetailUrl = position.tokenAddress.getLinkToExplorer(position.chain).let { URI.create(it) },
					tokenName = position.tokenName,
					tokenSymbol = position.tokenSymbol,
					tokenChain = position.chain,
					tokenImageUrl = position.tokenImageUrl,
					openValueUsd = positionUsdValue.acquisitionValueUsd,
					closeValueUsd = position.positionClosedAt?.let { positionUsdValue.currentValueUsd },
					openTimeStampAt = position.positionOpenedAt,
					closedTimeStampAt = position.positionClosedAt,
					pnlAmountUsd = positionUsdValue.currentPnlUsd,
					pnlAmountFraction = positionUsdValue.currentPnlFraction,
					currentValueUsd = positionUsdValue.currentValueUsd,
				)
			}

		val positionsInSelectedTimeRange = positionsResults.filter { it.openTimeStampAt in from..now }

		val pastPortfolioValues = findAllBotPortfolioValueSnapshot(
			botIds = botIdSet,
			from = query.filter.timeRange.toInstant(now),
			to = now,
		)

		val botTransactions = searchBotTransaction(
			userId = query.userId,
			botIds = botIdSet,
			searchString = null,
			infiniteScroll = InfiniteScrollDesc.UUID(size = 1), // We only need latest TX for the result
			fileToUrlMapper = fileUrlMapper::map,
			txHashToURIMapper = { txHash, chain -> getTxDetailUri(txHash, chain) },
		)

		val transactionCount = BotMarketPositionCalculator.numOfTransactions(positionsInSelectedTimeRange)
		val profitableMarketPositions = BotMarketPositionCalculator.numOfProfitablePositions(positionsInSelectedTimeRange)
		val lossMarketPositions = BotMarketPositionCalculator.numOfLossPositions(positionsInSelectedTimeRange)
		val positionsVolume = BotMarketPositionCalculator.calculatePositionsVolume(positionsInSelectedTimeRange)

		val tokenAccountRent = SolanaConstants.BOT_BUY_RENT_BASE_AMOUNT
		val buyFeesToFreeze = SolanaConstants.BOT_BUY_FEE_FREEZE_BASE_AMOUNT
		val sellFeesToFreeze = SolanaConstants.BOT_SELL_FEE_FREEZE_BASE_AMOUNT
		val minimumRentOnWalletAfterBuy = SolanaConstants.MINIMUM_RENT_THRESHOLD

		val botNeeds = bot.tradeAmount +
			tokenAccountRent +
			buyFeesToFreeze +
			sellFeesToFreeze +
			minimumRentOnWalletAfterBuy

		val activeBotMarketPositions = positionsResults
			.filter { it.closedTimeStampAt == null } // still open
			.sortedByDescending { it.openTimeStampAt }
			.take(8)

		val closedBotMarketPositions = positionsResults
			.filter { it.closedTimeStampAt != null } // already closed
			.sortedByDescending { it.closedTimeStampAt }
			.take(8)

		val botStatus = when {
			!bot.isActive -> BotStatus.DEACTIVATED
			bot.walletBalance < botNeeds && activeBotMarketPositions.isNotEmpty() ->
				BotStatus.INSUFFICIENT_BALANCE_FUNDS_IN_OPEN_POSITIONS
			bot.walletBalance < botNeeds -> BotStatus.INSUFFICIENT_BALANCE
			bot.remainingBuyFrequency == 0L -> BotStatus.BUY_DAILY_LIMIT_REACHED
			transactionCount == 0 -> BotStatus.NO_PURCHASE
			else -> BotStatus.HAS_PURCHASED
		}

		val botPortfolioLastValues = pastPortfolioValues.map { portfolioValue ->
			BotPortfolioPastValue(
				createdAt = portfolioValue.snapshotMadeAt,
				portfolioValueUsd = portfolioValue.portfolioValueUsd,
			)
		} + BotPortfolioPastValue(
			createdAt = now,
			portfolioValueUsd = totalValueUsd,
		)

		val botTotalPnlAmountUsd = totalValueUsd - bot.walletAcquisitionValueUsd
		val botTotalPnlAmountFraction = calculateFractionChange(totalValueUsd, bot.walletAcquisitionValueUsd)

		return SearchUserBotDetailQuery.Result(
			botId = bot.botId,
			botWalletAddress = bot.walletAddress,
			botWalletChain = bot.walletChain,
			botWalletBalanceUsd = walletBalanceUsd,
			botName = bot.name,
			isActive = bot.isActive,
			numberOfActiveDays = bot.numberOfActiveDays,
			botAvatarFileId = bot.botAvatarFileId,
			buyFrequency = bot.buyFrequency.toBigInteger(),
			remainingBuyFrequency = bot.remainingBuyFrequency.toBigInteger(),
			buyFrequencyLastResetAt = bot.buyFrequencyLastResetAt,
			botTotalValueAmountUsd = totalValueUsd,
			botTotalPnlAmountUsd = botTotalPnlAmountUsd,
			botTotalPnlAmountFraction = botTotalPnlAmountFraction,
			botPortfolioLastValues = botPortfolioLastValues,
			transactionsCount = transactionCount,
			buyTransactionsCount = positionsInSelectedTimeRange.size,
			profitableTransactionsCount = profitableMarketPositions,
			lossTransactionsCount = lossMarketPositions,
			transactionsVolumeSum = positionsVolume,
			activeBotMarketPositions = activeBotMarketPositions,
			closedBotMarketPositions = closedBotMarketPositions,
			// Return only last bot transaction
			botTransactions = botTransactions.content.take(1),
			botStatus = botStatus,
		)
	}
}
