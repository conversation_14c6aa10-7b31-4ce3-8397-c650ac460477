package com.cleevio.fatbot.application.module.userfattycard

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.userfattycard.port.out.SearchUserFattyCards
import com.cleevio.fatbot.application.module.userfattycard.query.SearchUserFattyCardsQuery
import org.springframework.stereotype.Component

@Component
class SearchUserFattyCardsQueryHandler(
	private val searchUserFattyCards: SearchUserFattyCards,
) : QueryHandler<List<SearchUserFattyCardsQuery.Result>, SearchUserFattyCardsQuery> {

	override val query = SearchUserFattyCardsQuery::class

	override fun handle(query: SearchUserFattyCardsQuery): List<SearchUserFattyCardsQuery.Result> {
		return searchUserFattyCards(
			userId = query.userId,
			claimed = query.claimed,
			displayed = query.displayed,
		)
	}
}
