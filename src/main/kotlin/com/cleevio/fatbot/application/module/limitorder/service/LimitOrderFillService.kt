package com.cleevio.fatbot.application.module.limitorder.service

import com.cleevio.fatbot.application.module.limitorder.event.LimitOrderMatchedEvent
import com.cleevio.fatbot.application.module.limitorder.finder.LimitOrderFinderService
import com.cleevio.fatbot.application.module.limitorder.locks.LIMIT_ORDER_MODULE
import com.cleevio.fatbot.application.module.limitorder.locks.UPDATE_LIMIT_ORDER
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class LimitOrderFillService(
	private val limitOrderFinderService: LimitOrderFinderService,
	private val applicationEventPublisher: ApplicationEventPublisher,
) {

	@Transactional
	@Lock(module = LIMIT_ORDER_MODULE, lockName = UPDATE_LIMIT_ORDER)
	fun unlock(@LockArgumentParameter id: UUID) {
		limitOrderFinderService.getById(id).unlock()
	}

	@Transactional
	@Lock(module = LIMIT_ORDER_MODULE, lockName = UPDATE_LIMIT_ORDER)
	fun enqueue(@LockFieldParameter("orderId") matchedOrder: LimitOrderMatchingService.Result) {
		val limitOrder = limitOrderFinderService.getById(matchedOrder.orderId)

		limitOrder.lock()
		applicationEventPublisher.publishEvent(
			LimitOrderMatchedEvent(
				orderId = matchedOrder.orderId,
				walletId = matchedOrder.walletId,
				chain = matchedOrder.chain,
				tokenAddress = matchedOrder.tokenAddress,
				pairAddress = matchedOrder.pairAddress,
				type = matchedOrder.type,
				amount = matchedOrder.amountIn,
				walletBalance = matchedOrder.walletBalance,
			),
		)
	}
}
