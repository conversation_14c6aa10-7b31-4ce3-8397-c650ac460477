package com.cleevio.fatbot.application.module.userfattycard.port.out

import com.cleevio.fatbot.application.module.userfattycard.query.GetUserFattyCardsOverviewQuery
import java.math.BigDecimal
import java.util.UUID

interface GetUserFattyCardsOverview {
	operator fun invoke(
		userId: UUID,
		maxCardsEarnedInOneDay: Int,
		tradeAmountForFattyCard: BigDecimal,
	): GetUserFattyCardsOverviewQuery.Result
}
