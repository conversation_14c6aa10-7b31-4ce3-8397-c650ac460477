package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.adapter.out.evm.GetTransactionResultsEVM
import com.cleevio.fatbot.adapter.out.solana.GetTransactionResultsSVM
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.transaction.constant.TransactionResult
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.constant.TransferCurrencyTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransferTokenTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.event.TransactionsStatusesResolvedEvent
import com.cleevio.fatbot.application.module.transaction.finder.TransactionFinderService
import com.cleevio.fatbot.application.module.transaction.port.out.model.TransactionStatusModel
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.walletposition.event.TransactionSuccessfulEvent
import com.cleevio.fatbot.domain.transaction.Transaction
import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.web3j.crypto.SignedRawTransaction
import org.web3j.crypto.TransactionDecoder
import java.math.BigInteger

@Service
class TransactionStatusProcessingService(
	private val transactionFinderService: TransactionFinderService,
	private val transactionStatusUpdateService: TransactionStatusUpdateService,
	private val getTransactionResultsEVM: GetTransactionResultsEVM,
	private val getTransactionResultsSVM: GetTransactionResultsSVM,
	private val getTokenPrices: GetTokenPrices,
	private val applicationEventPublisher: ApplicationEventPublisher,
) {
	private val log = logger()

	fun processTransactionStatuses() {
		val pendingTransactions = transactionFinderService.findAllRealPending().ifEmpty { return }
		log.info("Processing status for ${pendingTransactions.size} transactions...")

		val (transactionsToProcess, invalidTransactions) = pendingTransactions.partition { it.txHash != null }

		invalidTransactions.forEach {
			log.error("Can't process transaction status for transaction without txHash. Transaction ID: ${it.id}")
		}

		val chainToTransaction = transactionsToProcess.groupBy({ it.chain }, { it.txHash!! to it })

		val transactionsStatusModels = chainToTransaction.flatMap { (chain, transactions) ->
			val txHashToResult = getTransactionResults(chain, transactions)

			val statusModels = transactions.mapNotNull { (txHash, transaction) ->
				val transactionResult = runCatching {
					determineTransactionResult(
						resultMap = txHashToResult,
						txHash = txHash,
						transaction = transaction,
					)
				}.onFailure { log.error("Error getting transactionResult for txHash ${txHash.txHash}. Error: $it") }.getOrNull()

				log.info("Processed transaction with txHash: ${txHash.txHash} result was: $transactionResult")

				if (transactionResult == null) return@mapNotNull null

				transactionStatusUpdateService.updateStatus(transactionId = transaction.id, transactionResult = transactionResult)

				TransactionStatusModel(
					transactionId = transaction.id,
					transactionStatus = transactionResult.status,
					txHash = txHash,
					walletId = transaction.walletId,
				)
			}

			statusModels
		}

		val affectedWalletIds = transactionsStatusModels.mapToSet { it.walletId }
		affectedWalletIds.forEach {
			applicationEventPublisher.publishEvent(TransactionSuccessfulEvent(walletId = it))
		}

		applicationEventPublisher.publishEvent(
			TransactionsStatusesResolvedEvent(
				transactionStatusModels = transactionsStatusModels,
			),
		)

		log.info(
			"Transactions status processing completed." +
				" (${transactionsStatusModels.size} / ${pendingTransactions.size}) processed",
		)
	}

	private fun getTransactionResults(
		chain: Chain,
		transactions: List<Pair<TxHash, Transaction>>,
	): Map<TxHash, TransactionResult> {
		return when (chain.type) {
			ChainType.EVM -> getTransactionResultsEVM(
				txHashes = transactions.mapToSet { (txHash) -> txHash },
				chainId = chain.evmId,
			)
			ChainType.SOLANA -> getTransactionResultsSVM(
				txHashes = transactions.mapToSet { (txHash) -> txHash },
			)
		}
	}

	private fun determineTransactionResult(
		resultMap: Map<TxHash, TransactionResult>,
		txHash: TxHash,
		transaction: Transaction,
	) = when (transaction.chain.type) {
		ChainType.EVM -> determineEvmTransactionResult(resultMap, txHash, transaction)
		ChainType.SOLANA -> determineSvmTransactionResult(resultMap, txHash)
	}

	private fun determineSvmTransactionResult(
		resultMap: Map<TxHash, TransactionResult>,
		txHash: TxHash,
	): TransactionResult {
		// TODO: Handle TRANSFER_ETH and TRANSFER_TOKEN transaction types
		return resultMap[txHash] ?: error("Error getting transactionResult for txHash $txHash")
	}

	private fun determineEvmTransactionResult(
		resultMap: Map<TxHash, TransactionResult>,
		txHash: TxHash,
		transaction: Transaction,
	): TransactionResult {
		return when (transaction.type) {
			TransactionType.TRANSFER_CURRENCY -> {
				val signedRawTransaction = TransactionDecoder.decode(transaction.signedTx) as SignedRawTransaction
				val value = signedRawTransaction.value

				// TODO: Fee is not actually zero, but it's not used only for Solana trading currently
				TransferCurrencyTransactionSuccess(value = value, fee = BigInteger.ZERO)
			}

			TransactionType.TRANSFER_TOKEN -> {
				val signedRawTransaction = TransactionDecoder.decode(transaction.signedTx) as SignedRawTransaction
				val tokenAmount = BigInteger(signedRawTransaction.data.takeLast(64), 16)

				val tokenPrice = getTokenPrices.getSingle(transaction.tokenAddress!!.toChainAddress(transaction.chain))
				val tokenEthAmount = (tokenAmount * tokenPrice.amount) / 10.toBigInteger().pow(CryptoCurrency.ETH.decimals)

				TransferTokenTransactionSuccess(
					amountIn = tokenEthAmount,
					amountOut = tokenAmount,
				)
			}

			else -> resultMap[txHash] ?: error(
				"Failed to determine transaction result for transaction type=${transaction.type}.",
			)
		}
	}
}
