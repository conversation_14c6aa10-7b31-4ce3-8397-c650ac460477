package com.cleevio.fatbot.application.module.limitorder.service

import com.cleevio.fatbot.adapter.out.solana.pumpfun.GetAmountOutPumpfunSolana
import com.cleevio.fatbot.adapter.out.solana.pumpfun.GetPumpfunCurveState
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.div
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.limitorder.port.out.MatchLimitOrders
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutPumpswap
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutRaydium
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.port.out.GetTokenBalancesSolana
import com.cleevio.fatbot.application.module.token.finder.TokenPairInfoFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.service.WalletBalanceService
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.domain.token.TokenPairInfo
import com.cleevio.fatbot.domain.token.getTradeFeeBp
import com.cleevio.fatbot.infrastructure.config.FatbotDispatchers
import com.cleevio.fatbot.infrastructure.config.logger
import jakarta.annotation.PreDestroy
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.Channel.Factory.UNLIMITED
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger
import java.util.UUID
import kotlin.coroutines.coroutineContext

private const val LIMIT_ORDER_MATCHING_WORKERS = 8

@Service
class LimitOrderMatchingService(
	private val getAmountOutPumpswap: GetAmountOutPumpswap,
	private val getAmountOutRaydium: GetAmountOutRaydium,
	private val getAmountOutPumpfun: GetAmountOutPumpfunSolana,
	private val getPumpfunCurveState: GetPumpfunCurveState,
	private val tokenPairInfoFinderService: TokenPairInfoFinderService,
	private val matchLimitOrders: MatchLimitOrders,
	private val walletBalanceService: WalletBalanceService,
	private val getTokenBalancesSolana: GetTokenBalancesSolana,
	private val limitOrderFillService: LimitOrderFillService,
	fatbotDispatchers: FatbotDispatchers,
) {

	private val logger = logger()

	private val scope = CoroutineScope(
		fatbotDispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	/**
	 * Channel with updates on token prices. These updates are processed and matched with Limit orders.
	 * Matched orders are passed on to [matchedOrdersChannel]
	 */
	private val tokenPriceUpdateChannel: Channel<Map<AddressWrapper, BigDecimal>> = Channel(capacity = UNLIMITED)

	/**
	 * Channel with matched Limit orders that are *likely* to get filled.
	 * Processors of this channels should determine with certainty whether the order should be filled
	 * and initiate the fill process.
	 */
	private val matchedOrdersChannel: Channel<MatchLimitOrders.Result> =
		Channel(capacity = 100, onBufferOverflow = BufferOverflow.DROP_OLDEST)

	@EventListener(ApplicationReadyEvent::class)
	private fun onStartup() {
		scope.launch {
			processTokenPriceUpdates()
		}

		// Matched orders are currently processed individually, therefore we allow multiple workers to operate on them.
		repeat(LIMIT_ORDER_MATCHING_WORKERS) {
			scope.launch { processMatchedOrders() }
		}
	}

	@PreDestroy
	private fun onStop() {
		scope.cancel()
	}

	/**
	 * A single entrypoint into this service. Callers are able to submit [tokenToPrice] which will be evaluated by this service
	 */
	suspend fun submitTokenPriceUpdate(tokenToPrice: Map<AddressWrapper, BigDecimal>) {
		tokenPriceUpdateChannel.send(tokenToPrice)
	}

	private suspend fun processTokenPriceUpdates() {
		for (tokenPriceUpdate in tokenPriceUpdateChannel) {
			runCatching {
				matchLimitOrdersByTokenPrice(tokenToPrice = tokenPriceUpdate)
			}.onFailure { logger.error("Error in matchLimitOrdersByTokenPrice. \ntokenPriceUpdate: $tokenPriceUpdate", it) }
		}
	}

	private suspend fun processMatchedOrders() {
		for (order in matchedOrdersChannel) {
			runCatching {
				evaluateMatchedOrder(order = order)
			}.onFailure { logger.error("Error in evaluateMatchedOrder. \nOrder: $order", it) }
		}
	}

	private suspend fun matchLimitOrdersByTokenPrice(tokenToPrice: Map<AddressWrapper, BigDecimal>) {
		val matchedLimitOrders = matchLimitOrders(tokenToPrice)

		matchedLimitOrders.forEach { matchedOrdersChannel.send(it) }
	}

	data class Result(
		val orderId: UUID,
		val walletId: UUID,
		val chain: Chain,
		val type: LimitOrderType,
		val pairAddress: AddressWrapper,
		val tokenAddress: AddressWrapper,
		val amountIn: BaseAmount,
		val walletBalance: BaseAmount,
	)

	private suspend fun evaluateMatchedOrder(order: MatchLimitOrders.Result) {
		val walletBalance = scope.async {
			walletBalanceService.getBalance(order.walletAddress, Chain.SOLANA)
		}

		val amountInBalance = when (order.type) {
			LimitOrderType.BUY -> walletBalance
			LimitOrderType.SELL -> scope.async { getTokenBalancesSolana.single(order.walletAddress, order.tokenAddress) }
		}

		val amountOutResolvers = getTokenResolvers(order.tokenAddress, order.type)

		if (amountOutResolvers.isEmpty()) {
			logger.warn("No amountOut resolver for order ${order.id} found")

			return
		}

		if (walletBalance.await() == BaseAmount.ZERO) {
			logger.info("Wallet (${order.walletAddress}) balance of matched limit order ${order.id} was zero")

			return
		}

		val fillAmounts = getFillChunks(order.remainingAmount, order.filledAmount, partialFill = false)
			.sortedDescending()
			.map { it.asBaseAmount() }
			.filter { amountInBalance.await().amount >= it.amount }

		val result = fillAmounts.firstNotNullOfOrNull { amountIn ->
			val (resolver, amountOut) = amountOutResolvers
				.map { resolver -> resolver to resolver.getAmountOut(amountIn) }
				.maxBy { (_, amountOut) -> amountOut.amount }

			val selectedTokenPair = resolver.tokenPairInfo

			val tokenDecimals = selectedTokenPair.tokenDecimals.intValueExact()

			val averagePrice = calculateAveragePrice(
				amountIn = amountIn,
				amountOut = amountOut,
				tokenDecimals = tokenDecimals,
				chain = Chain.SOLANA,
				type = order.type,
			)

			val isValid = when (order.type) {
				LimitOrderType.BUY -> averagePrice.amount <= order.limitPrice
				LimitOrderType.SELL -> averagePrice.amount >= order.limitPrice
			}

			if (!isValid) return@firstNotNullOfOrNull null

			Result(
				orderId = order.id,
				walletId = order.walletId,
				chain = Chain.SOLANA,
				type = order.type,
				pairAddress = selectedTokenPair.pairAddress,
				tokenAddress = order.tokenAddress,
				amountIn = amountIn,
				walletBalance = walletBalance.await(),
			)
		}

		if (result != null) {
			withContext(coroutineContext) {
				limitOrderFillService.enqueue(result)
			}
		}
	}

	private fun calculateAveragePrice(
		amountIn: BaseAmount,
		amountOut: BaseAmount,
		tokenDecimals: Int,
		chain: Chain,
		type: LimitOrderType,
	): BaseAmount {
		val (inDecimals, outDecimals) = when (type) {
			LimitOrderType.BUY -> chain.currency.decimals to tokenDecimals
			LimitOrderType.SELL -> tokenDecimals to chain.currency.decimals
		}

		val amountOutNative = amountOut.toNative(outDecimals)
		val amountInNative = amountIn.toNative(inDecimals)

		val averagePrice = when (type) {
			LimitOrderType.BUY -> amountInNative / amountOutNative
			LimitOrderType.SELL -> amountOutNative / amountInNative
		}

		return averagePrice.toBase(chain)
	}

	private fun makeAmountOutResolver(tokenPairInfo: TokenPairInfo, isBuy: Boolean): (BaseAmount) -> BaseAmount {
		val makeAmountOutResolver: (TokenPairInfo) -> (amountIn: BaseAmount) -> BaseAmount = when (tokenPairInfo.dexType) {
			GetDex.Dex.RAYDIUM -> {
				when (tokenPairInfo.raydiumPoolType!!) {
					GetDex.PoolType.AMM -> { pair: TokenPairInfo ->
						val reserves = getAmountOutRaydium.getReservesAMM(pair.raydiumAmmMarketData!!);

						{ amount: BaseAmount -> getAmountOutRaydium.fromReservesAMM(amount, reserves, isBuy) }
					}
					GetDex.PoolType.CPMM -> { pair: TokenPairInfo ->
						val reserves = getAmountOutRaydium.getReservesCPMM(pair.tokenAddress, pair.pairAddress);

						{ amountIn ->
							val tradeFee = pair.raydiumCpmmMarketData!!.getTradeFeeBp()

							getAmountOutRaydium.fromReservesCPMM(amountIn, reserves, tradeFee, isBuy)
						}
					}
					// TODO: Will be implemented after CLMM amountOut is adapted
					GetDex.PoolType.CLMM -> error("CLMM Pool not supported in makeAmountOutResolver!")
				}
			}
			GetDex.Dex.PUMP_SWAP -> { pair ->
				val reserves = getAmountOutPumpswap.getReserves(pair.tokenAddress, pair.pairAddress, false);

				{ amount: BaseAmount -> getAmountOutPumpswap.fromReserves(amount, reserves, isBuy) }
			}
			GetDex.Dex.PUMP_FUN -> { pair ->
				val curveState = getPumpfunCurveState(pair.pairAddress);

				{ amount: BaseAmount -> getAmountOutPumpfun.fromCurveState(amount, curveState, isBuy) }
			}
			else -> error("${tokenPairInfo.dexType} not supported in makeAmountOutResolver!")
		}

		val amountOutResolver = makeAmountOutResolver(tokenPairInfo)

		return amountOutResolver
	}

	private data class AmountOutResolver(
		val getAmountOut: (BaseAmount) -> BaseAmount,
		val tokenPairInfo: TokenPairInfo,
	)

	private suspend fun getTokenResolvers(tokenAddress: AddressWrapper, type: LimitOrderType): List<AmountOutResolver> {
		val allTokenPairs = withContext(coroutineContext) {
			tokenPairInfoFinderService.findAllByTokenAddressAndChain(tokenAddress, Chain.SOLANA)
		}

		// Note: For now we only support constant product pools, i.e. pools that support calculating amountOut only from reserves
		// TODO: This means that Raydium CLMM is not currently supported on Solana
		val resolvers = allTokenPairs
			.filter { it.supportsReserves() }
			.map { scope.async { makeAmountOutResolver(it, isBuy = type == LimitOrderType.BUY) } }
			.awaitAll()
			.zip(allTokenPairs) { resolver, pair -> AmountOutResolver(resolver, pair) }

		return resolvers
	}

	private fun TokenPairInfo.supportsReserves() =
		dexType in listOf(GetDex.Dex.PUMP_SWAP, GetDex.Dex.PUMP_FUN, GetDex.Dex.RAYDIUM) &&
			raydiumPoolType in listOf(null, GetDex.PoolType.AMM, GetDex.PoolType.CPMM)

	private fun getFillChunks(
		remainingAmount: BigInteger,
		filledAmount: BigInteger,
		partialFill: Boolean = false,
	): List<BigInteger> {
		require(remainingAmount > BigInteger.ZERO) {
			"Cannot calculate fill chunks when remainingAmount < 0. Was: $remainingAmount"
		}
		if (!partialFill) return listOf(remainingAmount)

		// The number of chunks the limit order can be filled with
		// Will be extracted / highlighted after partial filling is enabled
		val totalFillChunkCount = BigInteger.TEN
		val fullAmount = remainingAmount + filledAmount

		val fillChunk = fullAmount / totalFillChunkCount

		val chunks = List(totalFillChunkCount.toInt()) { fillChunk }
			.runningReduce(BigInteger::plus)
			.filter { it <= remainingAmount }
			.dropLast(1)
			.plus(remainingAmount)
			.sortedDescending()

		return chunks
	}
}
