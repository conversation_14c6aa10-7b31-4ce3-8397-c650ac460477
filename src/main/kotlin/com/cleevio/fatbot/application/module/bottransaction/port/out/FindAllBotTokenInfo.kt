package com.cleevio.fatbot.application.module.bottransaction.port.out

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import java.net.URI

interface FindAllBotTokenInfo {
	operator fun invoke(tokenAddresses: Set<AddressWrapper>, fileToUrlMapper: FileToUrlMapper): Set<Result>

	data class Result(
		val tokenAddress: AddressWrapper,
		val tokenName: String,
		val tokenSymbol: String,
		val tokenImageUrl: URI?,
		val tokenDecimals: Int,
	)
}
