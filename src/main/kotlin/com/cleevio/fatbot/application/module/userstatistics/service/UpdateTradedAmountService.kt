package com.cleevio.fatbot.application.module.userstatistics.service

import com.cleevio.fatbot.application.module.userfattycard.service.CreateUserFattyCardsService
import com.cleevio.fatbot.application.module.userleaderbord.finder.UserLeaderboardFinderService
import com.cleevio.fatbot.application.module.userstatistics.finder.UserStatisticsFinderService
import com.cleevio.fatbot.application.module.userstatistics.locks.UPDATE_USER_STATISTICS
import com.cleevio.fatbot.application.module.userstatistics.locks.USER_STATISTICS_MODULE
import com.cleevio.fatbot.application.module.userstatistics.properties.UserStreakMultiplierProperties
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.util.UUID

@Service
class UpdateTradedAmountService(
	private val userStatisticsFinderService: UserStatisticsFinderService,
	private val userLeaderboardFinderService: UserLeaderboardFinderService,
	private val createUserFattyCardsService: CreateUserFattyCardsService,
	private val streakMultiplierProperties: UserStreakMultiplierProperties,
) {

	@Transactional
	@Lock(module = USER_STATISTICS_MODULE, lockName = UPDATE_USER_STATISTICS)
	fun update(@LockArgumentParameter userId: UUID, tradeUsdAmount: BigDecimal) {
		val userStatistics = userStatisticsFinderService.getByUserId(userId = userId)
		val donutMultiplier = userLeaderboardFinderService.findByUserId(userId)?.donutMultiplier ?: BigDecimal.ONE
		val streakMultiplier = streakMultiplierProperties.getCurrentMultiplier(userStatistics.daysInStreak)

		val originalAmount = userStatistics.tradedAmountInDay
		userStatistics.apply {
			if (daysInStreak == 0) {
				startStreak()
			}

			updateTradedAmount(tradeUsdAmount = tradeUsdAmount)
			updateDonuts(donuts = tradeUsdAmount * donutMultiplier * streakMultiplier)
		}

		createUserFattyCardsService.create(
			newAmount = tradeUsdAmount,
			originalAmount = originalAmount,
			userId = userId,
		)
	}
}
