package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.adapter.out.solana.model.PoolReserves
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper

interface GetAmountOutPumpswap {
	operator fun invoke(
		amount: BaseAmount,
		tokenAddress: AddressWrapper,
		pairAddress: AddressWrapper,
		isToken2022: Boolean,
		isBuy: Boolean,
	): BaseAmount

	fun fromReserves(amount: BaseAmount, reserves: PoolReserves, isBuy: Boolean): BaseAmount

	fun getReserves(tokenAddress: AddressWrapper, pairAddress: AddressWrapper, isToken2022: Boolean): PoolReserves
}
