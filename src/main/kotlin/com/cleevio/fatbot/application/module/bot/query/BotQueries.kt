package com.cleevio.fatbot.application.module.bot.query

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bot.constant.BotStatus
import com.cleevio.fatbot.application.module.bot.query.CompareBotsQuery.BotPortfolioPastValue
import com.cleevio.fatbot.application.module.botmarket.query.SearchBotMarketPositionQuery
import com.cleevio.fatbot.application.module.bottransaction.query.SearchUserBotsTransactionsQuery
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.Size
import java.math.BigDecimal
import java.math.BigInteger
import java.net.URI
import java.time.Instant
import java.util.UUID

data class GetAllBotsQuery(val userId: UUID) : Query<List<GetAllBotsQuery.Result>> {

	@Schema(name = "GetAllBotsResult")
	data class Result(
		val id: UUID,
		val createdAt: Instant,
		val userReadableId: String,
		val name: String,
		val avatarFileId: UUID,
		val tradeAmount: BigDecimal,
		val buyFrequency: BigInteger,
		val remainingBuyFrequency: BigInteger,
		val buyFrequencyLastResetAt: Instant,
		val profitTargetFraction: BigDecimal,
		val stopLossFraction: BigDecimal,
		val marketCapFromUsd: BigDecimal?,
		val marketCapToUsd: BigDecimal?,
		val liquidityFromUsd: BigDecimal?,
		val liquidityToUsd: BigDecimal?,
		val dailyVolumeFromUsd: BigDecimal?,
		val dailyVolumeToUsd: BigDecimal?,
		val numberOfHoldersFrom: BigInteger?,
		val numberOfHoldersTo: BigInteger?,
		val buyVolume: BigDecimal?,
		val sellVolume: BigDecimal?,
		val sellTransactionFraction: BigDecimal?,
		val buyTransactionFraction: BigDecimal?,
		val tokenTickerCopyIsChecked: Boolean,
		val creatorHighBuyIsChecked: Boolean,
		val bundledBuysDetectedIsChecked: Boolean,
		val suspiciousWalletsDetectedIsChecked: Boolean,
		val singleHighBuyIsChecked: Boolean,
		val shouldWaitBeforeBuying: Boolean,
		val shouldAutoSellAfterHoldTime: Boolean,
	)
}

class GetBotSettingsStatisticsQuery : Query<GetBotSettingsStatisticsQuery.Result> {

	@Schema(name = "GetBotSettingsStatisticsResult")
	data class Result(
		val tradeAmount: List<ValueBucket<BigDecimal>>,
		val buyFrequency: List<ValueBucket<BigInteger>>,
		val profitTarget: List<ValueBucket<BigDecimal>>,
		val stopLoss: List<ValueBucket<BigDecimal>>,
		val buyVolume: List<ValueBucket<BigDecimal>>,
		val sellVolume: List<ValueBucket<BigDecimal>>,
		val numberOfHolders: List<ValueBucket<BigInteger?>>,
		val marketCapUsd: List<ValueBucket<out BigDecimal?>>,
		val liquidityUsd: List<ValueBucket<out BigDecimal?>>,
		val dailyVolumeUsd: List<ValueBucket<out BigDecimal?>>,
		val sellTransactionFractionThreshold: List<ValueBucket<out BigDecimal?>>,
	)

	data class ValueBucket<T>(val from: T, val to: T, val count: Int)
}

data class GetMyBotsQuery(val userId: UUID, val timeRange: TimeRange) : Query<List<GetMyBotsQuery.Result>> {

	@Schema(name = "GetMyBotsResult")
	data class Result(
		val id: UUID,
		val isActive: Boolean,
		val numberOfActiveDays: Long,
		val botStatus: BotStatus,
		val name: String?,
		val avatarFileUrl: URI?,
		val avatarFileId: UUID?,
		val botWalletAddress: AddressWrapper?,
		val balanceUsd: BigDecimal?,
		val timeRangeChangeUsd: BigDecimal?,
		val timeRangeChangeFraction: BigDecimal?,
		val buyCount: BigInteger?,
		val sellCount: BigInteger?,
		val draftCompleteness: BigDecimal?,
	)
}

data class CompareBotsQuery(
	val userId: UUID,
	@field:Valid val filter: Filter,
) : Query<List<CompareBotsQuery.Result>> {

	data class Filter(
		@field:Size(min = 1, max = 3) val botIds: Set<UUID>,
		val timeRange: TimeRange,
	)

	@Schema(name = "CompareBotsResult")
	data class Result(
		val botId: UUID,
		val botName: String,
		val isActive: Boolean,
		val numberOfActiveDays: Long,
		val botAvatarFileId: UUID,
		val buyFrequency: BigInteger,
		val remainingBuyFrequency: BigInteger,
		val buyFrequencyLastResetAt: Instant,
		val botTotalValueAmountUsd: BigDecimal,
		val botTotalPnlAmountUsd: BigDecimal,
		val botTotalPnlAmountFraction: BigDecimal,
		val botPortfolioLastValues: List<BotPortfolioPastValue>,
		val transactionsCount: Int,
		val buyTransactionsCount: Int,
		val profitableTransactionsCount: Int,
		val lossTransactionsCount: Int,
		val transactionsVolumeSum: BigDecimal,
		val botStatus: BotStatus,
		val activeBotMarketPositions: List<SearchBotMarketPositionQuery.Result>,
		val closedBotMarketPositions: List<SearchBotMarketPositionQuery.Result>,
		val botTransactions: List<SearchUserBotsTransactionsQuery.Result>,
	)

	@Schema(name = "BotPortfolioPastValue")
	data class BotPortfolioPastValue(
		val portfolioValueUsd: BigDecimal,
		val createdAt: Instant,
	)
}

data class SearchUserBotDetailQuery(
	val userId: UUID,
	@field:Valid val filter: Filter,
) : Query<SearchUserBotDetailQuery.Result> {

	data class Filter(
		val botId: UUID,
		val timeRange: TimeRange,
	)

	@Schema(name = "SearchUserBotDetailResult")
	data class Result(
		val botId: UUID,
		val botWalletAddress: AddressWrapper,
		val botWalletChain: Chain,
		val botWalletBalanceUsd: BigDecimal,
		val botName: String,
		val isActive: Boolean,
		val numberOfActiveDays: Long,
		val botAvatarFileId: UUID,
		val buyFrequency: BigInteger,
		val remainingBuyFrequency: BigInteger,
		val buyFrequencyLastResetAt: Instant,
		val botTotalValueAmountUsd: BigDecimal,
		val botTotalPnlAmountUsd: BigDecimal,
		val botTotalPnlAmountFraction: BigDecimal,
		val botPortfolioLastValues: List<BotPortfolioPastValue>,
		val transactionsCount: Int,
		val buyTransactionsCount: Int,
		val profitableTransactionsCount: Int,
		val lossTransactionsCount: Int,
		val transactionsVolumeSum: BigDecimal,
		val activeBotMarketPositions: List<SearchBotMarketPositionQuery.Result>,
		val closedBotMarketPositions: List<SearchBotMarketPositionQuery.Result>,
		val botTransactions: List<SearchUserBotsTransactionsQuery.Result>,
		val botStatus: BotStatus,
	)
}

data class BotsOverviewQuery(
	val userId: UUID,
	@field:Valid val filter: Filter,
) : Query<BotsOverviewQuery.Result> {

	data class Filter(
		val timeRange: TimeRange,
	)

	@Schema(name = "BotsOverviewResult")
	data class Result(
		val userId: UUID,
		val botsTotalValueAmountUsd: BigDecimal,
		val botsTotalPnlAmountUsd: BigDecimal,
		val botsTotalPnlAmountFraction: BigDecimal,
		val botsOneDayChangeAmountUsd: BigDecimal,
		val botsOneDayChangeFraction: BigDecimal,
		val botsPortfolioValueSumPastValues: List<BotPortfolioPastValue>,
		val botsTransactionsCount: Int,
		val botsBuyTransactionsCount: Int,
		val botsProfitableTransactionsCount: Int,
		val botsLossTransactionsCount: Int,
		val botsTransactionsVolumeSum: BigDecimal,
	)
}

data class GetBotQuery(val userId: UUID, val botId: UUID) : Query<GetBotQuery.Result> {

	@Schema(name = "GetBotResult")
	data class Result(
		val id: UUID,
		val botWalletAddress: AddressWrapper,
		val botWalletChain: Chain,
		val botWalletBalanceNativeAmount: NativeAmount,
		val botWalletBalanceUsd: BigDecimal,
		val createdAt: Instant,
		val userReadableId: String,
		val name: String,
		val avatarFileId: UUID,
		val tradeAmount: BigDecimal,
		val buyFrequency: BigInteger,
		val remainingBuyFrequency: BigInteger,
		val buyFrequencyLastResetAt: Instant,
		val profitTargetFraction: BigDecimal,
		val stopLossFraction: BigDecimal,
		val hasSufficientBalanceForTrade: Boolean,
		val marketCapFromUsd: BigDecimal?,
		val marketCapToUsd: BigDecimal?,
		val liquidityFromUsd: BigDecimal?,
		val liquidityToUsd: BigDecimal?,
		val dailyVolumeFromUsd: BigDecimal?,
		val dailyVolumeToUsd: BigDecimal?,
		val numberOfHoldersFrom: BigInteger?,
		val numberOfHoldersTo: BigInteger?,
		val buyVolume: BigDecimal?,
		val sellVolume: BigDecimal?,
		val sellTransactionFraction: BigDecimal?,
		val buyTransactionFraction: BigDecimal?,
		val tokenTickerCopyIsChecked: Boolean,
		val creatorHighBuyIsChecked: Boolean,
		val bundledBuysDetectedIsChecked: Boolean,
		val suspiciousWalletsDetectedIsChecked: Boolean,
		val singleHighBuyIsChecked: Boolean,
		val shouldWaitBeforeBuying: Boolean,
		val shouldAutoSellAfterHoldTime: Boolean,
	)
}

data class GetBotWithdrawGasEstimationQuery(
	val userId: UUID,
	val botId: UUID,
	@field:Positive val nativeAmount: BigDecimal,
	@field:Valid val recipientAddress: AddressWrapper,
) : Query<GetBotWithdrawGasEstimationQuery.Result> {

	@Schema(name = "GetBotWithdrawGasEstimationResult")
	data class Result(
		val gasFeeUsd: BigDecimal?,
	)
}
