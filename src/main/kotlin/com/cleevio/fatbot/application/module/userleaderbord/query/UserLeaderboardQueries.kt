package com.cleevio.fatbot.application.module.userleaderbord.query

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollAsc
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.query.Query
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.util.UUID

data class GetUserLeaderboardInfoQuery(val userId: UUID) : Query<GetUserLeaderboardInfoQuery.Result> {

	@Schema(name = "GetUserLeaderboardInfoResult")
	open class Result(
		val userId: UUID,
		val donutsGained: BigDecimal,
		val donutsNeededForNextThreshold: BigDecimal,
		val volumeNeededForNextThreshold: BigDecimal,
		val volume: BigDecimal,
		val rank: Int?,
		val multiplier: BigDecimal,
	)
}

data class SearchUserLeaderboardQuery(
	val infiniteScroll: InfiniteScrollAsc.Integer,
) : Query<InfiniteScrollSlice<SearchUserLeaderboardQuery.Result, Int>> {

	@Schema(name = "SearchUserLeaderboardResult")
	data class Result(
		val userId: UUID,
		val rank: Int,
		val multiplier: BigDecimal,
		val donutsGained: BigDecimal,
		val email: String,
		val volumeNeededForNextThreshold: BigDecimal,
		val volume: BigDecimal,
	)
}

data class GetUserLeaderboardGeneralInfoQuery(
	val userId: UUID,
) : Query<List<GetUserLeaderboardGeneralInfoQuery.Result>> {

	@Schema(name = "GetUserLeaderboardGeneralInfoResult")
	data class Result(
		val rank: Int,
		val multiplier: BigDecimal,
	)
}
