package com.cleevio.fatbot.application.module.userstatistics.scheduled

import com.cleevio.fatbot.application.module.userstatistics.locks.PROCESS_USER_STATISTICS
import com.cleevio.fatbot.domain.userstatistics.UserStatisticUpdateService
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class UserFattyCardsStatisticsResetTrigger(
	private val userStatisticUpdateService: UserStatisticUpdateService,
) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.user-fatty-cards-statistics-reset")
	@Scheduled(cron = "\${fatbot.fatty-cards.statistics-reset.cron}")
	@SchedulerLock(
		name = PROCESS_USER_STATISTICS,
		lockAtLeastFor = "\${fatbot.fatty-cards.statistics-reset.lock-for}",
		lockAtMostFor = "\${fatbot.fatty-cards.statistics-reset.lock-for}",
	)
	fun trigger() {
		logger.info("$PROCESS_USER_STATISTICS cron started")
		userStatisticUpdateService.resetTradedAmountInDayForAll()
		logger.info("$PROCESS_USER_STATISTICS cron ended")
	}
}
