package com.cleevio.fatbot.application.module.wallet.command

import com.cleevio.fatbot.application.common.command.Command
import com.cleevio.fatbot.application.common.validation.NullOrNotBlank
import com.cleevio.fatbot.application.common.validation.ValidPrivateKey
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.util.UUID

data class CreateNewUserWalletCommand(
	val userId: UUID,
	@field:Valid val chain: Chain,
) : Command<CreateNewUserWalletCommand.Result> {

	@Schema(name = "CreateNewUserWalletResult")
	data class Result(
		val walletId: UUID,
		val walletAddress: AddressWrapper,
		val privateKey: String,
	)
}

data class ImportUserWalletCommand(
	val userId: UUID,
	@field:ValidPrivateKey val privateKey: String,
	@field:Valid val chain: Chain,
) : Command<ImportUserWalletCommand.Result> {

	@Schema(name = "ImportUserWalletResult")
	data class Result(
		val walletId: UUID,
		val walletAddress: AddressWrapper,
		val privateKey: String,
	)
}

data class PatchUserWalletCommand(
	val userId: UUID,
	val walletId: UUID,

	@field:NullOrNotBlank val customName: String?,
	val buyAntiMevProtection: Boolean?,
	val sellAntiMevProtection: Boolean?,
) : Command<Unit>

data class MarkWalletAsDefaultCommand(
	val userId: UUID,
	val walletId: UUID,
) : Command<Unit>

data class DeleteUserWalletCommand(
	val userId: UUID,
	val walletId: UUID,
) : Command<Unit>
