package com.cleevio.fatbot.application.module.tokenpricesnapshot

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.tokenprice.TokenPriceRepository
import com.cleevio.fatbot.domain.tokenprice.TokenPriceSnapshotCreateService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.Instant

@Service
class TokenPriceSnapshotService(
	private val tokenPriceSnapshotCreateService: TokenPriceSnapshotCreateService,
	private val tokenPriceSnapshotRepository: TokenPriceRepository,
) {

	data class TokenPriceSnapshot(
		val tokenAddress: AddressWrapper,
		val chain: Chain,
		val timestamp: Instant,
		val price: BaseAmount,
		val exchangeRate: BigDecimal,
	)

	@Transactional
	fun saveTokenPriceSnapshots(tokenPrices: List<TokenPriceSnapshot>) {
		tokenPrices.forEach {
			tokenPriceSnapshotCreateService.create(
				tokenAddress = it.tokenAddress,
				chain = it.chain,
				price = it.price,
				exchangeRate = it.exchangeRate,
				validAt = it.timestamp,
			)
		}
	}

	@Transactional
	fun deleteSnapshotsBefore(before: Instant) = tokenPriceSnapshotRepository.deleteSnapshotsBefore(before = before)
}
