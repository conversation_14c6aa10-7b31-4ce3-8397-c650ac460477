package com.cleevio.fatbot.application.module.market.service

import com.cleevio.fatbot.application.common.crypto.minus
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator
import com.cleevio.fatbot.application.module.market.port.out.FindAllUserMarketPosition
import com.cleevio.fatbot.application.module.market.port.out.FindAllUserMarketPositionFromSnapshot
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.tokenpricesnapshot.port.out.GetTokenPriceSnapshots
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

@Service
class AggregateMarketPositionsService(
	private val findAllUserMarketPosition: FindAllUserMarketPosition,
	private val findAllUserMarketPositionFromSnapshot: FindAllUserMarketPositionFromSnapshot,
	private val marketPositionSnapshotService: MarketPositionSnapshotService,
	private val fileUrlMapper: FileUrlMapper,
	private val getTokenPrices: GetTokenPrices,
	private val getTokenPriceSnapshots: GetTokenPriceSnapshots,
	private val usdConverter: UsdConverter,
	private val getUsdExchangeRate: GetUsdExchangeRate,
) {

	private val logger = logger()

	/**
	 * [walletId] - when null, aggregate market positions of all user wallets
	 */
	fun byChainNow(userId: UUID, walletId: UUID?, chains: Set<Chain>): Map<Chain, MarketPositionCalculator.Result> {
		val userMarketPositionsData = findAllUserMarketPosition(
			userId = userId,
			walletId = walletId,
			chains = chains,
			fileToUrlMapper = fileUrlMapper::map,
		)

		val tokens = userMarketPositionsData.mapToSet { it.tokenAddress.toChainAddress(it.chain) }
		val tokenAddressToCurrentTokenPrice = getTokenPrices(tokens = tokens)
		val chainToExchangeRate = Chain.entries.associateWith { getUsdExchangeRate(it.currency) }

		return MarketPositionCalculator.sumByChain(
			marketPositions = userMarketPositionsData,
			tokenAddressToPriceData = tokenAddressToCurrentTokenPrice,
			chainToExchangeRate = chainToExchangeRate,
		)
	}

	fun byChainTotalValueUsd24hAgo(
		userId: UUID,
		walletId: UUID?,
		chains: Set<Chain>,
		now: Instant,
	): Map<Chain, BigDecimal> {
		val snapshotMadeAt = marketPositionSnapshotService.getSnapshotTimeCreated24hoursAgo(now = now)
		val userMarketPositionsData = findAllUserMarketPositionFromSnapshot(
			userId = userId,
			chains = chains,
			walletId = walletId,
			snapshotMadeAt = snapshotMadeAt,
		)

		val tokens = userMarketPositionsData.mapToSet { it.tokenAddress.toChainAddress(it.chain) }

		val tokenToPriceUsd = getTokenPriceSnapshots(
			tokens = tokens,
			before = snapshotMadeAt,
		).mapValues { (token, snapshot) ->
			usdConverter.baseToUsd(
				snapshot.price,
				token.chain,
				snapshot.exchangeRateUsd,
			)
		}

		val missingPriceTokens = tokens.filter { it !in tokenToPriceUsd }

		if (missingPriceTokens.isNotEmpty()) {
			logger.warn(
				""""
				|Failed to fetch token prices for ${missingPriceTokens.size} tokens!
				|Token addresses: $missingPriceTokens
				|Snapshot time: $snapshotMadeAt
				""".trimMargin(),
			)
		}

		return userMarketPositionsData
			.groupBy { it.chain }
			.mapValues { (_, positions) ->
				positions.sumOf {
					val balance = it.tokenAmountBought - it.tokenAmountSold
					val balanceNative = balance.toNative(it.tokenDecimals)

					val priceUsd = tokenToPriceUsd[it.tokenAddress.toChainAddress(it.chain)] ?: BigDecimal.ZERO

					balanceNative.amount * priceUsd
				}
			}
	}

	fun totalValueUsd24hAgo(userId: UUID, walletId: UUID?, chains: Set<Chain>, now: Instant): BigDecimal {
		return byChainTotalValueUsd24hAgo(
			userId = userId,
			walletId = walletId,
			chains = chains,
			now = now,
		)
			.values
			.sumOf { it }
	}
}
