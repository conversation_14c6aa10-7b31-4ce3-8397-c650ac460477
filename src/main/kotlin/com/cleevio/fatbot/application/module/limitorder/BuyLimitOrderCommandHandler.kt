package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.adapter.out.solana.BuyTokenSolana
import com.cleevio.fatbot.adapter.out.solana.pumpfun.GetPumpfunCurveState
import com.cleevio.fatbot.adapter.out.solana.pumpswap.GetPumpswapPool
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.limitorder.command.BuyLimitOrderCommand
import com.cleevio.fatbot.application.module.limitorder.event.LimitOrderFillFailureEvent
import com.cleevio.fatbot.application.module.limitorder.finder.LimitOrderFinderService
import com.cleevio.fatbot.application.module.market.port.out.EstimateGas
import com.cleevio.fatbot.application.module.market.port.out.ValidateFundsForSvmTransaction
import com.cleevio.fatbot.application.module.market.service.GasLimitEstimationService
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.token.finder.TokenPairInfoFinderService
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.event.LimitOrderTokenBoughtEvent
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class BuyLimitOrderCommandHandler(
	private val tokenPairInfoFinderService: TokenPairInfoFinderService,
	private val tokenInfoFinderService: EvmTokenInfoFinderService,
	private val walletFinderService: WalletFinderService,
	private val limitOrderFinderService: LimitOrderFinderService,
	private val gasLimitEstimationService: GasLimitEstimationService,
	private val validateFundsForSvmTransaction: ValidateFundsForSvmTransaction,
	private val buyTokenSolana: BuyTokenSolana,
	private val getPumpswapPool: GetPumpswapPool,
	private val getPumpfunCurveState: GetPumpfunCurveState,
	private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, BuyLimitOrderCommand> {

	private val logger = logger()

	override val command = BuyLimitOrderCommand::class

	override fun handle(command: BuyLimitOrderCommand) {
		val result = runCatching {
			handleBuy(
				orderId = command.orderId,
				amount = command.amount,
				pairAddress = command.pairAddress,
				walletBalance = command.walletBalance,
			)
		}

		result.onFailure {
			logger.warn("Failed to perform Buy limit order command for $command", it)

			applicationEventPublisher.publishEvent(LimitOrderFillFailureEvent(command.orderId))
		}
	}

	private fun handleBuy(orderId: UUID, amount: BaseAmount, pairAddress: AddressWrapper, walletBalance: BaseAmount) {
		val order = limitOrderFinderService.getById(orderId)
		val tokenInfo = tokenInfoFinderService.getByTokenAddressAndChain(order.tokenAddress, order.chain)
		val tokenPair = tokenPairInfoFinderService.getByPairAddressAndChain(pairAddress, order.chain)
		val wallet = walletFinderService.getByIdAndUserId(order.walletId, order.userId)

		val dexPairInfo = tokenPair.getDexPairInfo()

		val estimateGasResult = gasLimitEstimationService.estimateGas(
			transactionType = TransactionType.BUY,
			chain = order.chain,
			userId = order.userId,
			walletId = order.walletId,
			tokenAddress = order.tokenAddress,
			isToken2022 = tokenInfo.isToken2022,
			baseAmount = amount,
			dexPairInfo = dexPairInfo,
			recipientAddress = null,
		)

		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultSolana) error("Estimate gas must be of Solana type")

		validateFundsForSvmTransaction.withBalance(
			balance = walletBalance,
			walletAddress = wallet.address,
			transactionAmount = amount,
			estimatedFees = estimateGasResult.feeBaseAmount,
		)

		val signedTx = buyTokenSolana(
			BuyTokenSolana.SolanaInput(
				userId = wallet.userId,
				tokenAddress = order.tokenAddress,
				isToken2022 = tokenInfo.isToken2022,
				privateKey = wallet.privateKey,
				buyForLamportAmount = amount,
				dexPairInfo = dexPairInfo,
				getRaydiumAMMMarketData = { tokenPair.raydiumAmmMarketData!! },
				getRaydiumCPMMMarketData = { tokenPair.raydiumCpmmMarketData!! },
				getPumpfunCreator = {
					tokenPair.creatorAddress ?: getPumpfunCurveState(dexPairInfo.pairAddress).creator
						?: error("Bonding curve ${dexPairInfo.pairAddress} does not provide creator address")
				},
				getPumpswapCoinCreator = { tokenPair.creatorAddress ?: getPumpswapPool(dexPairInfo.pairAddress).coinCreator },
				useStakedEndpoint = false,
				useMevProtection = wallet.buyAntiMevProtection,
			),
		)

		applicationEventPublisher.publishEvent(
			LimitOrderTokenBoughtEvent(
				walletId = order.walletId,
				signedTxList = listOf(signedTx),
				tokenAddress = order.tokenAddress,
				limitOrderId = order.id,
			),
		)
	}
}
