package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.adapter.`in`.rest.dto.DexPairInfoInputV2
import com.cleevio.fatbot.adapter.out.evm.context.UniswapV3Fee
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.exception.InvalidDexPairProvidedException
import com.cleevio.fatbot.application.module.market.port.out.GetDex.Version
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.fasterxml.jackson.annotation.JsonTypeInfo
import io.swagger.v3.oas.annotations.media.Schema

interface GetDex {
	operator fun invoke(
		dexId: String,
		pairAddress: AddressWrapper,
		version: Version?,
		poolType: PoolType?,
		chain: Chain,
	): Result
	fun supports(): ChainType

	enum class Version {
		V2,
		V3,
	}

	enum class PoolType {
		AMM,
		CLMM,
		CPMM,
	}

	enum class Dex {
		UNISWAP_V2,
		PANCAKESWAP_V2,
		UNISWAP_V3,
		PANCAKESWAP_V3,
		PUMP_FUN,
		PUMP_SWAP,
		RAYDIUM,
		METEORA,
	}

	@Schema(name = "GetDexResult")
	@JsonTypeInfo(
		use = JsonTypeInfo.Id.NAME,
		include = JsonTypeInfo.As.EXISTING_PROPERTY,
		property = "dex",
		visible = true,
	)
	sealed interface Result {
		val dex: Dex
		val pairAddress: AddressWrapper
	}

	sealed interface Evm : Result
	sealed interface Solana : Result

	@Schema(name = "GetDexUniswapV2")
	class UniswapV2(override val pairAddress: AddressWrapper) : Evm {
		override val dex: Dex = Dex.UNISWAP_V2
	}

	@Schema(name = "GetDexPancakeswapV2")
	class PancakeswapV2(override val pairAddress: AddressWrapper) : Evm {
		override val dex: Dex = Dex.PANCAKESWAP_V2
	}

	@Schema(name = "GetDexUniswapV3")
	class UniswapV3(override val pairAddress: AddressWrapper, val fee: UniswapV3Fee) : Evm {
		override val dex: Dex = Dex.UNISWAP_V3
	}

	@Schema(name = "GetDexPancakeswapV3")
	class PancakeswapV3(override val pairAddress: AddressWrapper, val fee: UniswapV3Fee) : Evm {
		override val dex: Dex = Dex.PANCAKESWAP_V3
	}

	@Schema(name = "GetDexPumpFun")
	class PumpFun(override val pairAddress: AddressWrapper) : Solana {
		override val dex: Dex = Dex.PUMP_FUN
	}

	@Schema(name = "GetDexPumpSwap")
	class PumpSwap(override val pairAddress: AddressWrapper) : Solana {
		override val dex: Dex = Dex.PUMP_SWAP
	}

	@Schema(name = "GetDexRaydium")
	class Raydium(override val pairAddress: AddressWrapper, val poolType: PoolType) : Solana {
		override val dex: Dex = Dex.RAYDIUM
	}

	@Schema(name = "GetDexMeteora")
	class Meteora(override val pairAddress: AddressWrapper) : Solana {
		override val dex: Dex = Dex.METEORA
	}
}

fun DexPairInfoInputV2.toDexPairInfo(): GetDex.Result = when (dex) {
	GetDex.Dex.UNISWAP_V2 -> GetDex.UniswapV2(pairAddress)
	GetDex.Dex.UNISWAP_V3 -> {
		if (fee == null) throw InvalidDexPairProvidedException("UNISWAP_V3 must contain fee!")
		GetDex.UniswapV3(pairAddress, fee)
	}
	GetDex.Dex.PANCAKESWAP_V2 -> GetDex.PancakeswapV2(pairAddress)
	GetDex.Dex.PANCAKESWAP_V3 -> {
		if (fee == null) throw InvalidDexPairProvidedException("PANCAKESWAP_V3 must contain fee!")
		GetDex.PancakeswapV3(pairAddress, fee)
	}

	GetDex.Dex.PUMP_FUN -> GetDex.PumpFun(pairAddress)
	GetDex.Dex.PUMP_SWAP -> GetDex.PumpSwap(pairAddress)
	GetDex.Dex.RAYDIUM -> {
		if (poolType == null) throw InvalidDexPairProvidedException("Raydium must contain poolType!")

		GetDex.Raydium(pairAddress, poolType)
	}
	GetDex.Dex.METEORA -> GetDex.Meteora(pairAddress)
}

fun GetDex.Result.toDexPairInfoInput(): DexPairInfoInputV2 {
	val fee = when (this) {
		is GetDex.PancakeswapV3 -> this.fee
		is GetDex.UniswapV3 -> this.fee
		is GetDex.PancakeswapV2, is GetDex.UniswapV2, is GetDex.Meteora -> null
		is GetDex.PumpFun, is GetDex.PumpSwap, is GetDex.Raydium -> null
	}

	val poolType = when (this) {
		is GetDex.Raydium -> poolType
		is GetDex.PancakeswapV2, is GetDex.PancakeswapV3, is GetDex.UniswapV2, is GetDex.UniswapV3 -> null
		is GetDex.PumpFun, is GetDex.PumpSwap, is GetDex.Meteora -> null
	}

	return DexPairInfoInputV2(
		pairAddress = pairAddress,
		dex = dex,
		fee = fee,
		poolType = poolType,
	)
}

// Helper function so we can call invoke on multiple GetDex instances, and find
// which one to use based on chain
operator fun List<GetDex>.invoke(
	dexId: String,
	pairAddress: AddressWrapper,
	version: Version?,
	poolType: GetDex.PoolType?,
	chain: Chain,
) = this
	.find { it.supports() == chain.type }
	?.invoke(dexId = dexId, pairAddress = pairAddress, version = version, poolType = poolType, chain = chain)
	?: error("Getting DEX is not supported for chainType ${chain.type}")
