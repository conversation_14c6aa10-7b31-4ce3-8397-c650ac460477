package com.cleevio.fatbot.application.module.userleaderbord.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.math.BigDecimal

@ConfigurationProperties(prefix = "leaderboard.multiplier.rank")
class LeaderboardMultiplierProperties(thresholds: Map<Int, BigDecimal>) {

	/**
	 * Sorted using natural order of keys, i.e. ascending rank thresholds
	 */
	val rankThresholdToMultiplier = thresholds.toSortedMap()

	fun getMultiplierByRank(rank: Int): BigDecimal {
		return rankThresholdToMultiplier.entries
			.firstOrNull { (threshold, _) -> threshold >= rank }
			?.value ?: BigDecimal.ONE
	}

	fun getNextRankThreshold(currentRank: Int): Int? {
		return rankThresholdToMultiplier.keys.filter { it < currentRank }.maxOrNull()
	}
}
