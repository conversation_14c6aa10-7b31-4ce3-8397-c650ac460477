package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.adapter.out.messagingproxy.FatbotMessagingProxyConnector
import com.cleevio.fatbot.adapter.out.websocket.dto.UserTransactionStatusChangedMessage
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.transaction.port.out.GetTransactionStatusChangedMessageAmounts
import com.cleevio.fatbot.application.module.transaction.port.out.model.TransactionStatusModel
import com.cleevio.fatbot.infrastructure.config.logger
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import org.springframework.stereotype.Service

@Service
class TransactionStatusPublishingService(
	private val messagingProxyConnector: FatbotMessagingProxyConnector,
	private val getTransactionStatusChangedMessageAmounts: GetTransactionStatusChangedMessageAmounts,
) {

	private val logger = logger()

	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	fun publishTransactionsToUsers(transactionStatusModels: List<TransactionStatusModel>) {
		val transactionAmounts =
			getTransactionStatusChangedMessageAmounts(transactionStatusModels.mapToSet { it.transactionId })

		transactionStatusModels.forEach {
			val txData = transactionAmounts[it.transactionId] ?: error(
				"TransactionStatusChanged message data for ${it.transactionId} not found",
			)

			scope.launch {
				runCatching {
					messagingProxyConnector.broadcastUserTransaction(
						UserTransactionStatusChangedMessage(
							transactionId = it.transactionId,
							transactionStatus = it.transactionStatus,
							txHash = it.txHash,
							userId = txData.userId,
							tokenNativeAmount = txData.tokenNativeAmount,
							currencyNativeAmount = txData.currencyNativeAmount,
							currencyAmountUsd = txData.currencyAmountUsd,
						),
					)
				}.onFailure {
					logger.error("Failed to broadcast user transaction status changed message", it)
				}
			}
		}
	}
}
