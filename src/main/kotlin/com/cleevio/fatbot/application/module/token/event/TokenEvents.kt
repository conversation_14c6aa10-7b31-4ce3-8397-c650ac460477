package com.cleevio.fatbot.application.module.token.event

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant

data class TokenOnDexScreenerFoundEvent(
	val chain: Chain,
	val tokenAddress: AddressWrapper,
	val pairAddress: AddressWrapper,
	val decimals: BigInteger,
	val name: String,
	val symbol: String,
	val imageUrl: String?,
)

data class TokenPairFoundEvent(
	val chain: Chain,
	val dex: GetDex.Result,
	val tokenAddress: AddressWrapper,
	val tokenDecimals: BigInteger,
)

data class NewEvmTokenInfoCreatedEvent(
	val chain: Chain,
	val tokenAddress: AddressWrapper,
	val pairAddress: AddressWrapper,
)

sealed interface TokenTradedEvent {
	val tokenAddress: AddressWrapper
	val price: BigDecimal
	val priceUsd: BigDecimal
	val amountUsd: BigDecimal
	val timestamp: Instant
	val blockHeight: Long
	val transactionIndex: Int
}

data class SolanaTokenTradedEvent(
	override val tokenAddress: AddressWrapper,
	override val price: BigDecimal,
	override val priceUsd: BigDecimal,
	override val amountUsd: BigDecimal,
	override val timestamp: Instant,
	override val blockHeight: Long,
	override val transactionIndex: Int,
) : TokenTradedEvent

data class EVMTokenTradedEvent(
	override val tokenAddress: AddressWrapper,
	override val price: BigDecimal,
	override val priceUsd: BigDecimal,
	override val amountUsd: BigDecimal,
	override val timestamp: Instant,
	override val blockHeight: Long,
	override val transactionIndex: Int,
	val chainId: Long,
) : TokenTradedEvent
