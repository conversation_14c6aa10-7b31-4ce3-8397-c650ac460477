package com.cleevio.fatbot.application.module.botdraft.query

import com.cleevio.fatbot.application.common.query.Query
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.util.UUID

data class GetAllBotDraftsQuery(val userId: UUID) : Query<List<GetAllBotDraftsQuery.Result>> {

	@Schema(name = "GetAllBotDraftsResult")
	data class Result(
		val id: UUID,
		val createdAt: Instant,
		val name: String?,
		val avatarFileId: UUID?,
		val tradeAmount: BigDecimal?,
		val buyFrequency: BigInteger?,
		val profitTargetFraction: BigDecimal?,
		val stopLossFraction: BigDecimal?,
		val marketCapFromUsd: BigDecimal?,
		val marketCapToUsd: BigDecimal?,
		val liquidityFromUsd: BigDecimal?,
		val liquidityToUsd: BigDecimal?,
		val dailyVolumeFromUsd: BigDecimal?,
		val dailyVolumeToUsd: BigDecimal?,
		val numberOfHoldersFrom: BigInteger?,
		val numberOfHoldersTo: BigInteger?,
		val buyVolume: BigDecimal?,
		val sellVolume: BigDecimal?,
		val sellTransactionFraction: BigDecimal?,
		val buyTransactionFraction: BigDecimal?,
		val tokenTickerCopyIsChecked: Boolean,
		val creatorHighBuyIsChecked: Boolean,
		val bundledBuysDetectedIsChecked: Boolean,
		val suspiciousWalletsDetectedIsChecked: Boolean,
		val singleHighBuyIsChecked: Boolean,
		val shouldWaitBeforeBuying: Boolean,
		val shouldAutoSellAfterHoldTime: Boolean,
	)
}
