package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper

interface ValidateFundsForSvmTransaction {
	/**
	 * Validates whether it's possible for given wallet to send amount of SOL.
	 */
	operator fun invoke(walletAddress: AddressWrapper, transactionAmount: BaseAmount, estimatedFees: BaseAmount)

	/**
	 * Validates whether it's possible for given wallet balance to send amount of SOL.
	 */
	fun withBalance(
		walletAddress: AddressWrapper,
		balance: BaseAmount,
		transactionAmount: BaseAmount,
		estimatedFees: BaseAmount,
	)
}
