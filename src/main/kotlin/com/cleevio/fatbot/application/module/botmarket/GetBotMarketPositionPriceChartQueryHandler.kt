package com.cleevio.fatbot.application.module.botmarket

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.bot.BotFinderService
import com.cleevio.fatbot.application.module.botmarket.finder.BotMarketPositionFinderService
import com.cleevio.fatbot.application.module.botmarket.port.out.GetBotMarketPositionPriceChart
import com.cleevio.fatbot.application.module.botmarket.query.GetBotMarketPositionPriceChartQuery
import org.springframework.stereotype.Component

@Component
class GetBotMarketPositionPriceChartQueryHandler(
	private val botFinderService: BotFinderService,
	private val botMarketPositionFinderService: BotMarketPositionFinderService,
	private val getBotMarketPositionPriceChart: GetBotMarketPositionPriceChart,
) : QueryHandler<List<GetBotMarketPositionPriceChartQuery.Result>, GetBotMarketPositionPriceChartQuery> {

	override val query = GetBotMarketPositionPriceChartQuery::class

	override fun handle(query: GetBotMarketPositionPriceChartQuery): List<GetBotMarketPositionPriceChartQuery.Result> {
		val bot = botFinderService.getByIdAndUserId(
			id = query.botId,
			userId = query.userId,
		)
		val marketPosition = botMarketPositionFinderService.getByIdAndBotId(
			id = query.botMarketPositionId,
			botId = bot.id,
		)

		return getBotMarketPositionPriceChart(tokenAddress = marketPosition.tokenAddress)
	}
}
