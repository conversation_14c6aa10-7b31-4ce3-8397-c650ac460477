package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.user.query.GetUserExistsByEmailQuery
import org.springframework.stereotype.Component

@Component
class GetUserExistsByEmailQueryHandler(
	private val firebaseUserFinderService: FirebaseUserFinderService,
) : QueryHandler<GetUserExistsByEmailQuery.Result, GetUserExistsByEmailQuery> {
	override val query = GetUserExistsByEmailQuery::class

	override fun handle(query: GetUserExistsByEmailQuery): GetUserExistsByEmailQuery.Result {
		val exists = firebaseUserFinderService.existByEmailIgnoringCase(email = query.email)
		return GetUserExistsByEmailQuery.Result(exists = exists)
	}
}
