package com.cleevio.fatbot.application.module.userstatistics.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.domain.userstatistics.UserStatistics
import com.cleevio.fatbot.domain.userstatistics.UserStatisticsRepository
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class UserStatisticsFinderService(
	private val userStatisticsRepository: UserStatisticsRepository,
) : BaseFinderService<UserStatistics>(userStatisticsRepository) {

	override fun errorBlock(message: String) = error(message)

	override fun getEntityType() = UserStatistics::class

	fun getByUserId(userId: UUID): UserStatistics {
		return userStatisticsRepository.findByUserId(userId) ?: error("User statistics for user id: $userId not found")
	}
}
