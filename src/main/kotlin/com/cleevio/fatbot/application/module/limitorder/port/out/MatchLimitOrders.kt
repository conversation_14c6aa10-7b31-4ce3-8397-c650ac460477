package com.cleevio.fatbot.application.module.limitorder.port.out

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import java.math.BigDecimal
import java.math.BigInteger
import java.util.UUID

interface MatchLimitOrders {

	data class Result(
		val id: UUID,
		val userId: UUID,
		val walletId: UUID,
		val walletAddress: AddressWrapper,
		val tokenAddress: AddressWrapper,
		val limitPrice: BigInteger,
		val type: LimitOrderType,
		val filledAmount: BigInteger,
		val remainingAmount: BigInteger,
	)

	operator fun invoke(tokenToPrice: Map<AddressWrapper, BigDecimal>): List<Result>
}
