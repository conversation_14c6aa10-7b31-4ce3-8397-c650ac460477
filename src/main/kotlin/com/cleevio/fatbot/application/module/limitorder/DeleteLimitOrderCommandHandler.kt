package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.limitorder.command.DeleteLimitOrderCommand
import com.cleevio.fatbot.application.module.limitorder.exception.LimitOrderLockedException
import com.cleevio.fatbot.application.module.limitorder.finder.LimitOrderFinderService
import com.cleevio.fatbot.application.module.limitorder.locks.LIMIT_ORDER_MODULE
import com.cleevio.fatbot.application.module.limitorder.locks.UPDATE_LIMIT_ORDER
import com.cleevio.fatbot.domain.limitorder.LimitOrderDeleteService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class DeleteLimitOrderCommandHandler(
	private val limitOrderFinderService: LimitOrderFinderService,
	private val limitOrderDeleteService: LimitOrderDeleteService,
) : CommandHandler<Unit, DeleteLimitOrderCommand> {

	override val command = DeleteLimitOrderCommand::class

	@Transactional
	@Lock(module = LIMIT_ORDER_MODULE, lockName = UPDATE_LIMIT_ORDER)
	override fun handle(@LockFieldParameter("limitOrderId") command: DeleteLimitOrderCommand) {
		val limitOrder = limitOrderFinderService.getByIdAndUserId(id = command.limitOrderId, userId = command.userId)

		if (limitOrder.isLocked) {
			throw LimitOrderLockedException(
				"Cannot delete limit order with pending transaction. Please wait for the transaction to complete.",
			)
		}

		limitOrderDeleteService.deleteById(limitOrderId = command.limitOrderId)
	}
}
