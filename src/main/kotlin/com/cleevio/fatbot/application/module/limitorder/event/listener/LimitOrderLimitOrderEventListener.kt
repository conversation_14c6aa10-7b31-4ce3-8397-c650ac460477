package com.cleevio.fatbot.application.module.limitorder.event.listener

import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.limitorder.command.BuyLimitOrderCommand
import com.cleevio.fatbot.application.module.limitorder.command.SellLimitOrderCommand
import com.cleevio.fatbot.application.module.limitorder.event.LimitOrderFillFailureEvent
import com.cleevio.fatbot.application.module.limitorder.event.LimitOrderMatchedEvent
import com.cleevio.fatbot.application.module.limitorder.service.LimitOrderFillService
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class LimitOrderLimitOrderEventListener(
	private val commandBus: CommandBus,
	private val limitOrderFillService: LimitOrderFillService,
) {

	@Async
	@TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
	fun onLimitOrderMatchedEvent(event: LimitOrderMatchedEvent) {
		val command = when (event.type) {
			LimitOrderType.BUY -> BuyLimitOrderCommand(
				orderId = event.orderId,
				pairAddress = event.pairAddress,
				walletBalance = event.walletBalance,
				amount = event.amount,
			)
			LimitOrderType.SELL -> SellLimitOrderCommand(
				orderId = event.orderId,
				pairAddress = event.pairAddress,
				walletBalance = event.walletBalance,
				amount = event.amount,
			)
		}

		commandBus(command)
	}

	@EventListener
	fun onLimitOrderFillFailureEvent(event: LimitOrderFillFailureEvent) {
		// Unlock limit order, so it can be retried
		limitOrderFillService.unlock(event.orderId)
	}
}
