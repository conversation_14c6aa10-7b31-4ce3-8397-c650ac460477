package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.util.truncateBySeconds
import com.cleevio.fatbot.application.module.exchangerate.port.out.GetExchangeRateSnapshots
import com.cleevio.fatbot.application.module.token.command.BackfillTokenPricesCommand
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.token.query.GetContinuousTokenPriceChartQuery
import com.cleevio.fatbot.application.module.tokenpricesnapshot.TokenPriceSnapshotService
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import java.math.MathContext
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit

@Component
class BackfillTokenPricesCommandHandler(
	private val tokenPriceSnapshotService: TokenPriceSnapshotService,
	private val getExchangeRateSnapshots: GetExchangeRateSnapshots,
	private val clock: Clock,
) : CommandHandler<Unit, BackfillTokenPricesCommand> {

	override val command = BackfillTokenPricesCommand::class

	@Lazy
	@Autowired
	private lateinit var queryBus: QueryBus

	private val logger = logger()

	@SentrySpan
	override fun handle(command: BackfillTokenPricesCommand) {
		val tokenPrices = queryBus(
			GetContinuousTokenPriceChartQuery.withoutNowItem(
				chain = command.chain,
				timeRange = TimeRange.DAY,
				pairAddress = command.pairAddress,
			),
		)

		if (tokenPrices.data.isEmpty()) return

		val receivedTokenPriceData = tokenPrices.data.map {
			val exchangeRate = it.close.divide(it.closeNative, MathContext.DECIMAL64)
			val price = it.closeNative.asNativeAmount().toBase(command.chain)

			TokenPriceSnapshotService.TokenPriceSnapshot(
				tokenAddress = command.tokenAddress,
				chain = command.chain,
				timestamp = it.timestamp,
				price = price,
				exchangeRate = exchangeRate,
			)
		}

		val oldestItem = receivedTokenPriceData.minBy { it.timestamp }

		val missingTokenPriceData = getMissingIntervalItems(
			timeRange = TimeRange.DAY,
			oldestItem = oldestItem,
		)

		val allTokenPriceData = receivedTokenPriceData + missingTokenPriceData

		tokenPriceSnapshotService.saveTokenPriceSnapshots(tokenPrices = allTokenPriceData)
	}

	private fun getMissingIntervalItems(
		timeRange: TimeRange,
		oldestItem: TokenPriceSnapshotService.TokenPriceSnapshot,
	): List<TokenPriceSnapshotService.TokenPriceSnapshot> {
		val oldestTimestamp = oldestItem.timestamp

		val intervalPeriod = timeRange.run {
			timeInterval.unit.duration.seconds * intervalCount
		}

		check(intervalPeriod == 300L) { "Interval period must be 5 minutes to align with Exchange rate snapshot period!" }

		// The oldest timestamp we want to have a snapshot for
		val targetOldestTimestamp = Instant.now(clock).minus(24, ChronoUnit.HOURS).truncateBySeconds(intervalPeriod)

		val secondsGap = oldestTimestamp.epochSecond - targetOldestTimestamp.epochSecond

		// Missing number of interval items with intervalPeriod to reach targetOldestTimestamp
		val missingItemsCount = secondsGap.div(intervalPeriod)

		val rates = getExchangeRateSnapshots(from = targetOldestTimestamp, to = oldestTimestamp, oldestItem.chain.currency)
			.mapKeys { (timestamp) -> timestamp.truncatedTo(ChronoUnit.MINUTES) }

		// Note: This matching logic works, because both intervalPeriod and exchange rate snapshot period are 5 minutes!
		val missingRecords = (1..missingItemsCount).mapNotNull {
			val timestamp = oldestTimestamp.minusSeconds(it * intervalPeriod)
			val rate = rates[timestamp]

			if (rate == null) {
				logger.warn("Failed to match exchange rate for $timestamp")

				return@mapNotNull null
			}

			oldestItem.copy(timestamp = timestamp, exchangeRate = rate)
		}

		return missingRecords
	}
}
