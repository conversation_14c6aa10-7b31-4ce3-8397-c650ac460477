package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.adapter.out.evm.context.UniswapV3Fee
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper

interface GetAmountOutUniswap {

	fun getAmountOutV3(
		chainId: Long,
		tokenAddress: AddressWrapper,
		amountIn: BaseAmount,
		fee: UniswapV3Fee,
		isBuy: Boolean,
	): BaseAmount

	fun getAmountOutV2(chainId: Long, tokenAddress: AddressWrapper, amountIn: BaseAmount, isBuy: Boolean): BaseAmount
}
