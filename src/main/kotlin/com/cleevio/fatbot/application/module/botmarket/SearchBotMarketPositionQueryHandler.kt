package com.cleevio.fatbot.application.module.botmarket

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.adapter.`in`.toInfiniteScrollSlice
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotMarketPosition
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator
import com.cleevio.fatbot.application.module.botmarket.query.SearchBotMarketPositionQuery
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.token.constant.toInstant
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.net.URI
import java.time.Clock
import java.time.Instant
import java.util.UUID

@Component
class SearchBotMarketPositionQueryHandler(
	private val findAllBotMarketPosition: FindAllBotMarketPosition,
	private val getTokenPrices: GetTokenPrices,
	private val fileUrlMapper: FileUrlMapper,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val clock: Clock,
) : QueryHandler<InfiniteScrollSlice<SearchBotMarketPositionQuery.Result, UUID>, SearchBotMarketPositionQuery> {

	override val query = SearchBotMarketPositionQuery::class

	override fun handle(
		query: SearchBotMarketPositionQuery,
	): InfiniteScrollSlice<SearchBotMarketPositionQuery.Result, UUID> {
		val allBotMarketPositions = findAllBotMarketPosition(
			userId = query.userId,
			botIds = query.filter.botIds,
			searchString = query.filter.searchString,
			isBotMarketPositionActive = query.filter.isBotMarketPositionActive,
			fileToUrlMapper = fileUrlMapper::map,
		)

		val now = Instant.now(clock)
		val from = query.filter.timeRange?.toInstant(now) ?: Instant.EPOCH
		val botMarketPositions = allBotMarketPositions.filter { it.positionOpenedAt in from..now }

		val chainToExchangeRate = Chain.entries.associateWith { getUsdExchangeRate(it.currency) }

		val tokens = botMarketPositions.filter { it.positionClosedAt == null }
			.mapToSet { it.tokenAddress.toChainAddress(it.chain) }
		val tokenAddressToTokenPrice = getTokenPrices(tokens = tokens)

		return botMarketPositions.mapNotNull { position ->
			val positionUsdValue = BotMarketPositionCalculator.toBotMarketPositionResult(
				botMarketPosition = position,
				tokenAddressToTokenPrice = tokenAddressToTokenPrice,
				currentExchangeRate = chainToExchangeRate.getValue(position.chain),
			)

			when (query.filter.marketPositionType) {
				SearchBotMarketPositionQuery.MarketPositionType.ALL -> Unit // just continue
				SearchBotMarketPositionQuery.MarketPositionType.PROFIT_ONLY ->
					if (positionUsdValue.isLoss()) return@mapNotNull null
				SearchBotMarketPositionQuery.MarketPositionType.LOSS_ONLY ->
					if (positionUsdValue.isProfit()) return@mapNotNull null
			}

			SearchBotMarketPositionQuery.Result(
				id = position.id,
				state = position.state,
				tokenAddress = position.tokenAddress,
				tokenDetailUrl = position.tokenAddress.getLinkToExplorer(position.chain).let { URI.create(it) },
				tokenName = position.tokenName,
				tokenSymbol = position.tokenSymbol,
				tokenChain = position.chain,
				tokenImageUrl = position.tokenImageUrl,
				openValueUsd = positionUsdValue.acquisitionValueUsd,
				closeValueUsd = position.positionClosedAt?.let { positionUsdValue.currentValueUsd },
				openTimeStampAt = position.positionOpenedAt,
				closedTimeStampAt = position.positionClosedAt,
				pnlAmountUsd = positionUsdValue.currentPnlUsd,
				pnlAmountFraction = positionUsdValue.currentPnlFraction,
				currentValueUsd = positionUsdValue.currentValueUsd,
			)
		}.toInfiniteScrollSlice(infiniteScroll = query.infiniteScroll, idSelector = { id })
	}

	private fun MarketPositionCalculator.UsdResult.isProfit() = currentPnlFraction >= BigDecimal.ZERO
	private fun MarketPositionCalculator.UsdResult.isLoss() = currentPnlFraction < BigDecimal.ZERO
}
