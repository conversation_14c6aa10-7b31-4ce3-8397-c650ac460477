package com.cleevio.fatbot.application.module.limitorder.event

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import java.util.UUID

data class LimitOrderMatchedEvent(
	val orderId: UUID,
	val walletId: UUID,
	val chain: Chain,
	val tokenAddress: AddressWrapper,
	val pairAddress: AddressWrapper,
	val type: LimitOrderType,
	val amount: BaseAmount,
	val walletBalance: BaseAmount,
)

data class LimitOrderFillFailureEvent(
	val orderId: UUID,
)
