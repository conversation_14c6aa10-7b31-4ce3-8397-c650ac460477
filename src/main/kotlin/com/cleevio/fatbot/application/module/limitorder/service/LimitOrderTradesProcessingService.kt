package com.cleevio.fatbot.application.module.limitorder.service

import com.cleevio.fatbot.adapter.out.bitquery.TokenTradeProvider
import com.cleevio.fatbot.application.common.util.collectCatching
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bottransaction.port.out.UpdateHeartBeat
import com.cleevio.fatbot.application.module.heartbeat.finder.HeartBeatFinderService
import com.cleevio.fatbot.application.module.limitorder.locks.LIMIT_ORDER_MODULE
import com.cleevio.fatbot.application.module.limitorder.locks.PROCESS_TOKEN_TRADES
import com.cleevio.fatbot.application.module.limitorder.port.out.GetLimitOrderPriceTrigger
import com.cleevio.fatbot.application.module.token.event.SolanaTokenTradedEvent
import com.cleevio.fatbot.application.module.token.event.TokenTradedEvent
import com.cleevio.fatbot.domain.heartbeat.HeartBeatCreateService
import com.cleevio.fatbot.domain.heartbeat.HeartBeatType
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.TryLock
import jakarta.annotation.PreDestroy
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Clock
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap
import kotlin.time.Duration.Companion.seconds

private val ACTIVE_LIMIT_ORDERS_REFRESH_INTERVAL = 10.seconds

@Service
class LimitOrderTradesProcessingService(
	private val heartBeatFinderService: HeartBeatFinderService,
	private val heartBeatCreateService: HeartBeatCreateService,
	private val clock: Clock,
	private val updateHeartBeat: UpdateHeartBeat,
	private val getLimitOrderPriceTrigger: GetLimitOrderPriceTrigger,
	private val limitOrderMatchingService: LimitOrderMatchingService,
	tokenTradeProvider: TokenTradeProvider,
) {

	private val logger = logger()

	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	private val tradesFlow = tokenTradeProvider.getTokenTradeFlow()

	private val tokenToPriceTrigger: ConcurrentMap<AddressWrapper, GetLimitOrderPriceTrigger.Result> = ConcurrentHashMap()

	@Transactional
	@TryLock(module = LIMIT_ORDER_MODULE, lockName = PROCESS_TOKEN_TRADES)
	fun tryStartTradesProcessing() {
		val heartBeat = heartBeatFinderService.findByType(type = HeartBeatType.LIMIT_ORDER)
			?: heartBeatCreateService.create(type = HeartBeatType.LIMIT_ORDER)

		// If it's active, early return
		val now = Instant.now(clock)
		if (heartBeat.isActive(now = now)) return

		heartBeat.beat(now = now)

		scope.launch {
			launch { heartBeat() }
			launch { processTradeEvents() }
		}

		logger.info("Started limit order trades processing")
	}

	@PreDestroy
	fun onStop() {
		scope.cancel()
	}

	suspend fun heartBeat() {
		while (true) {
			updateHeartBeat(now = Instant.now(clock), type = HeartBeatType.LIMIT_ORDER)

			val addressToTrigger = getLimitOrderPriceTrigger()
			tokenToPriceTrigger.clear()
			tokenToPriceTrigger.putAll(addressToTrigger)

			delay(ACTIVE_LIMIT_ORDERS_REFRESH_INTERVAL)
		}
	}

	private suspend fun processTradeEvents() {
		tradesFlow.collectCatching(
			exceptionHandler = { logger.error("Error in process trades events for limit orders", it) },
			collector = { trades ->
				val relevantTrades = trades
					.filterIsInstance<SolanaTokenTradedEvent>()
					.filter { trade -> trade.tokenAddress in tokenToPriceTrigger }

				val latestRelevantTrades = relevantTrades
					.sortedWith(compareByDescending<TokenTradedEvent> { it.blockHeight }.thenByDescending { it.transactionIndex })
					.distinctBy { trade -> trade.tokenAddress }
					.filter { trade ->
						val limitPrice = tokenToPriceTrigger.getValue(trade.tokenAddress)
						val minSellPrice = limitPrice.minSellPrice?.amount
						val maxBuyPrice = limitPrice.maxBuyPrice?.amount

						minSellPrice.notNullAnd { it < trade.price } || maxBuyPrice.notNullAnd { it > trade.price }
					}
					.associate { trade -> trade.tokenAddress to trade.price }

				if (latestRelevantTrades.isEmpty()) return@collectCatching

				limitOrderMatchingService.submitTokenPriceUpdate(latestRelevantTrades)
			},
		)
	}

	private fun <T> T?.notNullAnd(predicate: (T) -> Boolean) = this != null && predicate(this)
}
