package com.cleevio.fatbot.application.module.userleaderbord

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.userleaderbord.port.out.GetUserLeaderboardDonutsByRank
import com.cleevio.fatbot.application.module.userleaderbord.port.out.GetUserLeaderboardInfo
import com.cleevio.fatbot.application.module.userleaderbord.properties.LeaderboardMultiplierProperties
import com.cleevio.fatbot.application.module.userleaderbord.query.GetUserLeaderboardInfoQuery
import com.cleevio.fatbot.application.module.userstatistics.properties.UserStreakMultiplierProperties
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.RoundingMode

@Component
class GetUserLeaderboardInfoQueryHandler(
	private val getUserLeaderboardInfo: GetUserLeaderboardInfo,
	private val leaderboardMultiplierProperties: LeaderboardMultiplierProperties,
	private val getUserLeaderboardDonutsByRank: GetUserLeaderboardDonutsByRank,
	private val userStreakMultiplierProperties: UserStreakMultiplierProperties,
) : QueryHandler<GetUserLeaderboardInfoQuery.Result, GetUserLeaderboardInfoQuery> {

	override val query = GetUserLeaderboardInfoQuery::class

	override fun handle(query: GetUserLeaderboardInfoQuery): GetUserLeaderboardInfoQuery.Result {
		val result = getUserLeaderboardInfo(userId = query.userId)

		val nextRankThreshold = leaderboardMultiplierProperties.getNextRankThreshold(
			currentRank = result.rank ?: Int.MAX_VALUE,
		)

		val donutsNeededForNextThreshold = if (nextRankThreshold != null) {
			calculateDonutsNeededForNextThreshold(nextRank = nextRankThreshold, donutsGained = result.donutsGained)
		} else {
			BigDecimal.ZERO
		}

		val currentMultiplier = userStreakMultiplierProperties.getCurrentMultiplier(daysInStreak = result.daysInStreak)
		val multiplier = result.donutMultiplier.multiply(currentMultiplier)

		return GetUserLeaderboardInfoQuery.Result(
			userId = query.userId,
			donutsGained = result.donutsGained,
			donutsNeededForNextThreshold = donutsNeededForNextThreshold,
			volumeNeededForNextThreshold = donutsNeededForNextThreshold.divide(multiplier, 10, RoundingMode.HALF_DOWN),
			volume = result.volume,
			rank = result.rank,
			multiplier = multiplier,
		)
	}

	private fun calculateDonutsNeededForNextThreshold(nextRank: Int, donutsGained: BigDecimal): BigDecimal {
		val donutsInNextRank = getUserLeaderboardDonutsByRank(rank = nextRank)

		if (donutsInNextRank == BigDecimal.ZERO) {
			return donutsInNextRank
		}

		return donutsInNextRank - donutsGained
	}
}
