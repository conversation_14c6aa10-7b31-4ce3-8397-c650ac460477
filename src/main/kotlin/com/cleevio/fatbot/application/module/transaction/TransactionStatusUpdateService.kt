package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.limitorder.finder.LimitOrderFinderService
import com.cleevio.fatbot.application.module.market.finder.MarketPositionFinderService
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.referral.ReferralRewardService
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailure
import com.cleevio.fatbot.application.module.transaction.constant.TransactionPending
import com.cleevio.fatbot.application.module.transaction.constant.TransactionResult
import com.cleevio.fatbot.application.module.transaction.constant.TransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.constant.TransferCurrencyTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransferTokenTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.event.BuyTransactionSuccessfulEvent
import com.cleevio.fatbot.application.module.transaction.event.TransactionTryCountIncreaseEvent
import com.cleevio.fatbot.application.module.transaction.finder.TransactionFinderService
import com.cleevio.fatbot.application.module.transaction.locks.TRANSACTION_MODULE
import com.cleevio.fatbot.application.module.transaction.locks.UPDATE_TRANSACTION
import com.cleevio.fatbot.application.module.userstatistics.service.UpdateTradedAmountService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.domain.market.MarketPositionCreateService
import com.cleevio.fatbot.domain.market.MarketPositionDeleteService
import com.cleevio.fatbot.infrastructure.config.properties.TransactionProperties
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.BigInteger
import java.util.UUID

@Service
class TransactionStatusUpdateService(
	private val transactionFinderService: TransactionFinderService,
	private val transactionProperties: TransactionProperties,
	private val getExchangeRateUsd: GetUsdExchangeRate,
	private val marketPositionFinderService: MarketPositionFinderService,
	private val marketPositionCreateService: MarketPositionCreateService,
	private val marketPositionDeleteService: MarketPositionDeleteService,
	private val referralRewardService: ReferralRewardService,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val walletFinderService: WalletFinderService,
	private val updateTradedAmountService: UpdateTradedAmountService,
	private val limitOrderFinderService: LimitOrderFinderService,
) {

	@SentrySpan
	@Transactional
	@Lock(TRANSACTION_MODULE, UPDATE_TRANSACTION)
	fun updateStatus(@LockArgumentParameter transactionId: UUID, transactionResult: TransactionResult) {
		val tx = transactionFinderService.getById(transactionId)
		when (transactionResult) {
			is TransactionPending -> {
				tx.increaseTryCount(transactionProperties.verificationLimit)

				tx.signedTx?.let { applicationEventPublisher.publishEvent(TransactionTryCountIncreaseEvent(transactionId)) }
			}

			is TransactionFailure -> {
				tx.markAsFailed(failReason = transactionResult.failReason)

				if (tx.limitOrderId != null) {
					notifyLimitOrderFail(orderId = tx.limitOrderId)
				}
			}

			is TransferTokenTransactionSuccess -> {
				val exchangeRate = getExchangeRateUsd(cryptoCurrency = tx.chain.currency)
				tx.markAsSuccess(transactionResult, exchangeRate)
				updateMarketPosition(
					walletId = tx.walletId,
					chain = tx.chain,
					tokenAddress = tx.tokenAddress ?: error("Transaction's tokenAddress is null."),
					type = tx.type,
					amountIn = transactionResult.amountIn,
					amountOut = transactionResult.amountOut,
					exchangeRate = exchangeRate,
				)
			}

			is TransferCurrencyTransactionSuccess -> {
				tx.markAsSuccess(transactionResult, getExchangeRateUsd(cryptoCurrency = tx.chain.currency))
			}

			is BuySellTransactionSuccess -> {
				requireNotNull(tx.txHash) { "Transaction's txHash must not be null." }
				val exchangeRate = getExchangeRateUsd(cryptoCurrency = tx.chain.currency)
				tx.markAsSuccess(transactionResult, exchangeRate)

				updateMarketPosition(
					walletId = tx.walletId,
					chain = tx.chain,
					tokenAddress = tx.tokenAddress ?: error("Transaction's tokenAddress is null."),
					type = tx.type,
					amountIn = transactionResult.amountIn,
					amountOut = transactionResult.amountOut,
					exchangeRate = exchangeRate,
				)

				if (tx.limitOrderId != null) {
					/*
					TODO: An equivalent of instructionAmount will need to be implemented for EVM (currently always 0)
					A fast solution is to use `transactionResult.amountIn` for Sell order and `tx.baseValue` for Buy
					If we use `transactionResult.amountIn + transactionResult.fee` for Buy order, we risk imprecise result (rounding error).
					At the end of the day, this value needs to be "what amount the user passed in the swap call"
					 */
					check(tx.chain == Chain.SOLANA) { "Unsupported chain for limit order ${tx.chain}" }

					notifyLimitOrderSuccess(
						orderId = tx.limitOrderId,
						amountIn = transactionResult.instructionAmount,
					)
				}

				referralRewardService.saveReferralReward(
					userId = transactionResult.referralRewardRecipient,
					txHash = tx.txHash,
					amount = transactionResult.referralFee,
					chain = tx.chain,
				)

				updateTradedAmount(
					type = tx.type,
					amountIn = transactionResult.amountIn,
					amountOut = transactionResult.amountOut,
					exchangeRate = exchangeRate,
					walletId = tx.walletId,
					chain = tx.chain,
				)

				if (tx.type == TransactionType.BUY && tx.chain.id != null) {
					applicationEventPublisher.publishEvent(
						BuyTransactionSuccessfulEvent(
							walletId = tx.walletId,
							tokenAddress = tx.tokenAddress,
							chainId = tx.chain.evmId,
						),
					)
				}
			}

			is TransactionSuccess -> {
				tx.markAsSuccess()
			}
		}
	}

	private fun updateMarketPosition(
		walletId: UUID,
		chain: Chain,
		tokenAddress: AddressWrapper,
		type: TransactionType,
		amountIn: BigInteger,
		amountOut: BigInteger,
		exchangeRate: BigDecimal,
	) {
		val marketPosition = marketPositionFinderService.findMarketPosition(
			walletId = walletId,
			chain = chain,
			tokenAddress = tokenAddress,
		) ?: marketPositionCreateService.create(walletId, chain, tokenAddress)

		when (type) {
			TransactionType.BUY -> marketPosition.newBuy(
				amountOfTokensReceived = amountOut,
				amountOfBaseCurrencyPaid = amountIn,
				exchangeRate = exchangeRate,
			)

			TransactionType.SELL -> marketPosition.newSell(
				amountOfTokensSold = amountIn,
				amountOfBaseCurrencyReceived = amountOut,
			)

			TransactionType.TRANSFER_CURRENCY -> TODO("When transfer ETH is done.")
			TransactionType.TRANSFER_TOKEN -> marketPosition.newSell(
				amountOfTokensSold = amountOut,
				amountOfBaseCurrencyReceived = amountIn,
			)

			TransactionType.APPROVE -> error("Not possible to update market position based on APPROVE.")
			TransactionType.CLAIM_REFERRAL_REWARD -> error(
				"Not possible to update market position based on CLAIM_REFERRAL_REWARD.",
			)
		}

		if (marketPosition.isClosed()) {
			marketPositionDeleteService.delete(marketPosition.id)
		}
	}

	private fun notifyLimitOrderSuccess(orderId: UUID, amountIn: BigInteger) {
		val limitOrder = limitOrderFinderService.getById(orderId)

		limitOrder.fill(amountIn)
	}

	private fun notifyLimitOrderFail(orderId: UUID) {
		val limitOrder = limitOrderFinderService.getById(orderId)

		limitOrder.unlock()
	}

	private fun updateTradedAmount(
		type: TransactionType,
		amountIn: BigInteger,
		amountOut: BigInteger,
		exchangeRate: BigDecimal,
		walletId: UUID,
		chain: Chain,
	) {
		val userId = walletFinderService.getById(walletId).userId

		when (type) {
			TransactionType.BUY -> {
				// update donuts
				val solIn = amountIn.asBaseAmount().toNative(chain).amount

				updateTradedAmountService.update(
					userId = userId,
					tradeUsdAmount = solIn * exchangeRate,
				)
			}
			TransactionType.SELL -> {
				// update donuts
				val solOut = amountOut.asBaseAmount().toNative(chain).amount

				updateTradedAmountService.update(
					userId = userId,
					tradeUsdAmount = solOut * exchangeRate,
				)
			}
			else -> error("Unsupported transaction type")
		}
	}
}
