package com.cleevio.fatbot.application.module.userstatistics

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.atStartOfNextDay
import com.cleevio.fatbot.application.common.util.getWeekStart
import com.cleevio.fatbot.application.common.util.toLocalDateUTC
import com.cleevio.fatbot.application.module.userstatistics.constant.StreakDayState
import com.cleevio.fatbot.application.module.userstatistics.finder.UserStatisticsFinderService
import com.cleevio.fatbot.application.module.userstatistics.port.out.GetUserTradeDays
import com.cleevio.fatbot.application.module.userstatistics.properties.UserStreakMultiplierProperties
import com.cleevio.fatbot.application.module.userstatistics.query.GetUserStreakQuery
import com.cleevio.fatbot.application.module.userstatistics.query.GetUserStreakQuery.MultipliersThresholdResult
import com.cleevio.fatbot.domain.userstatistics.StreakState
import org.springframework.stereotype.Component
import java.time.Clock
import java.time.LocalDate

private const val NUM_OF_STREAK_DAYS = 6L

@Component
class GetUserStreakQueryHandler(
	private val userStatisticsFinderService: UserStatisticsFinderService,
	private val getUserTradeDays: GetUserTradeDays,
	private val userStreakMultiplierProperties: UserStreakMultiplierProperties,
	private val clock: Clock,
) : QueryHandler<GetUserStreakQuery.Result, GetUserStreakQuery> {

	override val query = GetUserStreakQuery::class

	override fun handle(query: GetUserStreakQuery): GetUserStreakQuery.Result {
		val userStatistics = userStatisticsFinderService.getByUserId(userId = query.userId)

		val today = LocalDate.now(clock)
		val weekStart = today.getWeekStart()

		val startDate = when (userStatistics.streakState) {
			StreakState.NOT_STARTED -> today
			// If streak is in progress or is failed the last streak started at timestamp must not be null
			StreakState.FAILED -> userStatistics.lastStreakStartedAt!!.toLocalDateUTC()
			StreakState.IN_PROGRESS -> weekStart.with(userStatistics.lastStreakStartedAt!!.toLocalDateUTC().dayOfWeek)
		}

		val userStatisticsCreatedDate = userStatistics.createdAt.toLocalDateUTC()

		val tradeDays = getUserTradeDays(
			userId = query.userId,
			startDate = startDate,
			endDate = startDate.plusDays(NUM_OF_STREAK_DAYS),
		).tradeDays

		val streakDaysInWeek = (0..NUM_OF_STREAK_DAYS).map { day ->
			val date = startDate.plusDays(day)

			val state = when {
				date in tradeDays -> StreakDayState.COMPLETED
				date > today -> StreakDayState.NEXT
				date == today -> StreakDayState.NOT_STARTED
				date < userStatisticsCreatedDate -> StreakDayState.NOT_STARTED
				else -> StreakDayState.FAILED
			}

			GetUserStreakQuery.StreakDayResult(
				date = date,
				state = state,
			)
		}

		val multiplier = userStreakMultiplierProperties.getCurrentMultiplier(
			daysInStreak = userStatistics.daysInStreak,
		)
		val nextMultiplier = userStreakMultiplierProperties.getNextMultiplierInfo(
			currentDaysInStreak = userStatistics.daysInStreak,
		)

		val threshold = userStreakMultiplierProperties.thresholds.map {
			MultipliersThresholdResult(
				daysInStreak = it.key,
				multiplier = it.value,
				completed = userStatistics.daysInStreak >= it.key,
			)
		}.sortedBy { it.daysInStreak }

		val streakExpiresAt = when (userStatistics.streakState) {
			StreakState.NOT_STARTED, StreakState.FAILED -> today.atStartOfNextDay()
			StreakState.IN_PROGRESS -> today.plusDays(1).atStartOfNextDay()
		}

		return GetUserStreakQuery.Result(
			userId = query.userId,
			daysInStreak = userStatistics.daysInStreak,
			currentMultiplier = multiplier,
			daysToNextStreak = nextMultiplier?.let { it.daysNeeded - userStatistics.daysInStreak },
			nextMultiplier = nextMultiplier?.multiplier,
			thresholds = threshold,
			streakDates = streakDaysInWeek,
			streakExpiresAt = streakExpiresAt,
			streakState = userStatistics.streakState,
			isThresholdDay = userStatistics.daysInStreak in userStreakMultiplierProperties.thresholds.keys,
		)
	}
}
