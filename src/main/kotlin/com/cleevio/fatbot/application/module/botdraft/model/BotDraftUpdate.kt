package com.cleevio.fatbot.application.module.botdraft.model

import com.cleevio.fatbot.adapter.`in`.rest.dto.PatchBotSettingsRequest
import com.cleevio.fatbot.application.common.util.ifNull
import com.cleevio.fatbot.domain.botdraft.BotDraft
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.PositiveOrZero
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.UUID

data class BotDraftUpdate(
	val name: String?,
	val avatarFileId: UUID?,
	@field:Positive val tradeAmount: BigDecimal?,
	@field:Positive val buyFrequency: Long?,
	@field:Positive val profitTargetFraction: BigDecimal?,
	@field:Positive val stopLossFraction: BigDecimal?,
	val tokenTickerCopyIsChecked: <PERSON><PERSON><PERSON>,
	val creatorHighBuyIsChecked: <PERSON><PERSON><PERSON>,
	val bundledBuysDetectedIsChecked: <PERSON><PERSON><PERSON>,
	val suspiciousWalletsDetectedIsChecked: <PERSON><PERSON><PERSON>,
	val singleHighBuyIsChecked: Boolean,
	val shouldAutoSellAfterHoldTime: Boolean,
	val buyTokensAliveAtLeastFor: Duration?,

	@field:PositiveOrZero val marketCapFromUsd: BigDecimal?,
	@field:Positive val marketCapToUsd: BigDecimal?,
	@field:PositiveOrZero val liquidityFromUsd: BigDecimal?,
	@field:Positive val liquidityToUsd: BigDecimal?,
	@field:PositiveOrZero val dailyVolumeFromUsd: BigDecimal?,
	@field:Positive val dailyVolumeToUsd: BigDecimal?,
	@field:PositiveOrZero val numberOfHoldersFrom: Long?,
	@field:Positive val numberOfHoldersTo: Long?,
	@field:DecimalMin("0.50") @field:DecimalMax("1.00") val buyVolume: BigDecimal?,
	@field:DecimalMin("0.00") @field:DecimalMax("0.50") val sellVolume: BigDecimal?,
	@field:DecimalMin("0.00") @field:DecimalMax("1.00") val sellTransactionFraction: BigDecimal?,
	@field:DecimalMin("0.00") @field:DecimalMax("1.00") val buyTransactionFraction: BigDecimal?,
)

private fun <T : Comparable<T>> getValidIntervalPair(from: T?, to: T?): Pair<T?, T?> {
	require(from == null || to == null || from < to) {
		"Invalid empty interval. $from >= $to"
	}

	return from to to
}

private fun getValidVolumeRange(buyVolume: BigDecimal?, sellVolume: BigDecimal?): Pair<BigDecimal?, BigDecimal?> {
	if ((buyVolume == null && sellVolume != null) || (buyVolume != null && sellVolume == null)) {
		throw IllegalArgumentException("Invalid volume, both buy volume and sell volume must be filled.")
	}

	if (buyVolume != null && sellVolume != null) {
		val volumeSum = buyVolume.plus(sellVolume).setScale(2, RoundingMode.CEILING)
		val requiredVolumeSum = BigDecimal.ONE.setScale(2)
		require(volumeSum == requiredVolumeSum) {
			"Invalid buy volume: $buyVolume and sell volume: $sellVolume, sum of two values must be always ${BigDecimal.ONE}."
		}
	}

	return buyVolume to sellVolume
}

private fun getValidTransactionFractionRange(
	buyTxFraction: BigDecimal?,
	sellTxFraction: BigDecimal?,
): Pair<BigDecimal?, BigDecimal?> {
	if ((buyTxFraction == null && sellTxFraction != null) || (buyTxFraction != null && sellTxFraction == null)) {
		throw IllegalArgumentException("Invalid transaction fraction, both buy and sell must be filled.")
	}

	if (buyTxFraction != null && sellTxFraction != null) {
		val sum = buyTxFraction.plus(sellTxFraction).setScale(2, RoundingMode.CEILING)
		val requiredSum = BigDecimal.ONE.setScale(2)
		require(sum == requiredSum) {
			"Invalid tx fraction: $buyTxFraction and tx fraction: $sellTxFraction, sum of two values must be always ${BigDecimal.ONE}."
		}
	}

	return buyTxFraction to sellTxFraction
}

fun PatchBotSettingsRequest.toValidBotDraftUpdate(draft: BotDraft): BotDraftUpdate {
	val (marketCapFrom, marketCapTo) = getValidIntervalPair(
		from = this.marketCapFromUsd.ifNull(draft.marketCapFromUsd),
		to = this.marketCapToUsd.ifNull(draft.marketCapToUsd),
	)

	val (liquidityFrom, liquidityTo) = getValidIntervalPair(
		from = this.liquidityFromUsd.ifNull(draft.liquidityFromUsd),
		to = this.liquidityToUsd.ifNull(draft.liquidityToUsd),
	)

	val (dailyVolumeFrom, dailyVolumeTo) = getValidIntervalPair(
		from = this.dailyVolumeFromUsd.ifNull(draft.dailyVolumeFromUsd),
		to = this.dailyVolumeToUsd.ifNull(draft.dailyVolumeToUsd),
	)

	val (numberOfHoldersFrom, numberOfHoldersTo) = getValidIntervalPair(
		from = this.numberOfHoldersFrom?.map { it.toLong() }.ifNull(draft.numberOfHoldersFrom),
		to = this.numberOfHoldersTo?.map { it.toLong() }.ifNull(draft.numberOfHoldersTo),
	)

	val (buyVolume, sellVolume) = getValidVolumeRange(
		buyVolume = this.buyVolume.ifNull(draft.buyVolume),
		sellVolume = this.sellVolume.ifNull(draft.sellVolume),
	)

	val (buyTransactionFraction, sellTransactionFraction) = getValidTransactionFractionRange(
		buyTxFraction = this.buyTransactionFraction.ifNull(draft.buyTransactionFraction),
		sellTxFraction = this.sellTransactionFraction.ifNull(draft.sellTransactionFraction),
	)

	val buyTokensAliveAtLeastFor = when (this.shouldWaitBeforeBuying) {
		true -> Duration.of(5, ChronoUnit.MINUTES)
		false -> null
		null -> draft.buyTokensAliveAtLeastFor
	}

	return BotDraftUpdate(
		name = this.name ?: draft.name,
		avatarFileId = this.avatarFileId ?: draft.avatarFileId,
		tradeAmount = this.tradeAmount ?: draft.tradeAmount,
		buyFrequency = this.buyFrequency?.toLong() ?: draft.buyFrequency,
		profitTargetFraction = this.profitTargetFraction ?: draft.profitTargetFraction,
		stopLossFraction = this.stopLossFraction ?: draft.stopLossFraction,
		tokenTickerCopyIsChecked = this.tokenTickerCopyIsChecked ?: draft.tokenTickerCopyIsChecked,
		creatorHighBuyIsChecked = this.creatorHighBuyIsChecked ?: draft.creatorHighBuyIsChecked,
		bundledBuysDetectedIsChecked = this.bundledBuysDetectedIsChecked ?: draft.bundledBuysDetectedIsChecked,
		suspiciousWalletsDetectedIsChecked = this.suspiciousWalletsDetectedIsChecked
			?: draft.suspiciousWalletsDetectedIsChecked,
		singleHighBuyIsChecked = this.singleHighBuyIsChecked ?: draft.singleHighBuyIsChecked,
		buyTokensAliveAtLeastFor = buyTokensAliveAtLeastFor,
		shouldAutoSellAfterHoldTime = this.shouldAutoSellAfterHoldTime ?: draft.shouldAutoSellAfterHoldTime,

		marketCapFromUsd = marketCapFrom,
		marketCapToUsd = marketCapTo,
		liquidityFromUsd = liquidityFrom,
		liquidityToUsd = liquidityTo,
		dailyVolumeFromUsd = dailyVolumeFrom,
		dailyVolumeToUsd = dailyVolumeTo,
		numberOfHoldersFrom = numberOfHoldersFrom,
		numberOfHoldersTo = numberOfHoldersTo,
		buyVolume = buyVolume,
		sellVolume = sellVolume,
		sellTransactionFraction = sellTransactionFraction,
		buyTransactionFraction = buyTransactionFraction,
	)
}
