package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.market.query.GetTokenDetailQuery
import com.cleevio.fatbot.application.module.token.finder.TokenPairInfoFinderService
import com.cleevio.fatbot.application.module.token.port.out.GetIntervalTokenPrices
import com.cleevio.fatbot.application.module.token.query.GetTokenPriceChartQuery
import com.cleevio.fatbot.application.module.token.query.TokenPriceTimeIntervalItem
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.time.Instant

@Component
class GetTokenPriceChartQueryHandler(
	private val getIntervalTokenPrices: GetIntervalTokenPrices,
	private val tokenPairInfoFinderService: TokenPairInfoFinderService,
	private val clock: Clock,
) : QueryHandler<GetTokenPriceChartQuery.Result, GetTokenPriceChartQuery> {

	override val query = GetTokenPriceChartQuery::class

	@Lazy
	@Autowired
	private lateinit var queryBus: QueryBus

	override fun handle(query: GetTokenPriceChartQuery): GetTokenPriceChartQuery.Result {
		val pairInfo = tokenPairInfoFinderService.getByPairAddressAndChain(
			pairAddress = query.pairAddress,
			chain = query.chain,
		)

		val tokenPrices = getIntervalTokenPrices(
			chain = query.chain,
			pairAddress = query.pairAddress,
			tokenAddress = pairInfo.tokenAddress,
			dex = pairInfo.dexType,
			timeRange = query.timeRange,
		)

		val intervalItems = tokenPrices.intervalItems.sortedBy { it.timestamp }

		if (!query.includeNowItem) return GetTokenPriceChartQuery.Result(data = intervalItems)

		// To make the graph end price and token detail end price the same, we have to use the cached data
		// from token detail
		val tokenDetail = queryBus(
			GetTokenDetailQuery.asAnonymousUser(
				chain = pairInfo.chain,
				tokenAddress = pairInfo.tokenAddress,
			),
		)

		val priceNowNative = (tokenDetail.tokenInfo as GetTokenDetailQuery.DexDetail).dexInfo.priceNative
		val priceNowUsd = tokenDetail.tokenInfo.dexInfo.priceUsd

		val tokenPriceTimeIntervalNow = TokenPriceTimeIntervalItem(
			timestamp = Instant.now(clock),
			close = priceNowUsd,
			closeNative = priceNowNative,
			volume = intervalItems.lastOrNull()?.volume ?: BigDecimal.ZERO,
		)

		val resultWithNow = intervalItems + tokenPriceTimeIntervalNow

		return GetTokenPriceChartQuery.Result(data = resultWithNow)
	}
}
