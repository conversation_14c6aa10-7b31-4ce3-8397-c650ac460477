package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.module.limitorder.command.CreateLimitOrderCommand
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.domain.limitorder.LimitOrderCreateService
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import org.springframework.stereotype.Component

@Component
class CreateLimitOrderCommandHandler(
	private val limitOrderCreateService: LimitOrderCreateService,
	private val tokenInfoFinderService: EvmTokenInfoFinderService,
) : CommandHandler<Unit, CreateLimitOrderCommand> {

	override val command = CreateLimitOrderCommand::class

	override fun handle(command: CreateLimitOrderCommand) {
		val initialAmountDecimals = when (command.type) {
			LimitOrderType.BUY -> command.chain.currency.decimals
			LimitOrderType.SELL -> {
				val tokenInfo = tokenInfoFinderService.getByTokenAddressAndChain(command.tokenAddress, command.chain)

				tokenInfo.decimals.intValueExact()
			}
		}

		limitOrderCreateService.create(
			userId = command.userId,
			walletId = command.walletId,
			tokenAddress = command.tokenAddress,
			chain = command.chain,
			limitPrice = command.limitPrice.toBase(chain = command.chain),
			initialAmount = command.initialAmount.toBase(initialAmountDecimals),
			type = command.type,
		)
	}
}
