package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutPumpfun
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutPumpswap
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutRaydium
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutUniswap
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.query.GetPredictedTokenAmountOnBuyQuery
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.token.finder.TokenPairInfoFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component

@Component
class GetPredictedTokenAmountOnBuyQueryHandler(
	private val getAmountOutUniswap: GetAmountOutUniswap,
	private val getAmountOutPumpfun: GetAmountOutPumpfun,
	private val getAmountOutRaydium: GetAmountOutRaydium,
	private val getAmountOutPumpswap: GetAmountOutPumpswap,
	private val evmTokenInfoFinderService: EvmTokenInfoFinderService,
	private val tokenPairInfoFinderService: TokenPairInfoFinderService,
) : QueryHandler<GetPredictedTokenAmountOnBuyQuery.Result, GetPredictedTokenAmountOnBuyQuery> {

	override val query = GetPredictedTokenAmountOnBuyQuery::class

	override fun handle(query: GetPredictedTokenAmountOnBuyQuery): GetPredictedTokenAmountOnBuyQuery.Result {
		val token = evmTokenInfoFinderService.getByTokenAddressAndChain(
			tokenAddress = query.tokenAddress,
			chain = query.chain,
		)

		val baseAmountIn = query.buyForCurrencyNativeAmount.toBase(query.chain)

		val amountOut = when (query.dexPairInfo) {
			is GetDex.UniswapV2, is GetDex.PancakeswapV2 -> getAmountOutUniswap.getAmountOutV2(
				chainId = query.chain.evmId,
				tokenAddress = query.tokenAddress,
				amountIn = baseAmountIn,
				isBuy = true,
			)

			is GetDex.UniswapV3 -> getAmountOutUniswap.getAmountOutV3(
				chainId = query.chain.evmId,
				tokenAddress = query.tokenAddress,
				amountIn = baseAmountIn,
				fee = query.dexPairInfo.fee,
				isBuy = true,
			)

			is GetDex.PancakeswapV3 -> getAmountOutUniswap.getAmountOutV3(
				chainId = query.chain.evmId,
				tokenAddress = query.tokenAddress,
				amountIn = baseAmountIn,
				fee = query.dexPairInfo.fee,
				isBuy = true,
			)

			is GetDex.PumpFun -> getAmountOutPumpfun(
				amount = baseAmountIn,
				pairAddress = query.dexPairInfo.pairAddress,
				isBuy = true,
			)

			is GetDex.Raydium -> when (query.dexPairInfo.poolType) {
				GetDex.PoolType.AMM -> {
					val tokenPair = tokenPairInfoFinderService.getByPairAddressAndChain(query.dexPairInfo.pairAddress, Chain.SOLANA)
					val marketData = tokenPair.raydiumAmmMarketData ?: error(
						"AMM market data missing for pair ${tokenPair.pairAddress}",
					)

					getAmountOutRaydium.getAmountOutAMM(
						amount = baseAmountIn,
						pairAddress = query.dexPairInfo.pairAddress,
						marketData = marketData,
						isBuy = true,
					)
				}
				GetDex.PoolType.CLMM -> getAmountOutRaydium.getAmountOutCLMM(
					amount = baseAmountIn,
					pairAddress = query.dexPairInfo.pairAddress,
					tokenAddress = query.tokenAddress,
					isBuy = true,
				)
				GetDex.PoolType.CPMM -> {
					val tokenPair = tokenPairInfoFinderService.getByPairAddressAndChain(query.dexPairInfo.pairAddress, query.chain)
					val marketData = tokenPair.raydiumCpmmMarketData ?: error(
						"CPMM market data missing for pair ${tokenPair.pairAddress}",
					)

					getAmountOutRaydium.getAmountOutCPMM(
						amount = baseAmountIn,
						pairAddress = query.dexPairInfo.pairAddress,
						tokenAddress = query.tokenAddress,
						marketData = marketData,
						isBuy = true,
					)
				}
			}

			is GetDex.PumpSwap -> getAmountOutPumpswap(
				amount = baseAmountIn,
				tokenAddress = query.tokenAddress,
				pairAddress = query.dexPairInfo.pairAddress,
				isToken2022 = token.isToken2022,
				isBuy = true,
			)

			is GetDex.Meteora -> TODO("Implement for Meteora")
		}

		val amountOutNative = amountOut.toNative(token.decimals.toInt())

		return GetPredictedTokenAmountOnBuyQuery.Result(predictedTokenNativeAmount = amountOutNative)
	}
}
