package com.cleevio.fatbot.application.module.transaction.event

import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import java.util.UUID

data class TokenSoldEvent(
	val walletId: UUID,
	val signedTxList: List<SignedTx>,
	val tokenAddress: AddressWrapper,
)

data class LimitOrderTokenSoldEvent(
	val limitOrderId: UUID,
	val walletId: UUID,
	val signedTxList: List<SignedTx>,
	val tokenAddress: AddressWrapper,
)
