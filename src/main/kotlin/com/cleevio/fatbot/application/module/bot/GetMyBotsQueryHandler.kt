package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.compareTo
import com.cleevio.fatbot.application.common.crypto.plus
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.bot.constant.BotStatus
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotMarketPosition
import com.cleevio.fatbot.application.module.bot.port.out.GetMyBots
import com.cleevio.fatbot.application.module.bot.query.GetMyBotsQuery
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.time.Instant
import java.util.UUID

@Component
class GetMyBotsQueryHandler(
	private val getMyBots: GetMyBots,
	private val findAllBotMarketPosition: FindAllBotMarketPosition,
	private val getTokenPrices: GetTokenPrices,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val fileUrlMapper: FileUrlMapper,
	private val usdConverter: UsdConverter,
	private val clock: Clock,
) : QueryHandler<List<GetMyBotsQuery.Result>, GetMyBotsQuery> {

	override val query = GetMyBotsQuery::class

	@SentrySpan
	override fun handle(query: GetMyBotsQuery): List<GetMyBotsQuery.Result> {
		val now = Instant.now(clock)

		val myAllBots = getMyBots(
			userId = query.userId,
			timeRange = query.timeRange,
			now = now,
			fileToUrlMapper = fileUrlMapper::map,
		)

		val bots = myAllBots.filterIsInstance<GetMyBots.Result.Bot>()
		val botDrafts = myAllBots.filterIsInstance<GetMyBots.Result.BotDraft>()

		val botToPortfolioValueOverview = getPortfolioOverviews(
			userId = query.userId,
			bots = bots,
		)

		val tokenAccountRent = SolanaConstants.BOT_BUY_RENT_BASE_AMOUNT
		val buyFeesToFreeze = SolanaConstants.BOT_BUY_FEE_FREEZE_BASE_AMOUNT
		val sellFeesToFreeze = SolanaConstants.BOT_SELL_FEE_FREEZE_BASE_AMOUNT
		val minimumRentOnWalletAfterBuy = SolanaConstants.MINIMUM_RENT_THRESHOLD

		val botResults = bots.map {
			val overview = botToPortfolioValueOverview.getValue(it.id)

			val botNeeds = it.tradeAmount +
				tokenAccountRent +
				buyFeesToFreeze +
				sellFeesToFreeze +
				minimumRentOnWalletAfterBuy

			val botStatus = when {
				!it.isActive -> BotStatus.DEACTIVATED
				it.walletBalance < botNeeds && it.buyCount != it.sellCount -> BotStatus.INSUFFICIENT_BALANCE_FUNDS_IN_OPEN_POSITIONS
				it.walletBalance < botNeeds -> BotStatus.INSUFFICIENT_BALANCE
				it.remainingBuyFrequency == 0L -> BotStatus.BUY_DAILY_LIMIT_REACHED
				overview.transactionCount == 0 -> BotStatus.NO_PURCHASE
				else -> BotStatus.HAS_PURCHASED
			}

			GetMyBotsQuery.Result(
				id = it.id,
				isActive = it.isActive,
				numberOfActiveDays = it.numberOfActiveDays,
				avatarFileId = it.avatarFileId,
				name = it.name,
				buyCount = it.buyCount,
				sellCount = it.sellCount,
				avatarFileUrl = it.avatarFileUrl,
				draftCompleteness = null,
				botWalletAddress = it.walletAddress,
				balanceUsd = overview.totalValueUsd,
				timeRangeChangeUsd = overview.timeRangeChangeUsd,
				timeRangeChangeFraction = overview.timeRangeChangeFraction,
				botStatus = botStatus,
			)
		}

		val botDraftResults = botDrafts.map {
			GetMyBotsQuery.Result(
				id = it.id,
				isActive = it.isActive,
				numberOfActiveDays = it.numberOfActiveDays,
				avatarFileId = it.avatarFileId,
				name = it.name,
				avatarFileUrl = it.avatarFileUrl,
				draftCompleteness = it.draftCompleteness,
				buyCount = null,
				sellCount = null,
				botWalletAddress = null,
				balanceUsd = null,
				timeRangeChangeUsd = null,
				timeRangeChangeFraction = null,
				botStatus = BotStatus.DEACTIVATED,
			)
		}

		return botResults + botDraftResults
	}

	private data class BotPortfolioValueOverview(
		val totalValueUsd: BigDecimal,
		val timeRangeChangeUsd: BigDecimal,
		val timeRangeChangeFraction: BigDecimal,
		val transactionCount: Int,
	)

	private fun getPortfolioOverviews(
		userId: UUID,
		bots: List<GetMyBots.Result.Bot>,
	): Map<UUID, BotPortfolioValueOverview> {
		val botIds = bots.mapToSet { it.id }

		val allBotMarketPositions = findAllBotMarketPosition(
			userId = userId,
			botIds = botIds,
			isBotMarketPositionActive = null,
			searchString = null,
			fileToUrlMapper = fileUrlMapper::map,
		)

		val openPositions = allBotMarketPositions.filter { it.positionClosedAt == null }
		val openPositionTokens = openPositions.mapToSet { it.tokenAddress.toChainAddress(it.chain) }
		val tokenAddressToTokenPrice = getTokenPrices(tokens = openPositionTokens)

		val chainToExchangeRate = Chain.entries.associateWith { getUsdExchangeRate(it.currency) }

		val botIdToMarketPositionValue = BotMarketPositionCalculator.botToMarketPositionsResults(
			botMarketPositions = openPositions,
			tokenAddressToTokenPrice = tokenAddressToTokenPrice,
			chainToExchangeRate = chainToExchangeRate,
		)

		val botIdToOverview = bots.associate { bot ->
			val walletBalanceUsd = usdConverter.baseToUsd(bot.walletBalance, bot.chain, chainToExchangeRate.getValue(bot.chain))
			val marketPositionsValueUsd = botIdToMarketPositionValue[bot.id]
				?.let { it.currentValueUsd + it.rentUsd }
				?: BigDecimal.ZERO

			val totalValueNowUsd = marketPositionsValueUsd + walletBalanceUsd

			val timeRangeChangeUsd = totalValueNowUsd - bot.acquisitionValueUsd
			val timeRangeChangeFraction = calculateFractionChange(totalValueNowUsd, bot.acquisitionValueUsd)

			val transactionCount = allBotMarketPositions.sumOf {
				val transactionsForPosition = when (it.positionClosedAt == null) {
					true -> 1 // open position so can only have buy
					false -> 2 // closed position so must have buy and sell
				}
				transactionsForPosition
			}

			val overview = BotPortfolioValueOverview(
				totalValueUsd = totalValueNowUsd,
				timeRangeChangeUsd = timeRangeChangeUsd,
				timeRangeChangeFraction = timeRangeChangeFraction,
				transactionCount = transactionCount,
			)

			bot.id to overview
		}

		return botIdToOverview
	}
}
