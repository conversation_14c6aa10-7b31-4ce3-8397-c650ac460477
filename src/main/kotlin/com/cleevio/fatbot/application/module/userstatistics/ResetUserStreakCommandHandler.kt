package com.cleevio.fatbot.application.module.userstatistics

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.userstatistics.command.ResetUserStreakCommand
import com.cleevio.fatbot.application.module.userstatistics.finder.UserStatisticsFinderService
import com.cleevio.fatbot.application.module.userstatistics.locks.UPDATE_USER_STATISTICS
import com.cleevio.fatbot.application.module.userstatistics.locks.USER_STATISTICS_MODULE
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class ResetUserStreakCommandHandler(
	private val userStatisticsFinderService: UserStatisticsFinderService,
) : CommandHandler<Unit, ResetUserStreakCommand> {
	override val command = ResetUserStreakCommand::class

	@Transactional
	@Lock(module = USER_STATISTICS_MODULE, lockName = UPDATE_USER_STATISTICS)
	override fun handle(@LockFieldParameter("userId") command: ResetUserStreakCommand) {
		val userStatistics = userStatisticsFinderService.getByUserId(userId = command.userId)
		userStatistics.resetStreak()
	}
}
