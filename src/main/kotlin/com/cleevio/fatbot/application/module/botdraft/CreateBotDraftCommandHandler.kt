package com.cleevio.fatbot.application.module.botdraft

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.bot.BotFinderService
import com.cleevio.fatbot.application.module.botdraft.command.CreateBotDraftCommand
import com.cleevio.fatbot.application.module.botdraft.finder.BotDraftFinderService
import com.cleevio.fatbot.application.module.botdraft.locks.BOT_DRAFT_CREATE
import com.cleevio.fatbot.application.module.botdraft.locks.BOT_DRAFT_MODULE
import com.cleevio.fatbot.domain.bot.MAX_BOTS_PER_USER
import com.cleevio.fatbot.domain.botdraft.BotDraftCreateService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class CreateBotDraftCommandHandler(
	private val botDraftFinderService: BotDraftFinderService,
	private val botDraftCreateService: BotDraftCreateService,
	private val botFinderService: BotFinderService,
) : CommandHandler<CreateBotDraftCommand.Result, CreateBotDraftCommand> {
	override val command = CreateBotDraftCommand::class

	@SentrySpan
	@Transactional
	@Lock(module = BOT_DRAFT_MODULE, lockName = BOT_DRAFT_CREATE)
	override fun handle(@LockFieldParameter("userId") command: CreateBotDraftCommand): CreateBotDraftCommand.Result {
		val existingDrafts = botDraftFinderService.findAllByUserId(command.userId)
		val existingBots = botFinderService.findAllByUserId(command.userId)

		if (existingDrafts.size + existingBots.size >= MAX_BOTS_PER_USER) {
			error("User ${command.userId} reached a maximum of $MAX_BOTS_PER_USER bots")
		}

		val newBotDraft = botDraftCreateService.create(userId = command.userId)

		return CreateBotDraftCommand.Result(botDraftId = newBotDraft.id)
	}
}
