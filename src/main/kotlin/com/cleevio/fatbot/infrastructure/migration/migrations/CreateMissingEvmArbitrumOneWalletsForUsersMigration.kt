package com.cleevio.fatbot.infrastructure.migration.migrations

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
class CreateMissingEvmArbitrumOneWalletsForUsersMigration(
	private val createMissingChainWalletsForUserMigration: CreateMissingChainWalletsForUserMigration,
) : Migration {

	override val migrationId: UUID = UUID.fromString("e2fb2ae0-fda3-403f-a0fe-95c3c060a35d")

	@Transactional
	override fun migrate() {
		createMissingChainWalletsForUserMigration(chain = Chain.EVM_ARBITRUM_ONE)
	}
}
