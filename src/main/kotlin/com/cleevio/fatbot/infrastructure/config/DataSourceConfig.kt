package com.cleevio.fatbot.infrastructure.config

import com.zaxxer.hikari.HikariDataSource
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.EnableTransactionManagement
import java.sql.Connection
import javax.sql.DataSource

@Configuration
@EnableTransactionManagement
class DataSourceConfig {

	open class HikariCpWrapper(private val dataSource: DataSource) : DataSource by dataSource {
		@SentrySpan(description = "Acquire DB connection from pool")
		override fun getConnection(): Connection {
			return dataSource.connection
		}
	}

	@Bean
	@Primary
	fun defaultSentryObservableDataSource(config: DataSourceProperties): DataSource {
		val dataSource = config.initializeDataSourceBuilder()
			.type(HikariDataSource::class.java)
			.build()

		return HikariCpWrapper(dataSource)
	}

	@Bean(name = ["matchMakingDataSource"])
	fun matchMakingObservableDataSource(config: DataSourceProperties): DataSource {
		val dataSource = config.initializeDataSourceBuilder()
			.type(HikariDataSource::class.java)
			.build()

		return HikariCpWrapper(dataSource)
	}

	/**
	 * Default transaction manager (connection pool) for application, will be used with '@Transactional' annotation
	 * without need to specify the transaction manager name in the annotation
	 */
	@Bean
	@Primary
	fun transactionManager(platformTransactionManager: PlatformTransactionManager) = platformTransactionManager

	/**
	 * Separate transaction manager (connection pool) for application, should be used with match making logic in
	 * matchmaking package. The name of the transaction manager 'matchMakingTransactionManager' needs to be specified
	 * in the '@Transactional(transactionManager = "matchMakingTransactionManager")' annotation like so in order to be
	 * used instead of the default one.
	 */
	@Bean
	fun matchMakingTransactionManager(@Qualifier("matchMakingDataSource") matchMakingObservableDataSource: DataSource) =
		JpaTransactionManager().apply { dataSource = matchMakingObservableDataSource }
}
