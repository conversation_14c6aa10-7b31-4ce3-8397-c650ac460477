package com.cleevio.fatbot.infrastructure.config

import com.cleevio.fatbot.application.module.access.port.out.ParseToken
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders
import org.springframework.messaging.Message
import org.springframework.messaging.MessageChannel
import org.springframework.messaging.converter.MappingJackson2MessageConverter
import org.springframework.messaging.converter.MessageConverter
import org.springframework.messaging.simp.config.ChannelRegistration
import org.springframework.messaging.simp.config.MessageBrokerRegistry
import org.springframework.messaging.simp.stomp.StompCommand
import org.springframework.messaging.simp.stomp.StompHeaderAccessor
import org.springframework.messaging.support.ChannelInterceptor
import org.springframework.messaging.support.MessageHeaderAccessor
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker
import org.springframework.web.socket.config.annotation.StompEndpointRegistry
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer

@Configuration
@EnableWebSocketMessageBroker
class WebSocketConfig(
	private val mappingJackson2MessageConverter: MappingJackson2MessageConverter,
	private val parseToken: ParseToken,
	private val firebaseUserFinderService: FirebaseUserFinderService,
) : WebSocketMessageBrokerConfigurer {

	private val logger = logger()

	override fun registerStompEndpoints(registry: StompEndpointRegistry) {
		registry.addEndpoint("/ws")
			.setAllowedOriginPatterns("*")
	}

	override fun configureMessageBroker(registry: MessageBrokerRegistry) {
		registry.enableSimpleBroker("/topic", "/queue")
		registry.setApplicationDestinationPrefixes("/app")
		registry.setUserDestinationPrefix("/user")
	}

	override fun configureClientInboundChannel(registration: ChannelRegistration) {
		registration.interceptors(
			object : ChannelInterceptor {
				override fun preSend(message: Message<*>, channel: MessageChannel): Message<*>? {
					val accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor::class.java) ?: return null

					if (accessor.command == StompCommand.CONNECT && accessor.user == null) {
						val jwt = accessor.getAuthorizationHeader()

						if (jwt != null) {
							runCatching {
								val email = parseToken(token = jwt).email
								val user = firebaseUserFinderService.getByEmailIgnoringCase(email)

								accessor.user = UsernamePasswordAuthenticationToken(user.id, null, null)
							}.onFailure {
								logger.warn("Failed to authenticate WebSocket connection")
							}
						}
					}

					return message
				}
			},
		)
	}

	override fun configureMessageConverters(messageConverters: MutableList<MessageConverter>): Boolean {
		messageConverters.add(mappingJackson2MessageConverter)
		return false
	}
}

private fun StompHeaderAccessor.getAuthorizationHeader(): String? =
	getFirstNativeHeader(HttpHeaders.AUTHORIZATION)?.substring("Bearer ".length)
