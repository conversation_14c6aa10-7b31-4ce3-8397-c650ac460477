package com.cleevio.fatbot.infrastructure.config

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

data class FatbotDispatchers(
	@Suppress("PropertyName")
	val IO: CoroutineDispatcher,
	@Suppress("PropertyName")
	val Caffeine: CoroutineDispatcher,
)

@Configuration
class CoroutineConfig {

	@Bean
	fun fatbotDispatchers() = FatbotDispatchers(
		IO = Dispatchers.IO,
		/**
		 * Caffeine tasks are predominately blocking, non CPU-intensive IO calls.
		 * The main advantage of using `limitedParallelism` is resource sharing (see [CoroutineDispatcher.limitedParallelism])
		 * with the commonly used [Dispatchers.IO]
		 */
		Caffeine = Dispatchers.IO.limitedParallelism(100),
	)
}
