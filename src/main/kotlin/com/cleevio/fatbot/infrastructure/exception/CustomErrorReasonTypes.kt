package com.cleevio.fatbot.infrastructure.exception

import com.cleevio.library.exceptionhandler.service.model.ErrorCode
import com.cleevio.library.exceptionhandler.service.model.ErrorReason

enum class ExtendedErrorReasonType : ErrorReason {
	FILE_NOT_FOUND,
	FILE_NOT_DELETED,
	<PERSON><PERSON><PERSON>OAD_FAILED_EXCEPTION,

	INVALID_TOKEN,
	ACCESS_DENIED,
	INVALID_PASSWORD,
	TOO_MANY_ATTEMPTS,

	USER_NOT_FOUND,

	ILLEGAL_CHAIN,
	MISSING_CHAIN_ID,
	UNSUPPORTED_CHAIN_ID,
	INVALID_CHAIN_ADDRESS_COMBINATION_EXCEPTION,
	INVALID_DEX_PAIR_PROVIDED,
	TOKEN_NOT_FOUND,
	MARKET_POSITION_NOT_FOUND,
	DATA_NOT_PROVIDED,
	FAILED_TO_VERIFY_PUMPSWAP_PAIR,

	WALLET_NOT_FOUND,
	MAX_WALLET_COUNT_EXCEEDED,
	LAST_WALLET_DELETION_EXCEPTION,
	WALLET_ALREADY_IMPORTED,
	FAILED_TO_GET_ADDRESS_FROM_PRIVATE_KEY,
	FAILED_TO_GET_WALLET_BALANCE,
	FAILED_TO_GET_TOKEN_BALANCE,
	INSUFFICIENT_FUNDS_FOR_TRANSACTION_FEES,
	INSUFFICIENT_FUNDS_FOR_TRANSACTION,
	TRANSACTION_LIKELY_FAIL,

	LIMIT_ORDER_NOT_FOUND,
	LIMIT_ORDER_LOCKED,

	TRANSACTION_NOT_FOUND,

	TITAN_BUILDER_API_EXCEPTION,

	REFERRAL_CODE_NOT_VALID,
	REFERRAL_CODE_ALREADY_IN_USE,
	REFERRAL_CODE_ALREADY_SET,
	REFERRAL_CODE_NOT_FOUND,

	REFERRAL_REWARD_NOT_FOUND,
	REFERRAL_REWARD_ALREADY_CLAIMED,
	REFERRAL_REWARD_BELOW_THRESHOLD,

	ILLEGAL_QUICK_BUY_VALUE,

	BOT_DRAFT_NOT_FOUND,

	BOT_NOT_FOUND,
	MAX_ACTIVE_BOTS_EXCEEDED,
	INVALID_BOT_STATE_TRANSITION,

	BOT_MARKET_POSITION_NOT_OPENED_EXCEPTION,

	BOT_WALLET_NOT_FOUND,
	BOT_MARKET_POSITION_NOT_FOUND,
	BOT_MARKET_POSITION_TRADE_STATE_NOT_FOUND,
	BOT_TRANSACTION_NOT_FOUND,

	WITHDRAWAL_BOT_INVALID_STATE,

	NOT_YET_IMPLEMENTED,

	ILLEGAL_USER_LEADERBOARD_ACCESS,

	USER_FATTY_CARD_ALREADY_CLAIMED,
	USER_FATTY_CARD_NOT_FOUND,
	USER_FATTY_CARD_ALREADY_DISPLAYED,

	USER_STATISTICS_NOT_FOUND,

	FATTY_CARD_NOT_FOUND,

	FATTY_LEAGUE_SEASON_NOT_FOUND,
	FATTY_TOKENS_NOT_CLAIMABLE,

	USER_STREAK_CANNOT_BE_RESET,
	;

	override val errorCode: ErrorCode
		get() = ErrorCode(this.name)
}
