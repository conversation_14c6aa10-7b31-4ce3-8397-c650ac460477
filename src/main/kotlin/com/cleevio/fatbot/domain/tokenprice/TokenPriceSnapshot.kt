package com.cleevio.fatbot.domain.tokenprice

import com.cleevio.fatbot.application.common.converter.AddressWrapperHibernateConverter
import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.BaseDomainEntity
import jakarta.persistence.Convert
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.util.UUID

@Entity
class TokenPriceSnapshot(
	id: UUID = UUIDv7(),
	@Convert(converter = AddressWrapperHibernateConverter::class)
	val tokenAddress: AddressWrapper,

	@Enumerated(EnumType.STRING)
	val chain: Chain,

	val priceWei: BigInteger,
	val exchangeRateUsd: BigDecimal,
	val validAt: Instant,
) : BaseDomainEntity(id)

@Repository
interface TokenPriceRepository : JpaRepository<TokenPriceSnapshot, UUID> {
	@Query("DELETE FROM token_price_snapshot WHERE valid_at < :before", nativeQuery = true)
	@Modifying
	fun deleteSnapshotsBefore(before: Instant)
}
