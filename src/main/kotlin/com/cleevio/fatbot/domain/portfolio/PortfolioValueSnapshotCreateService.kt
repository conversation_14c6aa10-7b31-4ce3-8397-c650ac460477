package com.cleevio.fatbot.domain.portfolio

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

@Service
class PortfolioValueSnapshotCreateService(
	private val portfolioValueSnapshotRepository: PortfolioValueSnapshotRepository,
) {

	fun createPortfolioValueSnapshot(userId: UUID, chain: Chain, date: LocalDate, currentValueUsd: BigDecimal) {
		portfolioValueSnapshotRepository.save(
			PortfolioValueSnapshot(
				userId = userId,
				chain = chain,
				date = date,
				valueUsd = currentValueUsd,
			),
		)
	}
}
