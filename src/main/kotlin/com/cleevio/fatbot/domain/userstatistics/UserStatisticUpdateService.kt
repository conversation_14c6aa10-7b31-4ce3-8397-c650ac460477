package com.cleevio.fatbot.domain.userstatistics

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate

@Service
class UserStatisticUpdateService(
	private val userStatisticsRepository: UserStatisticsRepository,
) {

	@Transactional
	fun resetLeaderboardForAll() = userStatisticsRepository.resetLeaderboardForAll()

	@Transactional
	fun resetLeagueDonutsForAll() = userStatisticsRepository.resetLeagueDonutsForAll()

	@Transactional
	fun resetTradedAmountInDayForAll() = userStatisticsRepository.resetTradedAmountInDayForAll()

	@Transactional
	fun updateDaysInStreakForAll(date: LocalDate) = userStatisticsRepository.updateDaysInStreakForAll(date)
}
