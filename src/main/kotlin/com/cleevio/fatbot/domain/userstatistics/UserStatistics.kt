package com.cleevio.fatbot.domain.userstatistics

import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.module.userstatistics.exception.UserStreakCannotBeResetException
import com.cleevio.fatbot.domain.BaseDomainEntity
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

enum class StreakState {
	FAILED,
	NOT_STARTED,
	IN_PROGRESS,
}

@Entity
class UserStatistics(
	id: UUID = UUIDv7(),
	daysInStreak: Int = 0,
	lastManualTradeAt: LocalDate? = null,
	val userId: UUID,
) : BaseDomainEntity(id) {

	var leaderboardDonuts: BigDecimal = BigDecimal.ZERO
		private set

	var leaderboardTradedAmount: BigDecimal = BigDecimal.ZERO
		private set

	var leagueDonuts: BigDecimal = BigDecimal.ZERO
		private set

	var daysInStreak: Int = daysInStreak
		private set

	var lastManualTradeAt: LocalDate? = lastManualTradeAt
		private set

	var lastStreakStartedAt: Instant? = null
		private set

	@Enumerated(EnumType.STRING)
	var streakState: StreakState = StreakState.NOT_STARTED
		private set

	var donutsGainedCumulative: BigDecimal = BigDecimal.ZERO
		private set

	var tradedAmountInDay: BigDecimal = BigDecimal.ZERO
		private set

	fun updateDonuts(donuts: BigDecimal) {
		this.leaderboardDonuts += donuts
		this.leagueDonuts += donuts
		this.donutsGainedCumulative += donuts
	}

	fun updateTradedAmount(tradeUsdAmount: BigDecimal) {
		this.tradedAmountInDay += tradeUsdAmount
		this.lastManualTradeAt = LocalDate.now()
		this.leaderboardTradedAmount += tradeUsdAmount
	}

	fun startStreak() {
		this.streakState = StreakState.IN_PROGRESS
		this.daysInStreak++
		this.lastStreakStartedAt = Instant.now()
	}

	fun resetStreak() {
		if (this.streakState != StreakState.FAILED) {
			throw UserStreakCannotBeResetException("Streak must be in failed state to be reset.")
		}

		this.streakState = StreakState.NOT_STARTED
		this.lastStreakStartedAt = null
	}
}

@Repository
interface UserStatisticsRepository : JpaRepository<UserStatistics, UUID> {
	fun findByUserId(userId: UUID): UserStatistics?

	@Modifying
	@Query("UPDATE UserStatistics u SET u.leaderboardDonuts = 0.0, u.leaderboardTradedAmount = 0.0")
	fun resetLeaderboardForAll()

	@Modifying
	@Query("UPDATE UserStatistics u SET u.leagueDonuts = 0.0")
	fun resetLeagueDonutsForAll()

	@Modifying
	@Query("UPDATE UserStatistics u SET u.tradedAmountInDay = 0.0")
	fun resetTradedAmountInDayForAll()

	@Modifying
	@Query(
		"""
		UPDATE UserStatistics u
			SET
				u.streakState = CASE
					WHEN u.lastManualTradeAt = :date THEN 'IN_PROGRESS'
					WHEN u.lastStreakStartedAt IS NOT NULL AND u.lastManualTradeAt != :date THEN 'FAILED'
					ELSE 'NOT_STARTED'
				END,
				u.daysInStreak = CASE
					WHEN u.lastManualTradeAt = :date AND u.daysInStreak != 1 THEN u.daysInStreak + 1
					WHEN u.lastManualTradeAt = :date AND u.daysInStreak = 1 THEN u.daysInStreak
					ELSE 0
				END
		""",
	)
	fun updateDaysInStreakForAll(date: LocalDate)
}
