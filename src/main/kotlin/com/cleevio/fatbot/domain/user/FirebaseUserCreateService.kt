package com.cleevio.fatbot.domain.user

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class FirebaseUserCreateService(private val firebaseUserRepository: FirebaseUserRepository) {
	fun create(email: String, referredByUserId: UUID?, selectedChains: Set<Chain>, language: Language): FirebaseUser {
		return firebaseUserRepository.save(
			FirebaseUser(
				email = email,
				referralCode = null,
				referredByUserId = referredByUserId,
				selectedChains = selectedChains,
				language = language,
			),
		)
	}
}
