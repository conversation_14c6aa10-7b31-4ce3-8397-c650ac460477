package com.cleevio.fatbot.domain.user

import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.module.user.exception.IllegalQuickBuyAmount
import com.cleevio.fatbot.application.module.user.exception.ReferralCodeAlreadySetException
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.BaseDomainEntity
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.util.UUID

@Entity
class FirebaseUser(
	id: UUID = UUIDv7(),
	email: String,
	referralCode: String?,
	referredByUserId: UUID?,
	quickBuyAmountUsd: BigDecimal = BigDecimal.TEN,
	selectedChains: Set<Chain>,
	language: Language,
) : BaseDomainEntity(id) {

	var email = email.lowercase()
		private set

	var referralCode = referralCode?.lowercase()
		private set

	var referredByUserId = referredByUserId
		private set

	var quickBuyAmountUsd = quickBuyAmountUsd
		private set

	@Enumerated(EnumType.STRING)
	@JdbcTypeCode(SqlTypes.ARRAY)
	@Column(columnDefinition = "text[]")
	var selectedChains = selectedChains
		private set

	@Enumerated(EnumType.STRING)
	var language = language
		private set

	fun setQuickBuyAmount(amountInUsd: BigDecimal) {
		if (amountInUsd <= BigDecimal.ZERO) {
			throw IllegalQuickBuyAmount("Value must be positive.")
		}

		quickBuyAmountUsd = amountInUsd
	}

	fun setNewReferralCode(referralCode: String) {
		if (this.referralCode != null) {
			throw ReferralCodeAlreadySetException("Referral code is already set and cannot be updated.")
		}

		this.referralCode = referralCode.lowercase()
	}

	fun setSelectedChains(selectedChains: Set<Chain>) {
		this.selectedChains = selectedChains
	}

	fun setLanguage(language: Language) {
		this.language = language
	}
}

enum class Language {
	ENGLISH,
	CZECH,
	RUSSIAN,
	SPANISH,
	PORTUGUESE,
	TURKISH,
	FRENCH,
	KOREAN,
}

@Repository
interface FirebaseUserRepository : JpaRepository<FirebaseUser, UUID> {
	fun findByEmail(email: String): FirebaseUser?

	fun existsByEmail(email: String): Boolean

	fun findByReferralCode(referralCode: String): FirebaseUser?

	fun existsByReferralCode(referralCode: String): Boolean
}
