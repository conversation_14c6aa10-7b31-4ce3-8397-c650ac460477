package com.cleevio.fatbot.domain.bottransaction

import com.cleevio.fatbot.application.common.converter.AddressWrapperHibernateConverter
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asTxHash
import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransferCurrencyTransactionSuccess
import com.cleevio.fatbot.domain.BaseDomainEntity
import jakarta.persistence.Convert
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Duration
import java.time.Instant
import java.util.UUID

@Entity
class BotTransaction(
	id: UUID = UUIDv7(),
	val botWalletId: UUID,

	@Enumerated(EnumType.STRING)
	val type: BotTransactionType,

	// Null on deposit
	val nonce: Long?,

	// Null on deposit
	val signedTx: String?,

	val txHash: TxHash,

	// Null on deposit
	@Convert(converter = AddressWrapperHibernateConverter::class)
	val tokenAddress: AddressWrapper?,

	val baseValue: BigInteger,
	exchangeRateUsd: BigDecimal?,
	percentageOf: BigDecimal?,
) : BaseDomainEntity(id) {

	@Enumerated(EnumType.STRING)
	var status: TransactionStatus = TransactionStatus.PENDING
		private set

	var verificationCount: Int = 0
		private set

	var amountIn: BigInteger? = null
		private set

	var amountOut: BigInteger? = null
		private set

	var fatbotFee: BigInteger? = null
		private set

	var exchangeRateUsd: BigDecimal? = exchangeRateUsd
		private set

	var percentageOf: BigDecimal? = percentageOf
		private set

	companion object {
		fun ofSvm(
			id: UUID = UUIDv7(),
			botWalletId: UUID,
			type: BotTransactionType,
			tradeAmount: BigInteger,
			signedTx: SignedTx,
			tokenAddress: AddressWrapper?,
			signature: String,
			percentageOf: BigDecimal?,
			exchangeRateUsd: BigDecimal?,
		) = BotTransaction(
			id = id,
			botWalletId = botWalletId,
			type = type,
			nonce = null,
			signedTx = signedTx.signedTx,
			txHash = signature.asTxHash(),
			tokenAddress = tokenAddress,
			baseValue = tradeAmount,
			exchangeRateUsd = exchangeRateUsd,
			percentageOf = percentageOf,
		)
	}

	fun increaseTryCount(verificationLimit: Int, now: Instant, gracePeriod: Duration) {
		assertIsPending()

		this.verificationCount++
		if (this.verificationCount > verificationLimit) {
			// Even though we reach verification count, if the time between creation of tx,
			// and it's marking as not landed is too close, we might mark transaction as not landed
			// by accident, when in fact it might still land onto chain
			val unsafeToMarkAsNotLandedPeriod = this.createdAt..this.createdAt.plus(gracePeriod)
			if (now in unsafeToMarkAsNotLandedPeriod) {
				// do nothing for now, it's too soon to mark as NOT_LANDED
			} else {
				markAsNotLanded()
			}
		}
	}

	private fun markAsNotLanded() {
		assertIsPending()
		this.status = TransactionStatus.NOT_LANDED
	}

	fun markAsFailed(exchangeRateUsd: BigDecimal) {
		assertIsPending()

		this.status = TransactionStatus.FAILED
		this.exchangeRateUsd = exchangeRateUsd
	}

	private fun assertIsPending() {
		require(this.status == TransactionStatus.PENDING) {
			"Trying to update status of bot transaction other than ${TransactionStatus.PENDING}."
		}
	}

	fun markAsSuccess(result: BuySellTransactionSuccess, exchangeRateUsd: BigDecimal) {
		assertIsPending()

		this.status = TransactionStatus.SUCCESS
		this.amountIn = result.amountIn
		this.amountOut = result.amountOut
		this.fatbotFee = result.fee
		this.exchangeRateUsd = exchangeRateUsd
	}

	fun markAsTransferSuccess(result: TransferCurrencyTransactionSuccess, exchangeRateUsd: BigDecimal) {
		assertIsPending()

		this.status = TransactionStatus.SUCCESS
		this.exchangeRateUsd = exchangeRateUsd

		when (this.type) {
			BotTransactionType.DEPOSIT -> this.amountIn = result.value
			BotTransactionType.PORTFOLIO_WITHDRAW -> this.amountOut = result.value
			else -> error("Transfer is not allowed for this transaction type: ${this.type}")
		}
	}
}

@Repository
interface BotTransactionRepository : JpaRepository<BotTransaction, UUID> {
	fun findAllByStatusOrderByCreatedAt(status: TransactionStatus): List<BotTransaction>

	@Modifying
	@Query("DELETE FROM BotTransaction bt WHERE bt.botWalletId = :walletId")
	fun deleteAllByWalletId(walletId: UUID)
}
