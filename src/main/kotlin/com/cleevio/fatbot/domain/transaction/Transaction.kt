package com.cleevio.fatbot.domain.transaction

import com.cleevio.fatbot.application.common.converter.AddressWrapperHibernateConverter
import com.cleevio.fatbot.application.common.crypto.Lamport
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.Wei
import com.cleevio.fatbot.application.common.crypto.asTxHash
import com.cleevio.fatbot.application.common.crypto.hashToTxHash
import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailureReason
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.constant.TransferCurrencyTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransferTokenTransactionSuccess
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.BaseDomainEntity
import jakarta.persistence.Convert
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.math.BigInteger
import java.util.UUID

@Entity
class Transaction private constructor(
	id: UUID = UUIDv7(),
	val walletId: UUID,

	@Enumerated(EnumType.STRING)
	val type: TransactionType,
	val nonce: Long?,
	val baseValue: BigInteger,
	val signedTx: String?,

	@Convert(converter = AddressWrapperHibernateConverter::class)
	val tokenAddress: AddressWrapper?,

	@Enumerated(EnumType.STRING)
	val chain: Chain,

	/**
	 * A unique identifier of a transaction on the chain. The actual value depends on the specifics of the chain:
	 * EVM -> Tx Hash
	 * SVM -> Tx Signature
	 */
	val txHash: TxHash?,

	/**
	 * Indicated that [Transaction] was created by Limit order.
	 */
	val limitOrderId: UUID?,
) : BaseDomainEntity(id) {

	@Enumerated(EnumType.STRING)
	var status: TransactionStatus = TransactionStatus.PENDING
		private set

	var verificationCount: Int = 0
		private set

	var amountIn: BigInteger? = null
		private set

	var amountOut: BigInteger? = null
		private set

	var fatbotFee: BigInteger? = null
		private set

	var exchangeRateUsd: BigDecimal? = null
		private set

	@Enumerated(EnumType.STRING)
	var failReason: TransactionFailureReason? = null
		private set

	fun increaseTryCount(verificationLimit: Int) {
		assertIsPending()

		this.verificationCount++
		if (this.verificationCount > verificationLimit) {
			markAsFailed(
				failReason = TransactionFailureReason.RETRY_LIMIT_EXCEEDED,
			)
		}
	}

	fun markAsFailed(failReason: TransactionFailureReason) {
		assertIsPending()

		this.status = TransactionStatus.FAILED
		this.failReason = failReason
	}

	fun markAsSuccess(result: BuySellTransactionSuccess, exchangeRateUsd: BigDecimal) {
		assertIsPending()

		this.status = TransactionStatus.SUCCESS
		this.amountIn = result.amountIn
		this.amountOut = result.amountOut
		this.fatbotFee = result.fee
		this.exchangeRateUsd = exchangeRateUsd
	}

	fun markAsSuccess(result: TransferTokenTransactionSuccess, exchangeRateUsd: BigDecimal) {
		assertIsPending()

		this.status = TransactionStatus.SUCCESS
		this.amountIn = result.amountIn
		this.amountOut = result.amountOut
		this.exchangeRateUsd = exchangeRateUsd
	}

	fun markAsSuccess(transactionResult: TransferCurrencyTransactionSuccess, exchangeRateUsd: BigDecimal) {
		assertIsPending()

		this.status = TransactionStatus.SUCCESS
		this.amountOut = transactionResult.value
		this.exchangeRateUsd = exchangeRateUsd
	}

	fun markAsSuccess() {
		assertIsPending()

		this.status = TransactionStatus.SUCCESS
	}

	fun completeTrackingTransaction(tokenAmount: BigInteger, baseValue: BigInteger) {
		assertIsPending()

		this.status = TransactionStatus.SUCCESS

		when (this.type) {
			TransactionType.SELL -> {
				amountIn = tokenAmount
				amountOut = baseValue
			}
			TransactionType.BUY -> {
				amountOut = tokenAmount
				amountIn = baseValue
			}
			else -> error("Unsupported tracking transaction type $type")
		}
	}

	private fun assertIsPending() {
		require(this.status == TransactionStatus.PENDING) {
			"Trying to update status of transaction other than ${TransactionStatus.PENDING}."
		}
	}

	companion object {
		/**
		 * Factory method to construct a valid instance of Transaction on an EVM chain
		 */
		fun ofEvm(
			id: UUID = UUIDv7(),
			walletId: UUID,
			type: TransactionType,
			nonce: Long,
			baseValue: Wei,
			signedTx: SignedTx,
			tokenAddress: AddressWrapper?,
			chainId: Long,
			limitOrderId: UUID?,
		) = Transaction(
			id = id,
			chain = Chain.ofEVM(chainId),
			walletId = walletId,
			type = type,
			nonce = nonce,
			baseValue = baseValue.amount,
			signedTx = signedTx.signedTx,
			tokenAddress = tokenAddress,
			txHash = signedTx.hashToTxHash(),
			limitOrderId = limitOrderId,
		)

		/**
		 * Factory method to construct a valid instance of Transaction on an SVM chain
		 */
		fun ofSvm(
			id: UUID = UUIDv7(),
			walletId: UUID,
			type: TransactionType,
			baseValue: Lamport,
			signedTx: SignedTx,
			tokenAddress: AddressWrapper?,
			signature: String,
			limitOrderId: UUID?,
		) = Transaction(
			id = id,
			chain = Chain.SOLANA,
			walletId = walletId,
			type = type,
			nonce = null,
			baseValue = baseValue.amount,
			signedTx = signedTx.signedTx,
			tokenAddress = tokenAddress,
			txHash = signature.asTxHash(),
			limitOrderId = limitOrderId,
		)

		/**
		 * Factory method to construct a fake Transaction for tracking tokens and managing
		 * on-chain vs platform balance discrepancies
		 */
		fun ofTracking(
			id: UUID = UUIDv7(),
			walletId: UUID,
			type: TransactionType,
			tokenAddress: AddressWrapper,
			baseValue: BigInteger,
			chain: Chain,
		) = Transaction(
			id = id,
			walletId = walletId,
			type = type,
			nonce = null,
			baseValue = baseValue,
			signedTx = null,
			tokenAddress = tokenAddress,
			chain = chain,
			txHash = null,
			limitOrderId = null,
		)
	}
}

@Repository
interface TransactionRepository : JpaRepository<Transaction, UUID> {

	fun findAllByStatusAndSignedTxIsNotNull(status: TransactionStatus): List<Transaction>
	fun deleteAllByWalletId(walletId: UUID)
}
