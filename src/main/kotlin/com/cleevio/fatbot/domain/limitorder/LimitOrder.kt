package com.cleevio.fatbot.domain.limitorder

import com.cleevio.fatbot.application.common.converter.AddressWrapperHibernateConverter
import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.BaseDomainEntity
import jakarta.persistence.Convert
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.math.BigInteger
import java.util.UUID

enum class LimitOrderType { BUY, SELL }

@Entity
class LimitOrder(
	id: UUID = UUIDv7(),
	val userId: UUID,
	val walletId: UUID,

	@Convert(converter = AddressWrapperHibernateConverter::class)
	val tokenAddress: AddressWrapper,

	@Enumerated(EnumType.STRING)
	val chain: Chain,

	val limitPrice: BigInteger,

	@Enumerated(EnumType.STRING)
	val type: LimitOrderType,

	initialAmount: BigInteger,
) : BaseDomainEntity(id) {

	var remainingAmount: BigInteger = initialAmount
		private set

	var filledAmount: BigInteger = BigInteger.ZERO
		private set

	var isLocked: Boolean = false
		private set

	fun lock() {
		check(!isLocked) { "Cannot lock already locked Limit order" }

		isLocked = true
	}

	fun unlock() {
		check(isLocked) { "Cannot unlock already unlocked Limit order" }

		isLocked = false
	}

	fun fill(amount: BigInteger) {
		check(isLocked) { "Cannot fill a LimitOrder while it is not locked!" }
		check(amount <= remainingAmount) {
			"Cannot fill LimitOrder with more that its remaining amount! Amount: $amount, Remaining: $remainingAmount"
		}

		filledAmount += amount
		remainingAmount -= amount
		isLocked = false
	}
}

@Repository
interface LimitOrderRepository : JpaRepository<LimitOrder, UUID> {
	fun findByIdAndUserId(id: UUID, userId: UUID): LimitOrder?
}
