package com.cleevio.fatbot.domain.market

import org.springframework.stereotype.Service
import java.time.Instant
import java.util.UUID

@Service
class MarketPositionRefreshCreateService(
	private val marketPositionRefreshRepository: MarketPositionRefreshRepository,
) {

	fun create(userId: UUID, initialLastViewedAt: Instant) = marketPositionRefreshRepository.save(
		MarketPositionRefresh(
			userId = userId,
			initialLastViewedAt = initialLastViewedAt,
		),
	)
}
