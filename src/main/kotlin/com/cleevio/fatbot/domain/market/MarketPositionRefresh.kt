package com.cleevio.fatbot.domain.market

import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.domain.BaseDomainEntity
import jakarta.persistence.Entity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

private val MARKET_REFRESH_THRESHOLD = Duration.of(60, ChronoUnit.SECONDS)
private val WALLET_REFRESH_THRESHOLD = Duration.of(20, ChronoUnit.SECONDS)

@Entity
class MarketPositionRefresh(
	id: UUID = UUIDv7(),
	val userId: UUID,
	initialLastViewedAt: Instant,
) : BaseDomainEntity(id) {

	var marketLastViewedAt: Instant = initialLastViewedAt
		private set

	var walletLastViewedAt: Instant = initialLastViewedAt
		private set

	fun marketViewed(now: Instant) {
		this.marketLastViewedAt = now
	}

	fun walletViewed(now: Instant) {
		this.walletLastViewedAt = now
	}

	fun isMarketStale(now: Instant): Boolean {
		val freshRange = now.minus(MARKET_REFRESH_THRESHOLD)..now
		return marketLastViewedAt !in freshRange
	}

	fun isWalletStale(now: Instant): Boolean {
		val freshRange = now.minus(WALLET_REFRESH_THRESHOLD)..now
		return walletLastViewedAt !in freshRange
	}
}

@Repository
interface MarketPositionRefreshRepository : JpaRepository<MarketPositionRefresh, UUID> {
	fun findByUserId(userId: UUID): MarketPositionRefresh?
}
