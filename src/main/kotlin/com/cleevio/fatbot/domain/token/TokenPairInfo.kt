package com.cleevio.fatbot.domain.token

import com.cleevio.fatbot.adapter.out.evm.context.UniswapV3Fee
import com.cleevio.fatbot.application.common.converter.AddressWrapperHibernateConverter
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.BaseDomainEntity
import jakarta.persistence.Column
import jakarta.persistence.Convert
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.math.BigInteger
import java.util.UUID

data class RaydiumAmmMarketData(
	val baseVault: AddressWrapper,
	val quoteVault: AddressWrapper,

	val baseMint: AddressWrapper,
	val quoteMint: AddressWrapper,
) {
	init {
		require(quoteMint == SolanaConstants.WSOL_MINT_ADDRESS || baseMint == SolanaConstants.WSOL_MINT_ADDRESS) {
			"Either of quoteMint and baseMint needs to be a WSOL mint! Was quote: $quoteMint, base: $baseMint"
		}
	}

	fun wsolVault(): AddressWrapper = if (quoteMint == SolanaConstants.WSOL_MINT_ADDRESS) quoteVault else baseVault

	fun tokenVault(): AddressWrapper = if (quoteMint == SolanaConstants.WSOL_MINT_ADDRESS) baseVault else quoteVault
}

data class RaydiumCPMMMarketData(
	val ammConfig: AddressWrapper,
	// Uses 1_000_000 as base. I.e. 2500 -> 0.25% (25 basis points)
	val tradeFeeRate: Long,
)

fun RaydiumCPMMMarketData.getTradeFeeBp() = BasisPoint.of(tradeFeeRate.toInt() / 100)

@Entity
class TokenPairInfo(
	id: UUID = UUIDv7(),
	@Enumerated(EnumType.STRING)
	val chain: Chain,

	@Enumerated(EnumType.STRING)
	val dexType: GetDex.Dex,

	@Enumerated(EnumType.STRING)
	@Column(name = "uniswap_v3_fee")
	val uniswapV3Fee: UniswapV3Fee?,

	@Enumerated(EnumType.STRING)
	val raydiumPoolType: GetDex.PoolType?,

	@Convert(converter = AddressWrapperHibernateConverter::class)
	val tokenAddress: AddressWrapper,
	@Convert(converter = AddressWrapperHibernateConverter::class)
	val pairAddress: AddressWrapper,

	val tokenDecimals: BigInteger,

	@JdbcTypeCode(SqlTypes.JSON)
	@Column(columnDefinition = "jsonb")
	val raydiumAmmMarketData: RaydiumAmmMarketData?,

	@JdbcTypeCode(SqlTypes.JSON)
	@Column(columnDefinition = "jsonb")
	val raydiumCpmmMarketData: RaydiumCPMMMarketData?,

	/**
	 * Relevant for [GetDex.Dex.PUMP_FUN] and [GetDex.Dex.PUMP_SWAP]
	 */
	@Convert(converter = AddressWrapperHibernateConverter::class)
	val creatorAddress: AddressWrapper?,

) : BaseDomainEntity(id) {

	fun getDexPairInfo(): GetDex.Result {
		return when (dexType) {
			GetDex.Dex.UNISWAP_V2 -> GetDex.UniswapV2(pairAddress)
			GetDex.Dex.UNISWAP_V3 -> GetDex.UniswapV3(pairAddress, uniswapV3Fee!!)
			GetDex.Dex.PANCAKESWAP_V2 -> GetDex.PancakeswapV2(pairAddress)
			GetDex.Dex.PANCAKESWAP_V3 -> GetDex.PancakeswapV3(pairAddress, uniswapV3Fee!!)
			GetDex.Dex.PUMP_FUN -> GetDex.PumpFun(pairAddress)
			GetDex.Dex.PUMP_SWAP -> GetDex.PumpSwap(pairAddress)
			GetDex.Dex.RAYDIUM -> GetDex.Raydium(pairAddress, raydiumPoolType!!)
			GetDex.Dex.METEORA -> GetDex.Meteora(pairAddress)
		}
	}
}

@Repository
interface TokenPairInfoRepository : JpaRepository<TokenPairInfo, UUID> {
	fun findByPairAddressAndChain(pairAddress: AddressWrapper, chain: Chain): TokenPairInfo?

	fun findByTokenAddressAndChain(tokenAddress: AddressWrapper, chain: Chain): TokenPairInfo?

	fun findByTokenAddressAndChainAndDexType(
		tokenAddress: AddressWrapper,
		chain: Chain,
		dexType: GetDex.Dex,
	): TokenPairInfo?

	fun findAllByTokenAddressAndChain(tokenAddress: AddressWrapper, chain: Chain): List<TokenPairInfo>

	fun findAllByTokenAddressInAndChain(tokenAddresses: Set<AddressWrapper>, chain: Chain): List<TokenPairInfo>
}
