package com.cleevio.fatbot.domain.bot

import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.Duration
import java.util.UUID

@Service
class BotCreateService(
	private val botRepository: BotRepository,
) {
	fun create(
		userId: UUID,
		isActive: <PERSON><PERSON><PERSON>,
		name: String,
		avatarFileId: UUID,
		tradeAmount: BigDecimal,
		buyFrequency: Long,
		profitTargetFraction: BigDecimal,
		stopLossFraction: BigDecimal,
		marketCapFromUsd: BigDecimal?,
		marketCapToUsd: BigDecimal?,
		liquidityFromUsd: BigDecimal?,
		liquidityToUsd: BigDecimal?,
		dailyVolumeFromUsd: BigDecimal?,
		dailyVolumeToUsd: BigDecimal?,
		numberOfHoldersFrom: Long?,
		numberOfHoldersTo: Long?,
		buyVolume: BigDecimal?,
		sellVolume: BigDecimal?,
		sellTransactionFraction: BigDecimal?,
		buyTransactionFraction: BigDecimal?,
		tokenTickerCopyIsChecked: <PERSON><PERSON><PERSON>,
		creatorHighBuyIsChecked: <PERSON>olean,
		bundledBuysDetectedIsChecked: Boolean,
		suspiciousWalletsDetectedIsChecked: Boolean,
		singleHighBuyIsChecked: Boolean,
		buyTokensAliveAtLeastFor: Duration?,
		shouldAutoSellAfterHoldTime: Boolean,
	) = botRepository.save(
		Bot(
			userId = userId,
			isActive = isActive,
			name = name,
			avatarFileId = avatarFileId,
			tradeAmount = tradeAmount,
			buyFrequency = buyFrequency,
			profitTargetFraction = profitTargetFraction,
			stopLossFraction = stopLossFraction,
			marketCapFromUsd = marketCapFromUsd,
			marketCapToUsd = marketCapToUsd,
			liquidityFromUsd = liquidityFromUsd,
			liquidityToUsd = liquidityToUsd,
			dailyVolumeFromUsd = dailyVolumeFromUsd,
			dailyVolumeToUsd = dailyVolumeToUsd,
			numberOfHoldersFrom = numberOfHoldersFrom,
			numberOfHoldersTo = numberOfHoldersTo,
			buyVolume = buyVolume,
			sellVolume = sellVolume,
			sellTransactionFraction = sellTransactionFraction,
			buyTransactionFraction = buyTransactionFraction,
			tokenTickerCopyIsChecked = tokenTickerCopyIsChecked,
			creatorHighBuyIsChecked = creatorHighBuyIsChecked,
			bundledBuysDetectedIsChecked = bundledBuysDetectedIsChecked,
			suspiciousWalletsDetectedIsChecked = suspiciousWalletsDetectedIsChecked,
			singleHighBuyIsChecked = singleHighBuyIsChecked,
			buyTokensAliveAtLeastFor = buyTokensAliveAtLeastFor,
			shouldAutoSellAfterHoldTime = shouldAutoSellAfterHoldTime,
		),
	)
}
