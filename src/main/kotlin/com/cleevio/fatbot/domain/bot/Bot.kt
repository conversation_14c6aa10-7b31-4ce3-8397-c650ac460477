package com.cleevio.fatbot.domain.bot

import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.module.bot.exception.InvalidBotStateTransitionException
import com.cleevio.fatbot.application.module.bot.model.BotUpdate
import com.cleevio.fatbot.domain.BaseDomainEntity
import jakarta.persistence.Entity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.math.MathContext
import java.time.Duration
import java.time.Instant
import java.util.UUID

@Entity
class Bot(
	id: UUID = UUIDv7(),
	val userId: UUID,
	isActive: Boolean,
	name: String,
	avatarFileId: UUID,
	tradeAmount: BigDecimal,
	buyFrequency: Long,
	profitTargetFraction: BigDecimal,
	stopLossFraction: BigDecimal,
	marketCapFromUsd: BigDecimal?,
	marketCapToUsd: BigDecimal?,
	liquidityFromUsd: BigDecimal?,
	liquidityToUsd: BigDecimal?,
	dailyVolumeFromUsd: BigDecimal?,
	dailyVolumeToUsd: BigDecimal?,
	numberOfHoldersFrom: Long?,
	numberOfHoldersTo: Long?,
	buyVolume: BigDecimal?,
	sellVolume: BigDecimal?,
	sellTransactionFraction: BigDecimal?,
	buyTransactionFraction: BigDecimal?,
	tokenTickerCopyIsChecked: Boolean,
	creatorHighBuyIsChecked: Boolean,
	bundledBuysDetectedIsChecked: Boolean,
	suspiciousWalletsDetectedIsChecked: Boolean,
	singleHighBuyIsChecked: Boolean,
	buyTokensAliveAtLeastFor: Duration?,
	shouldAutoSellAfterHoldTime: Boolean,
) : BaseDomainEntity(id) {

	var isActive: Boolean = isActive
		private set

	var numberOfActiveDays: Long = 1
		private set

	var name: String = name
		private set

	var avatarFileId: UUID = avatarFileId
		private set

	var tradeAmount: BigDecimal = tradeAmount
		private set

	// Max number of buy trades per day
	var buyFrequency: Long = buyFrequency
		private set

	var profitTargetFraction: BigDecimal = profitTargetFraction
		private set

	var stopLossFraction: BigDecimal = stopLossFraction
		private set

	var marketCapFromUsd: BigDecimal? = marketCapFromUsd
		private set

	var marketCapToUsd: BigDecimal? = marketCapToUsd
		private set

	var liquidityFromUsd: BigDecimal? = liquidityFromUsd
		private set

	var liquidityToUsd: BigDecimal? = liquidityToUsd
		private set

	var dailyVolumeFromUsd: BigDecimal? = dailyVolumeFromUsd
		private set

	var dailyVolumeToUsd: BigDecimal? = dailyVolumeToUsd
		private set

	var numberOfHoldersFrom: Long? = numberOfHoldersFrom
		private set

	var numberOfHoldersTo: Long? = numberOfHoldersTo
		private set

	var buyVolume: BigDecimal? = buyVolume
		private set

	var sellVolume: BigDecimal? = sellVolume
		private set

	var remainingBuyFrequency: Long = buyFrequency
		private set

	val hasZeroRemainingBuyFrequency: Boolean
		get() = remainingBuyFrequency == 0L

	var buyFrequencyLastResetAt: Instant = createdAt
		private set

	var buyTokensAliveAtLeastFor: Duration? = buyTokensAliveAtLeastFor
		private set

	var sellTransactionFraction: BigDecimal? = sellTransactionFraction
		private set

	var buyTransactionFraction: BigDecimal? = buyTransactionFraction
		private set

	// TODO: Experimental feature, only set via DB for now
	var creatorGraduationSuccessRateFraction: BigDecimal? = null
		private set

	var sellToBuyTransactionRatio: BigDecimal? = null
		private set

	var tokenTickerCopyIsChecked: Boolean = tokenTickerCopyIsChecked
		private set

	var creatorHighBuyIsChecked: Boolean = creatorHighBuyIsChecked
		private set

	var bundledBuysDetectedIsChecked: Boolean = bundledBuysDetectedIsChecked
		private set

	var suspiciousWalletsDetectedIsChecked: Boolean = suspiciousWalletsDetectedIsChecked
		private set

	var singleHighBuyIsChecked: Boolean = singleHighBuyIsChecked
		private set

	var shouldAutoSellAfterHoldTime: Boolean = shouldAutoSellAfterHoldTime
		private set

	/**
	 * ID of bot that is following this bot and copying the trades of this bot.
	 */
	var followerBotId: UUID? = null
		private set

	fun update(botUpdate: BotUpdate) {
		this.name = botUpdate.name
		this.avatarFileId = botUpdate.avatarFileId
		this.tradeAmount = botUpdate.tradeAmount
		this.buyFrequency = botUpdate.buyFrequency
		this.remainingBuyFrequency = botUpdate.remainingBuyFrequency

		this.profitTargetFraction = botUpdate.profitTargetFraction
		this.stopLossFraction = botUpdate.stopLossFraction

		this.tokenTickerCopyIsChecked = botUpdate.tokenTickerCopyIsChecked
		this.creatorHighBuyIsChecked = botUpdate.creatorHighBuyIsChecked
		this.bundledBuysDetectedIsChecked = botUpdate.bundledBuysDetectedIsChecked
		this.suspiciousWalletsDetectedIsChecked = botUpdate.suspiciousWalletsDetectedIsChecked
		this.singleHighBuyIsChecked = botUpdate.singleHighBuyIsChecked
		this.buyTokensAliveAtLeastFor = botUpdate.buyTokensAliveAtLeastFor
		this.shouldAutoSellAfterHoldTime = botUpdate.shouldAutoSellAfterHoldTime

		this.marketCapFromUsd = botUpdate.marketCapFromUsd
		this.marketCapToUsd = botUpdate.marketCapToUsd
		this.liquidityFromUsd = botUpdate.liquidityFromUsd
		this.liquidityToUsd = botUpdate.liquidityToUsd
		this.dailyVolumeFromUsd = botUpdate.dailyVolumeFromUsd
		this.dailyVolumeToUsd = botUpdate.dailyVolumeToUsd
		this.numberOfHoldersFrom = botUpdate.numberOfHoldersFrom
		this.numberOfHoldersTo = botUpdate.numberOfHoldersTo
		this.buyVolume = botUpdate.buyVolume
		this.sellVolume = botUpdate.sellVolume

		this.sellTransactionFraction = botUpdate.sellTransactionFraction
		this.buyTransactionFraction = botUpdate.buyTransactionFraction

		require(
			(sellTransactionFraction == null && buyTransactionFraction == null) ||
				(sellTransactionFraction != null && buyTransactionFraction != null),
		)

		recalculateSellToBuyTransactionRatio()
	}

	fun recalculateSellToBuyTransactionRatio() {
		this.sellToBuyTransactionRatio = when (buyTransactionFraction?.compareTo(BigDecimal.ZERO)) {
			// set to null, so we also set ratio back to null
			null -> null
			// when buyTransactionFraction is zero, that means user is asking that there should be 100% of sell tx
			// so essentially no token should ever be bought. We will simulate this by setting the ratio to super high
			// number, that is not realistically achievable (there would have to be 1000 sell tx to each buy tx)
			0 -> BigDecimal("1000")
			else -> sellTransactionFraction!!.divide(buyTransactionFraction!!, MathContext.DECIMAL128)
		}
	}

	// This is only used for follower bots and will be removed in the future
	fun updateRedFlagSettings(
		tokenTickerCopyIsChecked: Boolean,
		creatorHighBuyIsChecked: Boolean,
		bundledBuysDetectedIsChecked: Boolean,
		suspiciousWalletsDetectedIsChecked: Boolean,
		singleHighBuyIsChecked: Boolean,
		buyTokensAliveAtLeastFor: Duration?,
		shouldAutoSellAfterHoldTime: Boolean,
	) {
		this.tokenTickerCopyIsChecked = tokenTickerCopyIsChecked
		this.creatorHighBuyIsChecked = creatorHighBuyIsChecked
		this.bundledBuysDetectedIsChecked = bundledBuysDetectedIsChecked
		this.suspiciousWalletsDetectedIsChecked = suspiciousWalletsDetectedIsChecked
		this.singleHighBuyIsChecked = singleHighBuyIsChecked
		this.buyTokensAliveAtLeastFor = buyTokensAliveAtLeastFor
		this.shouldAutoSellAfterHoldTime = shouldAutoSellAfterHoldTime
	}

	fun updateState(newState: Boolean) {
		if (isActive == newState) {
			throw InvalidBotStateTransitionException(
				"Cannot transition bot to ${if (newState) "active" else "inactive"} state - bot is already in this state",
			)
		}
		isActive = newState
	}

	fun userReadableBotId() = id.toString().replace("-", "") + "fatbot"

	fun decreaseRemainingBuys() {
		this.remainingBuyFrequency--
	}

	fun resetRemainingBuyFrequency() {
		this.remainingBuyFrequency = buyFrequency
		this.buyFrequencyLastResetAt = Instant.now()
	}
}

@Repository
interface BotRepository : JpaRepository<Bot, UUID> {
	fun findAllByUserId(userId: UUID): List<Bot>
	fun findAllByUserIdAndIsActiveTrue(userId: UUID): List<Bot>
	fun findByIdAndUserId(id: UUID, userId: UUID): Bot?
}

const val MAX_ACTIVE_BOTS_PER_USER = 3
const val MAX_BOTS_PER_USER = 15
