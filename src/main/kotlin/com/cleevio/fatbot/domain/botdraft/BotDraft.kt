package com.cleevio.fatbot.domain.botdraft

import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.module.botdraft.model.BotDraftUpdate
import com.cleevio.fatbot.domain.BaseDomainEntity
import jakarta.persistence.Entity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.time.Duration
import java.util.UUID

/**
 * All currency-amount specifying values are to be in their base form, i.e., Wei for Ether.
 */
@Entity
class BotDraft(id: UUID = UUIDv7(), val userId: UUID) : BaseDomainEntity(id) {

	var name: String? = null
		private set

	var avatarFileId: UUID? = null
		private set

	var tradeAmount: BigDecimal? = null
		private set

	// Max number of buy trades per day
	var buyFrequency: Long? = null
		private set

	var profitTargetFraction: BigDecimal? = null
		private set

	var stopLossFraction: BigDecimal? = null
		private set

	var marketCapFromUsd: BigDecimal? = null
		private set

	var marketCapToUsd: BigDecimal? = null
		private set

	var liquidityFromUsd: BigDecimal? = null
		private set

	var liquidityToUsd: BigDecimal? = null
		private set

	var dailyVolumeFromUsd: BigDecimal? = null
		private set

	var dailyVolumeToUsd: BigDecimal? = null
		private set

	var numberOfHoldersFrom: Long? = null
		private set

	var numberOfHoldersTo: Long? = null
		private set

	var buyVolume: BigDecimal? = null
		private set

	var sellVolume: BigDecimal? = null
		private set

	var sellTransactionFraction: BigDecimal? = null
		private set

	var buyTransactionFraction: BigDecimal? = null
		private set

	var buyTokensAliveAtLeastFor: Duration? = null
		private set

	var tokenTickerCopyIsChecked: Boolean = false
		private set

	var creatorHighBuyIsChecked: Boolean = false
		private set

	var bundledBuysDetectedIsChecked: Boolean = false
		private set

	var suspiciousWalletsDetectedIsChecked: Boolean = false
		private set

	var singleHighBuyIsChecked: Boolean = false
		private set

	var shouldAutoSellAfterHoldTime: Boolean = false
		private set

	fun update(botUpdate: BotDraftUpdate) {
		this.name = botUpdate.name
		this.avatarFileId = botUpdate.avatarFileId
		this.tradeAmount = botUpdate.tradeAmount
		this.buyFrequency = botUpdate.buyFrequency
		this.profitTargetFraction = botUpdate.profitTargetFraction
		this.stopLossFraction = botUpdate.stopLossFraction

		this.tokenTickerCopyIsChecked = botUpdate.tokenTickerCopyIsChecked
		this.creatorHighBuyIsChecked = botUpdate.creatorHighBuyIsChecked
		this.bundledBuysDetectedIsChecked = botUpdate.bundledBuysDetectedIsChecked
		this.suspiciousWalletsDetectedIsChecked = botUpdate.suspiciousWalletsDetectedIsChecked
		this.singleHighBuyIsChecked = botUpdate.singleHighBuyIsChecked
		this.buyTokensAliveAtLeastFor = botUpdate.buyTokensAliveAtLeastFor
		this.shouldAutoSellAfterHoldTime = botUpdate.shouldAutoSellAfterHoldTime

		this.marketCapFromUsd = botUpdate.marketCapFromUsd
		this.marketCapToUsd = botUpdate.marketCapToUsd
		this.liquidityFromUsd = botUpdate.liquidityFromUsd
		this.liquidityToUsd = botUpdate.liquidityToUsd
		this.dailyVolumeFromUsd = botUpdate.dailyVolumeFromUsd
		this.dailyVolumeToUsd = botUpdate.dailyVolumeToUsd
		this.numberOfHoldersFrom = botUpdate.numberOfHoldersFrom
		this.numberOfHoldersTo = botUpdate.numberOfHoldersTo
		this.buyVolume = botUpdate.buyVolume
		this.sellVolume = botUpdate.sellVolume
		this.sellTransactionFraction = botUpdate.sellTransactionFraction
		this.buyTransactionFraction = botUpdate.buyTransactionFraction
	}
}

@Repository
interface BotDraftRepository : JpaRepository<BotDraft, UUID> {

	fun findAllByUserId(userId: UUID): List<BotDraft>

	fun findByIdAndUserId(id: UUID, userId: UUID): BotDraft?
}
