package com.cleevio.fatbot.adapter.`in`.rest

import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.adapter.`in`.SwaggerBearerToken
import com.cleevio.fatbot.adapter.`in`.rest.dto.SearchBotMarketPositionRequest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.botmarket.query.GetBotMarketPositionPriceChartQuery
import com.cleevio.fatbot.application.module.botmarket.query.GetBotMarketPositionQuery
import com.cleevio.fatbot.application.module.botmarket.query.SearchBotMarketPositionQuery
import com.cleevio.fatbot.application.module.matchmaking.command.ForceSellBotMarketPositionCommand
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Bot Market Position")
@RestController
@SwaggerBearerToken
@RequestMapping("/bots/{botId}/market-positions")
class BotMarketPositionController(
	private val queryBus: QueryBus,
	private val commandBus: CommandBus,
) {

	@Operation(
		description = """
            Search for bot market positions with various filters.
            Returns paginated results of bot's market positions with their current values and changes.
        """,
	)
	@PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
	fun searchBotMarketPositions(
		@AuthenticationPrincipal userId: UUID,
		@PathVariable botId: UUID,
		@ParameterObject infiniteScroll: InfiniteScrollDesc.UUID,
		@RequestBody request: SearchBotMarketPositionRequest,
	): InfiniteScrollSlice<SearchBotMarketPositionQuery.Result, UUID> = queryBus(
		request.toQuery(userId = userId, botId = botId, infiniteScroll = infiniteScroll),
	)

	@Operation(
		description = """
            Forces immediate sell of open bot market position.
        """,
	)
	@PostMapping("/{botMarketPositionId}/force-sell", produces = [ApiVersion.VERSION_1_JSON])
	fun forceSellMarketPositions(
		@AuthenticationPrincipal userId: UUID,
		@PathVariable botMarketPositionId: UUID,
		@PathVariable botId: UUID,
	): Unit = commandBus(
		ForceSellBotMarketPositionCommand(
			userId = userId,
			botId = botId,
			botMarketPositionId = botMarketPositionId,
		),
	)

	@GetMapping("/{botMarketPositionId}", produces = [ApiVersion.VERSION_1_JSON])
	fun getBotMarketPosition(
		@AuthenticationPrincipal userId: UUID,
		@PathVariable botId: UUID,
		@PathVariable botMarketPositionId: UUID,
	) = queryBus(GetBotMarketPositionQuery(userId = userId, botId = botId, botMarketPositionId = botMarketPositionId))

	@GetMapping("/{botMarketPositionId}/price-chart", produces = [ApiVersion.VERSION_1_JSON])
	fun getBotMarketPositionPriceChart(
		@AuthenticationPrincipal userId: UUID,
		@PathVariable botId: UUID,
		@PathVariable botMarketPositionId: UUID,
	) = queryBus(
		GetBotMarketPositionPriceChartQuery(
			userId = userId,
			botId = botId,
			botMarketPositionId = botMarketPositionId,
		),
	)
}
