package com.cleevio.fatbot.adapter.`in`.rest

import com.cleevio.fatbot.adapter.`in`.SwaggerBearerToken
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.user.command.UpdateQuickBuyAmountCommand
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.math.BigDecimal
import java.util.UUID

@Tag(name = "User Settings")
@RestController
@SwaggerBearerToken
@RequestMapping("/users/me/settings")
class UserSettingsController(
	private val commandBus: CommandBus,
) {

	@PatchMapping("/quick-buy")
	fun setQuickBuyAmount(
		@AuthenticationPrincipal userId: UUID,
		@RequestParam(name = "quickBuyAmountUsd") quickBuyAmountUsd: BigDecimal,
	): Unit = commandBus(UpdateQuickBuyAmountCommand(userId, quickBuyAmountUsd))
}
