package com.cleevio.fatbot.adapter.`in`.rest

import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.adapter.`in`.SwaggerBearerToken
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.userstatistics.command.ResetUserStreakCommand
import com.cleevio.fatbot.application.module.userstatistics.query.GetUserStreakGeneralInfoQuery
import com.cleevio.fatbot.application.module.userstatistics.query.GetUserStreakQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "User Statistics")
@RestController
@SwaggerBearerToken
@RequestMapping("/users/me/statistics")
class UserStatisticsController(
	private val queryBus: QueryBus,
	private val commandBus: CommandBus,
) {

	@GetMapping("/streak", produces = [ApiVersion.VERSION_1_JSON])
	fun getUserStreak(@AuthenticationPrincipal userId: UUID) = queryBus(GetUserStreakQuery(userId))

	@GetMapping("/streak/info", produces = [ApiVersion.VERSION_1_JSON])
	fun getUserStreakGeneralInfo(@AuthenticationPrincipal userId: UUID) = queryBus(GetUserStreakGeneralInfoQuery(userId))

	@PostMapping("/reset-streak", produces = [ApiVersion.VERSION_1_JSON])
	fun resetUserStreak(@AuthenticationPrincipal userId: UUID) = commandBus(ResetUserStreakCommand(userId = userId))
}
