package com.cleevio.fatbot.adapter.`in`

/**
 * This class describes API version related constants.
 *
 * The structure of this class is strictly defined, including constant names, their types, accessors, modifiers, etc.
 * All of that is guarded by a unit test, which fails on invalid definition.
 */
object ApiVersion {
	/**
	 * Defines the Application namespace. This constant exists so that it can be used across all possible application
	 * versions, giving one the ability to change it in one place and thus reflecting the change everywhere.
	 */
	private const val APP_NAMESPACE = "fatbot"

	/**
	 * The version values are to be used by the [RequestMapping.produces] (e.g. [GetMapping]
	 * and/or [PutMapping], etc.) attribute, implementing a content negotiation mechanism on a Spring level.
	 *
	 * Based on semantic versioning, on API level we're only interested in major version, 1, 2, not 1.1, 2.0,
	 * or 1.0.0. This is because hotfixes are to be deployed without client's knowledge anyway, backwards compatible
	 * changes may be simply added to the current version, and backward incompatible changes require new version.
	 *
	 * The structure of the value must be: application/APP_NAMESPACE-v{version}+{dataFormat}.
	 *
	 * The name of the constant must correlate to its value, i.e., where the field's name basically determines the
	 * version and data format to use. E.g. in order to represent v1 for xml data type, the field must be named
	 * VERSION_1_XML, which should ultimately lead to application/APP_NAMESPACE-v1+xml value. This correlation
	 * is validated by a unit test.
	 *
	 * Feel free to add other necessary versions directly beneath this one.
	 */
	const val VERSION_1_JSON = "application/$APP_NAMESPACE-v1+json"

	const val VERSION_1_OCTET_STREAM = "application/$APP_NAMESPACE-v1+octet-stream"

	/**
	 * A set of all supported versions (= all public static fields of type String, with the exclusion of the
	 * constant holding the application namespace). This set needs to hold all the values, and at the same time all
	 * values present within this set must be defined as versions through public static constants.
	 */
	@JvmField
	val SUPPORTED_API_VERSIONS = setOf(VERSION_1_JSON, VERSION_1_OCTET_STREAM)
}
