package com.cleevio.fatbot.adapter.`in`.aggregator.dto

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.constant.SolanaTokenRedFlag
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.math.BigDecimal

@JsonIgnoreProperties(ignoreUnknown = true)
data class TokenRedFlaggedRequest(
	val blockSlot: Int,
	val tokenAddress: AddressWrapper,
	val creator: AddressWrapper,
	val type: SolanaTokenRedFlag,
	val marketCapUsd: BigDecimal,
	val liquidityUsd: BigDecimal,
	val volumeUsd: BigDecimal,
	val numOfAccountHolders: Long,
	val buyVolume: BigDecimal,
	val sellVolume: BigDecimal,
	val fractionOfSellTransactions: BigDecimal,
)
