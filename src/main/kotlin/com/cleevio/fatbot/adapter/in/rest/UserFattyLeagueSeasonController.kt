package com.cleevio.fatbot.adapter.`in`.rest

import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.adapter.`in`.SwaggerBearerToken
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.userfattyleagueseason.command.ClaimUserFattyTokensCommand
import com.cleevio.fatbot.application.module.userfattyleagueseason.query.GetUserFattyLeagueSeasonsQuery
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "User Fatty League Season")
@RestController
@SwaggerBearerToken
@RequestMapping("/users/me/fatty-league-seasons")
class UserFattyLeagueSeasonController(
	private val queryBus: QueryBus,
	private val commandBus: CommandBus,
) {

	@Operation(description = "Get all user fatty league seasons.")
	@GetMapping(produces = [ApiVersion.VERSION_1_JSON])
	fun getUserFattyLeagueSeasons(@AuthenticationPrincipal userId: UUID): GetUserFattyLeagueSeasonsQuery.Result =
		queryBus(GetUserFattyLeagueSeasonsQuery(userId = userId))

	@Operation(description = "Claim user fatty tokens.")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping("/{userFattyLeagueId}/claim", produces = [ApiVersion.VERSION_1_JSON])
	fun claimFattyTokens(@AuthenticationPrincipal userId: UUID, @PathVariable userFattyLeagueId: UUID): Unit =
		commandBus(ClaimUserFattyTokensCommand(userId = userId, userFattyLeagueId = userFattyLeagueId))
}
