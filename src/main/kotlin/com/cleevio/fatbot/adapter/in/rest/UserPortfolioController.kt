package com.cleevio.fatbot.adapter.`in`.rest

import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.adapter.`in`.SwaggerBearerToken
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.portfolio.query.GetPortfolioValuesQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "User Portfolio")
@RestController
@SwaggerBearerToken
@RequestMapping("/users/me/portfolio")
class UserPortfolioController(
	private val queryBus: QueryBus,
) {

	@GetMapping("/overview", produces = [ApiVersion.VERSION_1_JSON])
	fun getPortfolioOverview(@AuthenticationPrincipal userId: UUID) = queryBus(GetPortfolioValuesQuery(userId = userId))
}
