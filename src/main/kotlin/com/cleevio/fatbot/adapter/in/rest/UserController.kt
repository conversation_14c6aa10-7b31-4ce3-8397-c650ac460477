package com.cleevio.fatbot.adapter.`in`.rest

import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.adapter.`in`.SwaggerBearerToken
import com.cleevio.fatbot.adapter.`in`.rest.dto.UpdateLanguageRequest
import com.cleevio.fatbot.adapter.`in`.rest.dto.UpdateSelectedChainsRequest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.user.command.SignUpFirebaseUserCommand
import com.cleevio.fatbot.application.module.user.command.UpdateLanguageCommand
import com.cleevio.fatbot.application.module.user.command.UpdateSelectedChainsCommand
import com.cleevio.fatbot.application.module.user.query.GetUserQuery
import com.cleevio.fatbot.domain.user.Language
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Sign-Up")
@RestController
@SwaggerBearerToken
@RequestMapping("/users")
class UserController(
	private val commandBus: CommandBus,
	private val queryBus: QueryBus,
) {
	@Operation(
		description = """
			Sign new user with email into database.
			Then after calling sign-up, it's possible to call all other endpoints by providing Firebase JWT token.
			Multiple invocations are possible, and do not have side effects.
		""",
	)
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping("/sign-up", produces = [ApiVersion.VERSION_1_JSON])
	fun signUpUser(
		@AuthenticationPrincipal email: String,
		@RequestParam(name = "referralCode", required = false) referralCode: String? = null,
		@RequestParam(name = "language", required = false) language: Language? = null,
	): Unit = commandBus(SignUpFirebaseUserCommand(email = email, referralCode = referralCode, language = language))

	@GetMapping("/me", produces = [ApiVersion.VERSION_1_JSON])
	fun getUser(@AuthenticationPrincipal userId: UUID): GetUserQuery.Result = queryBus(GetUserQuery(userId = userId))

	@PutMapping("/me/selected-chains", produces = [ApiVersion.VERSION_1_JSON])
	fun selectChains(@AuthenticationPrincipal userId: UUID, @RequestBody request: UpdateSelectedChainsRequest) =
		commandBus(UpdateSelectedChainsCommand(userId = userId, selectedChains = request.selectedChains))

	@PutMapping("/me/language", produces = [ApiVersion.VERSION_1_JSON])
	fun updateLanguage(@AuthenticationPrincipal userId: UUID, @RequestBody request: UpdateLanguageRequest) =
		commandBus(UpdateLanguageCommand(userId = userId, language = request.language))
}
