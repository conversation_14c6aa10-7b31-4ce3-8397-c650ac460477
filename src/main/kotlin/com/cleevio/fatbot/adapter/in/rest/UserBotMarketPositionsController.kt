package com.cleevio.fatbot.adapter.`in`.rest

import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.adapter.`in`.SwaggerBearerToken
import com.cleevio.fatbot.adapter.`in`.rest.dto.SearchUserBotMarketPositionsRequest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.botmarket.query.SearchBotMarketPositionQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "User Bot Market Position")
@RestController
@SwaggerBearerToken
@RequestMapping("/users/me/bots/market-positions")
class UserBotMarketPositionsController(
	private val queryBus: QueryBus,
) {

	@PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
	fun searchUserBotMarketPositions(
		@AuthenticationPrincipal userId: UUID,
		@ParameterObject infiniteScroll: InfiniteScrollDesc.UUID,
		@RequestBody request: SearchUserBotMarketPositionsRequest,
	): InfiniteScrollSlice<SearchBotMarketPositionQuery.Result, UUID> = queryBus(
		SearchBotMarketPositionQuery(
			userId = userId,
			infiniteScroll = infiniteScroll,
			filter = SearchBotMarketPositionQuery.Filter(
				botIds = request.botId?.let { setOf(it) },
				searchString = request.searchString,
				marketPositionType = request.botMarketPositionType
					?: SearchBotMarketPositionQuery.MarketPositionType.ALL,
			),
		),
	)
}
