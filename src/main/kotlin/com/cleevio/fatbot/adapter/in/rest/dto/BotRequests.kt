package com.cleevio.fatbot.adapter.`in`.rest.dto

import com.cleevio.fatbot.adapter.`in`.InfiniteScroll
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bot.command.ResetBotBuyFrequencyCommand
import com.cleevio.fatbot.application.module.bot.command.WithdrawFromBotPortfolioCommand
import com.cleevio.fatbot.application.module.bot.constant.BotSortParameter
import com.cleevio.fatbot.application.module.bot.query.BotsOverviewQuery
import com.cleevio.fatbot.application.module.bot.query.CompareBotsQuery
import com.cleevio.fatbot.application.module.bot.query.GetBotWithdrawGasEstimationQuery
import com.cleevio.fatbot.application.module.bot.query.SearchUserBotDetailQuery
import com.cleevio.fatbot.application.module.botleaderboard.query.BotsLeaderboardSearchQuery
import com.cleevio.fatbot.application.module.botmarket.query.SearchBotMarketPositionQuery
import com.cleevio.fatbot.application.module.bottransaction.query.SearchUserBotsTransactionsQuery
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.math.BigInteger
import java.util.Optional
import java.util.UUID

/**
 * The Goal of this class is to allow a Patch request for:
 *
 * 1. Non-nullable field -> can be updated to a new value or not updated
 * 2. Nullable field -> can be updated to a new nullable value or not updated
 *
 * Ideally, we would use Kotlin's idiomatic `value class`, but that is not yet well-supported by `Swagger`.
 * Hence, we chose [Optional] + Kotlin's nullability to represent the three states we need.
 *
 * - Null property -> No update
 * - [Optional.isEmpty] -> Update to null
 * - [Optional.isPresent] -> Update to [Optional.value]
 *
 * The reason for this assignment is with `@JsonIgnoreProperties(ignoreUnknown = true)` we are able to receive `null`
 * values when they are undefined in the request payload and [Optional.empty] when they are defined as a `null`.
 *
 * TODO: Since OpenAPI type generation into TS loses nullable / not-required information we annotate Non-nullable fields
 * TODO: This is not a ideal solution, which will require wrapping Nullable fields in schema-visible object as to generate the following:
 * {
 *   nonNullableField?: { value: ValueType }
 *   nullableField?: { nullableValue?: ValueType }
 * }
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class PatchBotSettingsRequest(
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val name: String? = null,
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val avatarFileId: UUID? = null,
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val tradeAmount: BigDecimal? = null,
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val buyFrequency: BigInteger? = null,
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val profitTargetFraction: BigDecimal? = null,
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val stopLossFraction: BigDecimal? = null,
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val tokenTickerCopyIsChecked: Boolean? = null,
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val creatorHighBuyIsChecked: Boolean? = null,
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val bundledBuysDetectedIsChecked: Boolean? = null,
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val suspiciousWalletsDetectedIsChecked: Boolean? = null,
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val singleHighBuyIsChecked: Boolean? = null,
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val shouldWaitBeforeBuying: Boolean? = null,
	@Schema(description = "Non-Nullable - `null` indicates no update")
	val shouldAutoSellAfterHoldTime: Boolean? = null,

	@Schema(nullable = true)
	val marketCapFromUsd: Optional<BigDecimal>? = null,
	@Schema(nullable = true)
	val marketCapToUsd: Optional<BigDecimal>? = null,

	@Schema(nullable = true)
	val liquidityFromUsd: Optional<BigDecimal>? = null,
	@Schema(nullable = true)
	val liquidityToUsd: Optional<BigDecimal>? = null,

	@Schema(nullable = true)
	val dailyVolumeFromUsd: Optional<BigDecimal>? = null,
	@Schema(nullable = true)
	val dailyVolumeToUsd: Optional<BigDecimal>? = null,

	@Schema(nullable = true)
	val numberOfHoldersFrom: Optional<BigInteger>? = null,
	@Schema(nullable = true)
	val numberOfHoldersTo: Optional<BigInteger>? = null,

	@Schema(nullable = true)
	val buyVolume: Optional<BigDecimal>? = null,
	@Schema(nullable = true)
	val sellVolume: Optional<BigDecimal>? = null,

	@Schema(nullable = true)
	val sellTransactionFraction: Optional<BigDecimal>? = null,
	@Schema(nullable = true)
	val buyTransactionFraction: Optional<BigDecimal>? = null,
)

data class BotsLeaderboardSearchRequest(
	val searchString: String?,
	val allBots: Boolean,
	val timeRange: TimeRange,
	val sortParameter: BotSortParameter,
) {
	fun toQuery(userId: UUID?, infiniteScroll: InfiniteScrollDesc<BigDecimal>) = BotsLeaderboardSearchQuery(
		userId = userId,
		filter = BotsLeaderboardSearchQuery.Filter(
			timeRange = timeRange,
			searchString = searchString,
			allBots = allBots,
			sortParameter = sortParameter,
		),
		infiniteScroll = infiniteScroll,
	)
}

data class SearchUserBotsTransactionsRequest(
	val botId: UUID,
	val searchString: String?,
) {
	fun toQuery(userId: UUID, infiniteScroll: InfiniteScroll<UUID>) = SearchUserBotsTransactionsQuery(
		userId = userId,
		filter = SearchUserBotsTransactionsQuery.Filter(
			botId = botId,
			searchString = searchString,
		),
		infiniteScroll = infiniteScroll,
	)
}

data class CompareBotsRequest(
	val botIds: Set<UUID>,
	val timeRange: TimeRange,
) {
	fun toQuery(userId: UUID) = CompareBotsQuery(
		userId = userId,
		filter = CompareBotsQuery.Filter(
			botIds = botIds,
			timeRange = timeRange,
		),
	)
}

data class ResetBotBuyFrequencyRequest(
	val botId: UUID,
) {
	fun toCommand(userId: UUID) = ResetBotBuyFrequencyCommand(
		userId = userId,
		botId = botId,
	)
}

data class SearchUserBotDetailRequest(
	val botId: UUID,
	val timeRange: TimeRange = TimeRange.DAY, // TODO: Tmp fix, probably can be deleted
) {
	fun toQuery(userId: UUID) = SearchUserBotDetailQuery(
		userId = userId,
		filter = SearchUserBotDetailQuery.Filter(
			botId = botId,
			timeRange = timeRange,
		),
	)
}

data class SearchBotMarketPositionRequest(
	val timeRange: TimeRange,
	val searchString: String?,
	val isBotMarketPositionActive: Boolean,
	val marketPositionType: SearchBotMarketPositionQuery.MarketPositionType?,
) {

	fun toQuery(userId: UUID, botId: UUID, infiniteScroll: InfiniteScrollDesc.UUID) = SearchBotMarketPositionQuery(
		userId = userId,
		filter = SearchBotMarketPositionQuery.Filter(
			botIds = setOf(botId),
			isBotMarketPositionActive = isBotMarketPositionActive,
			timeRange = timeRange,
			searchString = searchString,
			marketPositionType = marketPositionType ?: SearchBotMarketPositionQuery.MarketPositionType.ALL,
		),
		infiniteScroll = infiniteScroll,
	)
}

data class BotsOverviewRequest(
	val timeRange: TimeRange,
) {
	fun toQuery(userId: UUID) = BotsOverviewQuery(
		userId = userId,
		filter = BotsOverviewQuery.Filter(
			timeRange = timeRange,
		),
	)
}

data class WithdrawFromBotPortfolioRequest(
	val nativeAmount: BigDecimal,
	val percentageOf: BigDecimal,
	val recipientAddress: AddressWrapper,
) {
	fun toCommand(userId: UUID, botId: UUID) = WithdrawFromBotPortfolioCommand(
		userId = userId,
		botId = botId,
		nativeAmount = nativeAmount,
		percentageOf = percentageOf,
		recipientAddress = recipientAddress,
	)
}

data class GetBotWithdrawGasEstimationRequest(
	val nativeAmount: BigDecimal,
	val recipientAddress: AddressWrapper,
) {
	fun toQuery(userId: UUID, botId: UUID) = GetBotWithdrawGasEstimationQuery(
		userId = userId,
		botId = botId,
		nativeAmount = nativeAmount,
		recipientAddress = recipientAddress,
	)
}
