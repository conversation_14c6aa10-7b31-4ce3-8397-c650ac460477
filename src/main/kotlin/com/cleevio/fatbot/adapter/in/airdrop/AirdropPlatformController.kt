package com.cleevio.fatbot.adapter.`in`.airdrop

import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.adapter.`in`.SwaggerInternalApiKey
import com.cleevio.fatbot.adapter.`in`.airdrop.dto.ConnectAirdropPlatformAccountRequest
import com.cleevio.fatbot.application.common.query.QueryBus
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@Tag(name = "Airdrop Platform")
@RestController
@SwaggerInternalApiKey
@RequestMapping("/integration/airdrop-platform")
class AirdropPlatformController(
	private val queryBus: QueryBus,
) {
	@PostMapping("/connect-account", produces = [ApiVersion.VERSION_1_JSON])
	fun connectAirdropPlatformAccount(@RequestBody request: ConnectAirdropPlatformAccountRequest) =
		queryBus(request.toQuery())
}
