package com.cleevio.fatbot.adapter.`in`.rest.dto

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.botmarket.query.SearchBotMarketPositionQuery
import com.cleevio.fatbot.application.module.market.command.BuyTokenCommand
import com.cleevio.fatbot.application.module.market.command.QuickBuyTokenCommand
import com.cleevio.fatbot.application.module.market.command.SellTokenCommand
import com.cleevio.fatbot.application.module.market.command.TransferCurrencyCommand
import com.cleevio.fatbot.application.module.market.command.TransferTokenCommand
import com.cleevio.fatbot.application.module.market.port.out.toDexPairInfo
import com.cleevio.fatbot.application.module.token.query.GetTokenMaxBuyQuery
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.user.command.CreateNewReferralCodeCommand
import com.cleevio.fatbot.application.module.wallet.command.CreateNewUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.command.ImportUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.command.PatchUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.query.GetAllUserWalletsQuery
import com.cleevio.fatbot.domain.user.Language
import java.util.UUID

data class GetAllUserWalletsRequestV2(
	val chains: Set<Chain>,
) {
	fun toQuery(userId: UUID) = GetAllUserWalletsQuery(
		userId = userId,
		useSelectedChains = false,
	)
}

data class CreateNewUserWalletRequestV2(
	val chain: Chain,
) {
	fun toCommand(userId: UUID) = CreateNewUserWalletCommand(
		userId = userId,
		chain = chain,
	)
}

data class ImportUserWalletRequestV2(
	val privateKey: String,
	val chain: Chain,
) {
	fun toCommand(userId: UUID) = ImportUserWalletCommand(
		userId = userId,
		privateKey = privateKey,
		chain = chain,
	)
}

data class BuyTokenRequestV2(
	val tokenAddress: AddressWrapper,
	val buyForNativeAmount: NativeAmount,
	val dexPairInfo: DexPairInfoInputV2,
) {
	fun toCommand(userId: UUID, walletId: UUID) = BuyTokenCommand(
		userId = userId,
		walletId = walletId,
		tokenAddress = tokenAddress,
		buyForNativeAmount = buyForNativeAmount,
		dexPairInfo = dexPairInfo.toDexPairInfo(),
	)
}

data class SellTokenRequestV2(
	val tokenAddress: AddressWrapper,
	val toSellNativeAmount: NativeAmount,
	val dexPairInfo: DexPairInfoInputV2,
) {
	fun toCommand(userId: UUID, walletId: UUID) = SellTokenCommand(
		userId = userId,
		walletId = walletId,
		tokenAddress = tokenAddress,
		toSellNativeAmount = toSellNativeAmount,
		dexPairInfo = dexPairInfo.toDexPairInfo(),
	)
}

data class QuickBuyTokenRequestV2(
	val chain: Chain,
) {
	fun toCommand(userId: UUID, tokenAddress: AddressWrapper) = QuickBuyTokenCommand(
		userId = userId,
		tokenAddress = tokenAddress,
		chain = chain,
	)
}

data class PatchUserWalletRequest(
	val customName: String?,
	val buyAntiMevProtection: Boolean?,
	val sellAntiMevProtection: Boolean?,
) {
	fun toCommand(userId: UUID, walletId: UUID) = PatchUserWalletCommand(
		userId = userId,
		walletId = walletId,
		customName = customName,
		buyAntiMevProtection = buyAntiMevProtection,
		sellAntiMevProtection = sellAntiMevProtection,
	)
}

data class SearchUserTokensTransactionRequestV2(
	val chains: Set<Chain>,
	val walletId: UUID?,
	val searchString: String?,
	val allowedStatuses: Set<TransactionStatus>,
)

data class SearchUserTokensTransactionRequestV3(
	val walletId: UUID?,
	val searchString: String?,
	val useSelectedChains: Boolean,
	val allowedStatuses: Set<TransactionStatus>,
)

data class SearchUserTokenTransactionsRequestV2(
	val chain: Chain,
	val walletId: UUID?,
	val allowedStatuses: Set<TransactionStatus>,
)

data class SearchUserTokenRequestV2(
	val chains: Set<Chain>,
	val walletId: UUID?,
	val searchString: String?,
)

data class SearchUserTokenRequestV3(
	val useSelectedChains: Boolean,
	val walletId: UUID?,
	val searchString: String?,
)

data class SearchUserBotMarketPositionsRequest(
	val botId: UUID?,
	val searchString: String?,
	val botMarketPositionType: SearchBotMarketPositionQuery.MarketPositionType?,
)

data class CreateNewReferralCodeRequest(
	val referralCode: String,
) {
	fun toCommand(userId: UUID) = CreateNewReferralCodeCommand(userId = userId, referralCode = referralCode)
}

data class TransferTokenRequestV2(
	val tokenAddress: AddressWrapper,
	val destinationWalletAddress: AddressWrapper,
	val toTransferNativeAmount: NativeAmount,
	val password: String,
) {
	fun toCommand(userId: UUID, walletId: UUID) = TransferTokenCommand(
		userId = userId,
		walletId = walletId,
		tokenAddress = tokenAddress,
		destinationWalletAddress = destinationWalletAddress,
		nativeAmount = toTransferNativeAmount,
		password = password,
	)
}

data class TransferCurrencyRequestV2(
	val destinationWalletAddress: AddressWrapper,
	val toTransferNativeAmount: NativeAmount,
	val password: String,
) {
	fun toCommand(userId: UUID, walletId: UUID) = TransferCurrencyCommand(
		userId = userId,
		walletId = walletId,
		destinationWalletAddress = destinationWalletAddress,
		toTransferNativeAmount = toTransferNativeAmount,
		password = password,
	)
}

data class GetMaxBuyRequestV2(
	val tokenAddress: AddressWrapper,
	val dexPairInfo: DexPairInfoInputV2,
) {
	fun toQuery(userId: UUID, walletId: UUID) = GetTokenMaxBuyQuery(
		userId = userId,
		walletId = walletId,
		tokenAddress = tokenAddress,
		dexPairInfo = dexPairInfo.toDexPairInfo(),
	)
}

data class UpdateSelectedChainsRequest(
	val selectedChains: Set<Chain>,
)

data class UpdateLanguageRequest(
	val language: Language,
)
