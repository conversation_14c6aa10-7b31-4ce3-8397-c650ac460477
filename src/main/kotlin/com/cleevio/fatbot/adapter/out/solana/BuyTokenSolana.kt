package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.adapter.out.solana.client.FatbotTsProxyConnector
import com.cleevio.fatbot.adapter.out.solana.pumpfun.util.getPumpfunCreatorVaultPDA
import com.cleevio.fatbot.adapter.out.solana.pumpfun.util.getPumpswapCoinCreatorVaultAuthority
import com.cleevio.fatbot.adapter.out.solana.raydium.util.getCLMMPoolBitmapExtensionAccountPda
import com.cleevio.fatbot.adapter.out.solana.raydium.util.getCLMMPoolVaultPda
import com.cleevio.fatbot.adapter.out.solana.raydium.util.getCPMMObservationPda
import com.cleevio.fatbot.adapter.out.solana.raydium.util.getCPMMPoolVaultPda
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.ASSOCIATED_TOKEN_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.CU_LIMIT_BUY_PUMPFUN
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.CU_LIMIT_BUY_PUMPSWAP
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.CU_LIMIT_BUY_RAYDIUM
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.FATBOT_ROUTER_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.JITO_MIN_TIP
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.JITO_TIP_1_ACCOUNT
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.MEMO_RPGORAM
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.PUMPFUN_EVENT_AUTHORITY
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.PUMPFUN_FEE_ACCOUNT
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.PUMPFUN_GLOBAL
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.PUMPFUN_PROGRAM
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.PUMPSWAP_EVENT_AUTHORITY
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.PUMPSWAP_FEE_ACCOUNT
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.PUMPSWAP_GLOBAL_CONFIG
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.PUMPSWAP_PROGRAM
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.RAYDIUM_AMM_AUTHORITY
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.RAYDIUM_AMM_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.RAYDIUM_CLMM_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.RAYDIUM_CPMM_AUTHORITY
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.RAYDIUM_CPMM_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.SYSTEM_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.TOKEN_2022_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.TOKEN_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.WSOL_MINT
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.WSOL_MINT_ADDRESS
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.minusBasisPoints
import com.cleevio.fatbot.application.common.crypto.toSignedTx
import com.cleevio.fatbot.application.common.util.getAssociatedTokenAddress
import com.cleevio.fatbot.application.common.util.getFunctionDiscriminator
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.port.out.BuyToken
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutRaydium
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.referral.ReferralRewardService.Companion.ZERO_UUID
import com.cleevio.fatbot.application.module.token.port.out.GetTokenTransferFee
import com.cleevio.fatbot.application.module.transaction.fee.FeeDetailsProvider
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.token.RaydiumAmmMarketData
import com.cleevio.fatbot.domain.token.RaydiumCPMMMarketData
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import com.syntifi.near.borshj.BorshBuffer
import org.bitcoinj.core.Base58
import org.p2p.solanaj.core.Account
import org.p2p.solanaj.core.AccountMeta
import org.p2p.solanaj.core.LegacyTransaction
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.core.TransactionInstruction
import org.p2p.solanaj.programs.ComputeBudgetProgram
import org.p2p.solanaj.programs.SystemProgram
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component
import java.util.UUID

private const val BUY_PUMPFUN_FUNCTION_NAME = "global:buy_pumpfun"
private const val BUY_PUMPSWAP_FUNCTION_NAME = "global:buy_pumpswap"
private const val BUY_RAYDIUM_AMM_FUNCTION_NAME = "global:buy_raydium_amm"
private const val BUY_RAYDIUM_CLMM_FUNCTION_NAME = "global:buy_raydium_clmm"
private const val BUY_RAYDIUM_CPMM_FUNCTION_NAME = "global:buy_raydium_cpmm"

private val SLIPPAGE_BP = BasisPoint.ofPercentage(2.0)

/**
 * For pools where we manually calculate the minAmountOut (we don't pass the slippage to the program)
 * we add a slippage buffer to account for the possible shift due to the extra time this takes to reach the swap.
 */
private val MANUAL_MIN_AMOUNT_OUT_SLIPPAGE_BUFFER = BasisPoint.ofPercentage(2.0)

@Component
class BuyTokenSolana(
	private val feeDetailsProvider: FeeDetailsProvider,
	private val rpcClient: RpcClient,
	@Qualifier("stakedRpcClient") private val stakedRpcClient: RpcClient,
	@Qualifier("jitoRpcClient") private val jitoRpcClient: RpcClient,
	private val fatbotTsProxyConnector: FatbotTsProxyConnector,
	private val getAmountOutRaydium: GetAmountOutRaydium,
	private val getRecommendedFeeSolana: GetRecommendedFeeSolana,
	private val getLatestBlockHashSolana: GetLatestBlockHashSolana,
	private val getTokenTransferFee: GetTokenTransferFee,
	chainProperties: ChainProperties,
) : BuyToken<BuyTokenSolana.SolanaInput> {

	private val treasuryWallet = PublicKey(chainProperties.svm.solana.treasuryWallet.getAddressString())
	private val referralWallet = Account(Base58.decode(chainProperties.svm.solana.referralWalletPrivateKey)).publicKey

	data class SolanaInput(
		val userId: UUID,
		val tokenAddress: AddressWrapper,
		val isToken2022: Boolean,
		val dexPairInfo: GetDex.Result,
		val privateKey: String,
		val buyForLamportAmount: BaseAmount,
		val getRaydiumAMMMarketData: () -> RaydiumAmmMarketData,
		val getRaydiumCPMMMarketData: () -> RaydiumCPMMMarketData,
		val getPumpfunCreator: () -> AddressWrapper,
		val getPumpswapCoinCreator: () -> AddressWrapper,
		val useStakedEndpoint: Boolean,
		val useMevProtection: Boolean,
	)

	override fun invoke(input: SolanaInput): SignedTx {
		val feeDetails = feeDetailsProvider.getFeeDetails(input.userId, input.tokenAddress.toChainAddress(Chain.SOLANA))
		val userAccount = Account(Base58.decode(input.privateKey))

		val targetTokenProgramId = if (input.isToken2022) TOKEN_2022_PROGRAM_ID else TOKEN_PROGRAM_ID
		val associatedTokenAccount = getAssociatedTokenAddress(
			mint = input.tokenAddress.getSolanaPublicKey(),
			owner = userAccount.publicKey,
			programId = targetTokenProgramId,
		)

		val signedTx = when (input.dexPairInfo) {
			is GetDex.PumpFun -> buyPumpfun(
				userAccount = userAccount,
				tokenAddress = input.tokenAddress,
				associatedTokenAccount = associatedTokenAccount,
				bondingCurve = input.dexPairInfo.pairAddress,
				creatorAddress = input.getPumpfunCreator(),
				amount = input.buyForLamportAmount.amount.asBaseAmount(),
				feeBp = feeDetails.platformFeeBps,
				referralFeeBp = feeDetails.referrerFeeBps,
				referralUUID = feeDetails.referrerId ?: ZERO_UUID,
				useStakedEndpoint = input.useStakedEndpoint,
				useMevProtection = input.useMevProtection,
			)

			is GetDex.Raydium -> when (input.dexPairInfo.poolType) {
				GetDex.PoolType.AMM -> {
					val marketData = input.getRaydiumAMMMarketData()

					buyRaydiumAMM(
						userAccount = userAccount,
						tokenAddress = input.tokenAddress,
						associatedTokenAccount = associatedTokenAccount,
						targetTokenProgramId = targetTokenProgramId,
						pairMarketAddress = input.dexPairInfo.pairAddress,
						amount = input.buyForLamportAmount,
						marketData = marketData,
						feeBp = feeDetails.platformFeeBps,
						referralFeeBp = feeDetails.referrerFeeBps,
						referralUUID = feeDetails.referrerId ?: ZERO_UUID,
						useMevProtection = input.useMevProtection,
					)
				}

				GetDex.PoolType.CLMM -> buyRaydiumCLMM(
					userAccount = userAccount,
					tokenAddress = input.tokenAddress,
					associatedTokenAccount = associatedTokenAccount,
					targetTokenProgramId = targetTokenProgramId,
					pairAddress = input.dexPairInfo.pairAddress,
					amount = input.buyForLamportAmount,
					feeBp = feeDetails.platformFeeBps,
					referralFeeBp = feeDetails.referrerFeeBps,
					referralUUID = feeDetails.referrerId ?: ZERO_UUID,
					useMevProtection = input.useMevProtection,
				)

				GetDex.PoolType.CPMM -> {
					val marketData = input.getRaydiumCPMMMarketData()

					buyRaydiumCPMM(
						userAccount = userAccount,
						tokenAddress = input.tokenAddress,
						associatedTokenAccount = associatedTokenAccount,
						targetTokenProgramId = targetTokenProgramId,
						pairAddress = input.dexPairInfo.pairAddress,
						marketData = marketData,
						amount = input.buyForLamportAmount,
						feeBp = feeDetails.platformFeeBps,
						referralFeeBp = feeDetails.referrerFeeBps,
						referralUUID = feeDetails.referrerId ?: ZERO_UUID,
						useMevProtection = input.useMevProtection,
					)
				}
			}

			is GetDex.PumpSwap -> buyPumpswap(
				userAccount = userAccount,
				tokenAddress = input.tokenAddress,
				associatedTokenAccount = associatedTokenAccount,
				targetTokenProgramId = targetTokenProgramId,
				pairAddress = input.dexPairInfo.pairAddress,
				coinCreatorAddress = input.getPumpswapCoinCreator(),
				amount = input.buyForLamportAmount.amount.asBaseAmount(),
				feeBp = feeDetails.platformFeeBps,
				referralFeeBp = feeDetails.referrerFeeBps,
				referralUUID = feeDetails.referrerId ?: ZERO_UUID,
				useStakedEndpoint = input.useStakedEndpoint,
				useMevProtection = input.useMevProtection,
			)

			is GetDex.Meteora -> error("Buy not supported for ${input.dexPairInfo.dex}")
			is GetDex.UniswapV2, is GetDex.UniswapV3, is GetDex.PancakeswapV2, is GetDex.PancakeswapV3 ->
				error("Illegal EVM dex ${input.dexPairInfo} in BuyTokenSolana")
		}

		return signedTx
	}

	fun buyPumpfun(
		userAccount: Account,
		bondingCurve: AddressWrapper,
		creatorAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		associatedTokenAccount: PublicKey,
		amount: BaseAmount,
		feeBp: BasisPoint,
		referralFeeBp: BasisPoint,
		referralUUID: UUID,
		useStakedEndpoint: Boolean,
		useMevProtection: Boolean,
	): SignedTx {
		val discriminator = getFunctionDiscriminator(BUY_PUMPFUN_FUNCTION_NAME)
		// Discriminator (8) + 4 * U64 (8) + UUID size U32 (4) + UUID (36)
		val instructionData = BorshBuffer.allocate(80).apply {
			write(discriminator)
			writeU64(amount.amount.toLong()) // amount
			writeU64(SLIPPAGE_BP.toLong()) // slippage_bp
			writeU64(feeBp.toLong()) // Fee
			writeU64(referralFeeBp.toLong()) // referral_fee_bp
			writeString(referralUUID.toString()) // uuid
		}.toByteArray()

		val associatedBondingCurve = getAssociatedTokenAddress(
			mint = tokenAddress.getSolanaPublicKey(),
			owner = bondingCurve.getSolanaPublicKey(),
		)

		val creatorVault = getPumpfunCreatorVaultPDA(creator = creatorAddress)

		val accounts = listOf(
			AccountMeta(userAccount.publicKey, true, true),
			AccountMeta(bondingCurve.getSolanaPublicKey(), false, true),
			AccountMeta(associatedBondingCurve, false, true),
			AccountMeta(tokenAddress.getSolanaPublicKey(), false, false),
			AccountMeta(associatedTokenAccount, false, true),
			AccountMeta(PUMPFUN_FEE_ACCOUNT, false, true),
			AccountMeta(PUMPFUN_GLOBAL, false, false),
			AccountMeta(PUMPFUN_EVENT_AUTHORITY, false, false),
			AccountMeta(treasuryWallet, false, true),
			AccountMeta(referralWallet, false, true),
			AccountMeta(PUMPFUN_PROGRAM, false, false),
			AccountMeta(creatorVault, false, true),
			AccountMeta(SYSTEM_PROGRAM_ID, false, false),
			AccountMeta(ASSOCIATED_TOKEN_PROGRAM_ID, false, false),
			AccountMeta(TOKEN_PROGRAM_ID, false, false),
		)

		val transactionInstruction = TransactionInstruction(
			FATBOT_ROUTER_PROGRAM_ID,
			accounts,
			instructionData,
		)

		val currentRecommendedFee = getRecommendedFeeSolana().let {
			// When using staked endpoint, we increase the recommended fee
			// in order to speed up buys as much as possible
			if (useStakedEndpoint) (it.toLong() * SolanaConstants.STAKED_ENDPOINT_BUY_CU_PRICE_MULTIPLIER).toInt() else it
		}

		val culInstruction = ComputeBudgetProgram.setComputeUnitLimit(CU_LIMIT_BUY_PUMPFUN)
		val cupInstruction = ComputeBudgetProgram.setComputeUnitPrice(currentRecommendedFee)

		val transaction = LegacyTransaction().apply {
			addInstruction(culInstruction)
			addInstruction(cupInstruction)
			addInstruction(transactionInstruction)
		}

		sendTransaction(transaction, userAccount, useStakedEndpoint, useMevProtection)

		return transaction.toSignedTx()
	}

	private fun buyPumpswap(
		userAccount: Account,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		coinCreatorAddress: AddressWrapper,
		associatedTokenAccount: PublicKey,
		targetTokenProgramId: PublicKey,
		amount: BaseAmount,
		feeBp: BasisPoint,
		referralFeeBp: BasisPoint,
		referralUUID: UUID,
		useStakedEndpoint: Boolean,
		useMevProtection: Boolean,
	): SignedTx {
		val discriminator = getFunctionDiscriminator(BUY_PUMPSWAP_FUNCTION_NAME)
		// Discriminator (8) + 4 * U64 (8) + UUID size U32 (4) + UUID (36)
		val instructionData = BorshBuffer.allocate(80).apply {
			write(discriminator)
			writeU64(amount.amount.toLong()) // amount
			writeU64(SLIPPAGE_BP.toLong()) // slippage_bp
			writeU64(feeBp.toLong()) // Fee
			writeU64(referralFeeBp.toLong()) // referral_fee_bp
			writeString(referralUUID.toString()) // uuid
		}.toByteArray()

		val wsolTokenAccount = getAssociatedTokenAddress(
			mint = WSOL_MINT,
			owner = userAccount.publicKey,
		)

		val pool = pairAddress.getSolanaPublicKey()

		val wsolVault = getAssociatedTokenAddress(mint = WSOL_MINT, owner = pool)
		val targetTokenVault = getAssociatedTokenAddress(
			mint = tokenAddress.getSolanaPublicKey(),
			owner = pool,
			programId = targetTokenProgramId,
		)

		val targetTokenFeeAccount = getAssociatedTokenAddress(
			mint = WSOL_MINT,
			owner = PUMPSWAP_FEE_ACCOUNT,
			programId = targetTokenProgramId,
		)

		val creatorVaultAuthority = getPumpswapCoinCreatorVaultAuthority(coinCreator = coinCreatorAddress)

		val coinCreatorVaultTokenAccount = getAssociatedTokenAddress(
			mint = WSOL_MINT,
			owner = creatorVaultAuthority,
		)

		val accounts = listOf(
			AccountMeta(userAccount.publicKey, true, true), // User
			AccountMeta(pool, false, false), // Pool
			AccountMeta(PUMPSWAP_GLOBAL_CONFIG, false, false), // global_config

			AccountMeta(wsolTokenAccount, false, true), // wsol_token_account
			AccountMeta(WSOL_MINT, false, false), // wsol_mint
			AccountMeta(wsolVault, false, true), // wsol_vault

			AccountMeta(associatedTokenAccount, false, true), // target_token_account
			AccountMeta(tokenAddress.getSolanaPublicKey(), false, false), // target_token_mint
			AccountMeta(targetTokenVault, false, true), // target_token_vault

			AccountMeta(PUMPSWAP_FEE_ACCOUNT, false, false), // protocol_fee_recipient
			AccountMeta(targetTokenFeeAccount, false, true), // protocol_fee_recipient_token_account

			AccountMeta(treasuryWallet, false, true), // treasury_account
			AccountMeta(referralWallet, false, true), // referral_account
			AccountMeta(coinCreatorVaultTokenAccount, false, true), // coin_creator_vault_ata
			AccountMeta(creatorVaultAuthority, false, false), // coin_creator_vault_authority

			AccountMeta(PUMPSWAP_EVENT_AUTHORITY, false, false), // event_authority
			AccountMeta(PUMPSWAP_PROGRAM, false, false), // program
			AccountMeta(SYSTEM_PROGRAM_ID, false, false), // system_program
			AccountMeta(ASSOCIATED_TOKEN_PROGRAM_ID, false, false), // associated_token_program
			AccountMeta(targetTokenProgramId, false, false), // target_token_program
			AccountMeta(TOKEN_PROGRAM_ID, false, false), // token_program
		)

		val transactionInstruction = TransactionInstruction(
			FATBOT_ROUTER_PROGRAM_ID,
			accounts,
			instructionData,
		)

		val currentRecommendedFee = getRecommendedFeeSolana()

		val culInstruction = ComputeBudgetProgram.setComputeUnitLimit(CU_LIMIT_BUY_PUMPSWAP)
		val cupInstruction = ComputeBudgetProgram.setComputeUnitPrice(currentRecommendedFee)

		val transaction = LegacyTransaction().apply {
			addInstruction(culInstruction)
			addInstruction(cupInstruction)
			addInstruction(transactionInstruction)
		}

		sendTransaction(transaction, userAccount, useStakedEndpoint, useMevProtection)

		return transaction.toSignedTx()
	}

	private fun buyRaydiumAMM(
		userAccount: Account,
		pairMarketAddress: AddressWrapper,
		marketData: RaydiumAmmMarketData,
		tokenAddress: AddressWrapper,
		associatedTokenAccount: PublicKey,
		targetTokenProgramId: PublicKey,
		amount: BaseAmount,
		feeBp: BasisPoint,
		referralFeeBp: BasisPoint,
		referralUUID: UUID,
		useMevProtection: Boolean,
	): SignedTx {
		val taxBp = if (targetTokenProgramId == TOKEN_2022_PROGRAM_ID) getTokenTransferFee(tokenAddress) else BasisPoint.ZERO

		val amountInAfterFee = amount.minusBasisPoints(feeBp)
		val amountOut = getAmountOutRaydium.getAmountOutAMM(amountInAfterFee, pairMarketAddress, marketData, true)
		val minAmountOut = amountOut
			.minusBasisPoints(taxBp)
			.minusBasisPoints(SLIPPAGE_BP + MANUAL_MIN_AMOUNT_OUT_SLIPPAGE_BUFFER)

		val discriminator = getFunctionDiscriminator(BUY_RAYDIUM_AMM_FUNCTION_NAME)
		// Discriminator (8) + 4 * U64 (8) + UUID size U32 (4) + UUID (36)
		val instructionData = BorshBuffer.allocate(80).apply {
			write(discriminator)
			writeU64(amount.amount.toLong()) // amount
			writeU64(minAmountOut.amount.toLong()) // minimum_amount_out
			writeU64(feeBp.toLong()) // fee_bp
			writeU64(referralFeeBp.toLong()) // referral_fee_bp
			writeString(referralUUID.toString()) // uuid
		}.toByteArray()

		val wsolTokenAccount = getAssociatedTokenAddress(
			mint = WSOL_MINT,
			owner = userAccount.publicKey,
		)

		val market = pairMarketAddress.getSolanaPublicKey()

		val accounts = listOf(
			AccountMeta(RAYDIUM_AMM_PROGRAM_ID, false, false),
			AccountMeta(userAccount.publicKey, true, true),
			AccountMeta(wsolTokenAccount, false, true),
			AccountMeta(WSOL_MINT, false, false),
			AccountMeta(market, false, true),
			AccountMeta(RAYDIUM_AMM_AUTHORITY, false, false),
			AccountMeta(marketData.baseVault.getSolanaPublicKey(), false, true),
			AccountMeta(marketData.quoteVault.getSolanaPublicKey(), false, true),
			AccountMeta(market, false, true),
			AccountMeta(associatedTokenAccount, false, true),
			AccountMeta(tokenAddress.getSolanaPublicKey(), false, false),
			AccountMeta(treasuryWallet, false, true),
			AccountMeta(referralWallet, false, true),
			AccountMeta(ASSOCIATED_TOKEN_PROGRAM_ID, false, false),
			AccountMeta(market, false, true),
			AccountMeta(TOKEN_PROGRAM_ID, false, false),
			AccountMeta(targetTokenProgramId, false, false),
			AccountMeta(SYSTEM_PROGRAM_ID, false, false),
		)

		val transactionInstruction = TransactionInstruction(
			FATBOT_ROUTER_PROGRAM_ID,
			accounts,
			instructionData,
		)

		val currentRecommendedFee = getRecommendedFeeSolana()
		val culInstruction = ComputeBudgetProgram.setComputeUnitLimit(CU_LIMIT_BUY_RAYDIUM)
		val cupInstruction = ComputeBudgetProgram.setComputeUnitPrice(currentRecommendedFee)

		val transaction = LegacyTransaction().apply {
			addInstruction(culInstruction)
			addInstruction(cupInstruction)
			addInstruction(transactionInstruction)
		}
		sendTransaction(transaction, userAccount, useStakedEndpoint = false, useMevProtection)

		return transaction.toSignedTx()
	}

	private fun buyRaydiumCLMM(
		userAccount: Account,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		associatedTokenAccount: PublicKey,
		targetTokenProgramId: PublicKey,
		amount: BaseAmount,
		feeBp: BasisPoint,
		referralFeeBp: BasisPoint,
		referralUUID: UUID,
		useMevProtection: Boolean,
	): SignedTx {
		val inputAmountAfterFee = amount.minusBasisPoints(feeBp)

		val swapData = fatbotTsProxyConnector.computeSwapCLMM(
			poolAddress = pairAddress,
			inputTokenMint = WSOL_MINT_ADDRESS,
			inputAmount = inputAmountAfterFee,
		)

		val minAmountOut = swapData.expectedAmountOut.asBaseAmount()
			.minusBasisPoints(SLIPPAGE_BP + MANUAL_MIN_AMOUNT_OUT_SLIPPAGE_BUFFER)

		val discriminator = getFunctionDiscriminator(BUY_RAYDIUM_CLMM_FUNCTION_NAME)
		// Discriminator (8) + 4 * U64 (8) + UUID size U32 (4) + UUID (36)
		val instructionData = BorshBuffer.allocate(80).apply {
			write(discriminator)
			writeU64(amount.amount.toLong()) // amount
			writeU64(minAmountOut.amount.toLong()) // minimum_amount_out
			writeU64(feeBp.toLong()) // fee_bp
			writeU64(referralFeeBp.toLong()) // referral_fee_bp
			writeString(referralUUID.toString()) // uuid
		}.toByteArray()

		val wsolTokenAccount = getAssociatedTokenAddress(
			mint = WSOL_MINT,
			owner = userAccount.publicKey,
		)

		val poolAddress = pairAddress.getSolanaPublicKey()
		val wsolVault = getCLMMPoolVaultPda(poolAddress, WSOL_MINT)
		val tokenVault = getCLMMPoolVaultPda(poolAddress, tokenAddress.getSolanaPublicKey())
		val bitmapExtensionAccount = getCLMMPoolBitmapExtensionAccountPda(poolAddress)

		// We take observationId from swapData even though it SHOULD BE a direct Pda of poolAddress as per current CLMM program
		// It seems that some old tokens do not abide by that, suggesting the observation account was much less constrained in the past
		val observationAccount = swapData.observationId
		val remainingAccounts = swapData.remainingAccounts.map { AccountMeta(it.getSolanaPublicKey(), false, true) }

		val accounts = listOf(
			AccountMeta(userAccount.publicKey, true, true),
			AccountMeta(wsolTokenAccount, false, true),
			AccountMeta(WSOL_MINT, false, false),
			AccountMeta(wsolVault, false, true),
			AccountMeta(associatedTokenAccount, false, true),
			AccountMeta(tokenAddress.getSolanaPublicKey(), false, false),
			AccountMeta(tokenVault, false, true),
			AccountMeta(treasuryWallet, false, true),
			AccountMeta(referralWallet, false, true),
			AccountMeta(poolAddress, false, true),
			AccountMeta(swapData.ammConfig.id.getSolanaPublicKey(), false, false),
			AccountMeta(observationAccount.getSolanaPublicKey(), false, true),
			AccountMeta(RAYDIUM_CLMM_PROGRAM_ID, false, false),
			AccountMeta(SYSTEM_PROGRAM_ID, false, false),
			AccountMeta(ASSOCIATED_TOKEN_PROGRAM_ID, false, false),
			AccountMeta(TOKEN_PROGRAM_ID, false, false),
			AccountMeta(TOKEN_2022_PROGRAM_ID, false, false),
			AccountMeta(targetTokenProgramId, false, false),
			AccountMeta(MEMO_RPGORAM, false, false),

			AccountMeta(bitmapExtensionAccount, false, true),
		).plus(remainingAccounts)

		val transactionInstruction = TransactionInstruction(
			FATBOT_ROUTER_PROGRAM_ID,
			accounts,
			instructionData,
		)

		val currentRecommendedFee = getRecommendedFeeSolana()
		val culInstruction = ComputeBudgetProgram.setComputeUnitLimit(CU_LIMIT_BUY_RAYDIUM)
		val cupInstruction = ComputeBudgetProgram.setComputeUnitPrice(currentRecommendedFee)

		val transaction = LegacyTransaction().apply {
			addInstruction(culInstruction)
			addInstruction(cupInstruction)
			addInstruction(transactionInstruction)
		}
		sendTransaction(transaction, userAccount, useStakedEndpoint = false, useMevProtection)

		return transaction.toSignedTx()
	}

	private fun buyRaydiumCPMM(
		userAccount: Account,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		associatedTokenAccount: PublicKey,
		targetTokenProgramId: PublicKey,
		marketData: RaydiumCPMMMarketData,
		amount: BaseAmount,
		feeBp: BasisPoint,
		referralFeeBp: BasisPoint,
		referralUUID: UUID,
		useMevProtection: Boolean,
	): SignedTx {
		val taxBp =
			if (targetTokenProgramId == TOKEN_2022_PROGRAM_ID) getTokenTransferFee(tokenAddress) else BasisPoint.ZERO

		val amountInAfterFee = amount.minusBasisPoints(feeBp)

		val amountOut = getAmountOutRaydium.getAmountOutCPMM(amountInAfterFee, pairAddress, tokenAddress, marketData, true)
		val minAmountOut = amountOut
			.minusBasisPoints(taxBp)
			.minusBasisPoints(SLIPPAGE_BP + MANUAL_MIN_AMOUNT_OUT_SLIPPAGE_BUFFER)

		val discriminator = getFunctionDiscriminator(BUY_RAYDIUM_CPMM_FUNCTION_NAME)
		// Discriminator (8) + 4 * U64 (8) + UUID size U32 (4) + UUID (36)
		val instructionData = BorshBuffer.allocate(80).apply {
			write(discriminator)
			writeU64(amount.amount.toLong()) // amount
			writeU64(minAmountOut.amount.toLong()) // minimum_amount_out
			writeU64(feeBp.toLong()) // fee_bp
			writeU64(referralFeeBp.toLong()) // referral_fee_bp
			writeString(referralUUID.toString()) // uuid
		}.toByteArray()

		val wsolTokenAccount = getAssociatedTokenAddress(
			mint = WSOL_MINT,
			owner = userAccount.publicKey,
		)

		val poolAddress = pairAddress.getSolanaPublicKey()
		val wsolVault = getCPMMPoolVaultPda(poolAddress = poolAddress, mint = WSOL_MINT)
		val tokenVault = getCPMMPoolVaultPda(poolAddress = poolAddress, mint = tokenAddress.getSolanaPublicKey())
		val observation = getCPMMObservationPda(poolAddress = poolAddress)

		val accounts = listOf(
			AccountMeta(userAccount.publicKey, true, true),
			AccountMeta(wsolTokenAccount, false, true),
			AccountMeta(WSOL_MINT, false, false),
			AccountMeta(wsolVault, false, true),

			AccountMeta(associatedTokenAccount, false, true),
			AccountMeta(tokenAddress.getSolanaPublicKey(), false, false),
			AccountMeta(tokenVault, false, true),

			AccountMeta(treasuryWallet, false, true),
			AccountMeta(referralWallet, false, true),

			AccountMeta(pairAddress.getSolanaPublicKey(), false, true),
			AccountMeta(marketData.ammConfig.getSolanaPublicKey(), false, false),
			AccountMeta(RAYDIUM_CPMM_AUTHORITY, false, false),
			AccountMeta(observation, false, true),

			AccountMeta(RAYDIUM_CPMM_PROGRAM_ID, false, false),
			AccountMeta(SYSTEM_PROGRAM_ID, false, false),
			AccountMeta(ASSOCIATED_TOKEN_PROGRAM_ID, false, false),
			AccountMeta(targetTokenProgramId, false, false),
			AccountMeta(TOKEN_PROGRAM_ID, false, false),
		)

		val transactionInstruction = TransactionInstruction(
			FATBOT_ROUTER_PROGRAM_ID,
			accounts,
			instructionData,
		)

		val currentRecommendedFee = getRecommendedFeeSolana()
		val culInstruction = ComputeBudgetProgram.setComputeUnitLimit(CU_LIMIT_BUY_RAYDIUM)
		val cupInstruction = ComputeBudgetProgram.setComputeUnitPrice(currentRecommendedFee)

		val transaction = LegacyTransaction().apply {
			addInstruction(culInstruction)
			addInstruction(cupInstruction)
			addInstruction(transactionInstruction)
		}
		sendTransaction(transaction, userAccount, useStakedEndpoint = false, useMevProtection)

		return transaction.toSignedTx()
	}

	private fun sendTransaction(
		transaction: LegacyTransaction,
		account: Account,
		useStakedEndpoint: Boolean,
		useMevProtection: Boolean,
	) {
		if (useStakedEndpoint) require(!useMevProtection)
		if (useMevProtection) require(!useStakedEndpoint)

		val client = when {
			useStakedEndpoint -> stakedRpcClient
			useMevProtection -> jitoRpcClient
			else -> rpcClient
		}

		if (useMevProtection) transaction.addInstruction(createJitoTipInstruction(account))

		client.api.sendLegacyTransaction(transaction, account, getLatestBlockHashSolana())
	}

	private fun createJitoTipInstruction(from: Account) =
		SystemProgram.transfer(from.publicKey, JITO_TIP_1_ACCOUNT, JITO_MIN_TIP.amount.toLong())
}
