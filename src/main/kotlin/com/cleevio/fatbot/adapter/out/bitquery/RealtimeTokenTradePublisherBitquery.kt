package com.cleevio.fatbot.adapter.out.bitquery

import com.cleevio.fatbot.adapter.out.websocket.TokenTradeWebSocketSessionRegistry
import com.cleevio.fatbot.application.common.util.collectCatching
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.token.event.EVMTokenTradedEvent
import com.cleevio.fatbot.application.module.token.event.SolanaTokenTradedEvent
import com.cleevio.fatbot.application.module.token.event.TokenTradedEvent
import com.cleevio.fatbot.application.module.token.port.out.PublishTokenRealTimeTrade
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.logger
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
@ConditionalOnProperty("processing.token-trade-publishing.enabled", havingValue = "true")
class RealtimeTokenTradePublisherBitquery(
	private val publishTokenRealTimeTrade: PublishTokenRealTimeTrade,
	private val tokenTradeSessionRegistry: TokenTradeWebSocketSessionRegistry,
	tokenTradeProvider: TokenTradeProvider,
) {

	private val logger = logger()

	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	private val tradesFlow = tokenTradeProvider.getTokenTradeFlow()

	@EventListener(ApplicationReadyEvent::class)
	fun onStartup() {
		scope.launch { processTradeEvents() }
	}

	private suspend fun processTradeEvents() {
		tradesFlow.collectCatching(
			exceptionHandler = { logger.error("Error in processTradeEvent", it) },
			collector = { trades -> trades.forEach { processTradeEvent(it) } },
		)
	}

	private fun processTradeEvent(event: TokenTradedEvent) {
		val chainAddress = when (event) {
			is SolanaTokenTradedEvent -> event.tokenAddress.toChainAddress(Chain.SOLANA)
			is EVMTokenTradedEvent -> event.tokenAddress.toChainAddress(Chain.ofEVM(event.chainId))
		}

		// Ignore trades with 0 USD price
		if (event.priceUsd.compareTo(BigDecimal.ZERO) == 0) return

		if (tokenTradeSessionRegistry.hasSubscribers(chainAddress)) {
			publishTokenRealTimeTrade(
				tokenAddress = event.tokenAddress,
				chain = chainAddress.chain,
				timestamp = event.timestamp,
				priceUsd = event.priceUsd,
				amountUsd = event.amountUsd,
			)
		}
	}
}
