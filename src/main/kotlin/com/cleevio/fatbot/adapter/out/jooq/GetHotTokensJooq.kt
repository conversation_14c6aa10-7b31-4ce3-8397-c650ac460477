package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.adapter.`in`.InfiniteScroll
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.adapter.`in`.toInfiniteScrollSlice
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.hottoken.port.out.GetHotTokens
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.HOT_TOKEN
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class GetHotTokensJooq(
	private val dslContext: DSLContext,
) : GetHotTokens {

	override fun invoke(
		chains: Set<Chain>,
		infiniteScroll: InfiniteScroll<BigDecimal>,
		fileToUrlMapper: FileToUrlMapper,
	): InfiniteScrollSlice<GetHotTokens.Result, BigDecimal> {
		return dslContext
			.select(
				HOT_TOKEN.ID,
				HOT_TOKEN.TOKEN_ADDRESS,
				HOT_TOKEN.TOKEN_NAME,
				HOT_TOKEN.TOKEN_SYMBOL,
				HOT_TOKEN.CHAIN,
				HOT_TOKEN.TOKEN_IMAGE_FILE_ID,
				HOT_TOKEN.TOKEN_IMAGE_FILE_EXTENSION,
				HOT_TOKEN.VOLUME24H_USD,
			)
			.from(HOT_TOKEN)
			.whereWithInfiniteScroll(
				conditions = listOf(HOT_TOKEN.CHAIN.`in`(chains)),
				infiniteScroll = infiniteScroll,
				idFieldSelector = HOT_TOKEN.VOLUME24H_USD,
			)
			.fetch()
			.map {
				GetHotTokens.Result(
					id = it[HOT_TOKEN.ID]!!,
					tokenAddress = it[HOT_TOKEN.TOKEN_ADDRESS]!!,
					tokenName = it[HOT_TOKEN.TOKEN_NAME]!!,
					tokenSymbol = it[HOT_TOKEN.TOKEN_SYMBOL]!!,
					tokenChain = it[HOT_TOKEN.CHAIN]!!,
					tokenImageUrl = fileToUrlMapper(it[HOT_TOKEN.TOKEN_IMAGE_FILE_ID], it[HOT_TOKEN.TOKEN_IMAGE_FILE_EXTENSION]),
					volume24hUsd = it[HOT_TOKEN.VOLUME24H_USD]!!,
				)
			}.toInfiniteScrollSlice(infiniteScroll = infiniteScroll, idSelector = { volume24hUsd })
	}
}
