package com.cleevio.fatbot.adapter.out.bitquery.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.time.Instant

@JsonIgnoreProperties(ignoreUnknown = true)
data class BitqueryProxySolanaTradesResponse(
	val solana: Solana,
) {

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Solana(
		val dextrades: List<DexTrade>,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class DexTrade(
		val trade: Trade,
		val block: Block,
		val transaction: Transaction,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Trade(
		val buy: TradeSide,
		val sell: TradeSide,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class TradeSide(
		val currency: Currency,
		val amountInUSD: String,
		val priceInUSD: String,
		val price: String,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Currency(
		val mintAddress: String,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Block(
		val time: Instant,
		val slot: String,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Transaction(
		val index: Int,
	)
}
