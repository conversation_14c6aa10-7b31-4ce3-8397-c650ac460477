package com.cleevio.fatbot.adapter.out.solana.pumpswap

import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalances
import com.cleevio.fatbot.adapter.out.solana.model.PoolReserves
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.util.getAssociatedTokenAddress
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutPumpswap
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class GetAmountOutPumpswapSolana(
	private val rpcClient: RpcClient,
) : GetAmountOutPumpswap {

	override operator fun invoke(
		amount: BaseAmount,
		tokenAddress: AddressWrapper,
		pairAddress: AddressWrapper,
		isToken2022: Boolean,
		isBuy: Boolean,
	): BaseAmount {
		val reserves = getReserves(tokenAddress, pairAddress, isToken2022)

		val amountOut = fromReserves(amount, reserves, isBuy)
		return amountOut
	}

	override fun fromReserves(amount: BaseAmount, reserves: PoolReserves, isBuy: Boolean): BaseAmount {
		require(amount != BaseAmount.ZERO) {
			"Amount in getAmountOutPumpswap cannot be 0! Reserves: $reserves, isBuy: $isBuy"
		}

		val amountOut = if (isBuy) {
			calculateBuyAmount(amount = amount, wsolReserves = reserves.wsol.amount, tokenReserves = reserves.token.amount)
		} else {
			calculateSellAmount(amount = amount, wsolReserves = reserves.wsol.amount, tokenReserves = reserves.token.amount)
		}

		return amountOut
	}

	override fun getReserves(
		tokenAddress: AddressWrapper,
		pairAddress: AddressWrapper,
		isToken2022: Boolean,
	): PoolReserves {
		val pool = pairAddress.getSolanaPublicKey()

		val tokenVault = getAssociatedTokenAddress(
			mint = tokenAddress.getSolanaPublicKey(),
			owner = pool,
			programId = if (isToken2022) SolanaConstants.TOKEN_2022_PROGRAM_ID else SolanaConstants.TOKEN_PROGRAM_ID,
		)

		val wsolVault = getAssociatedTokenAddress(
			mint = SolanaConstants.WSOL_MINT,
			owner = pool,
		)

		val (tokenReserves, wsolReserves) = getReserves(tokenVault = tokenVault, wsolVault = wsolVault)

		return PoolReserves(token = tokenReserves.asBaseAmount(), wsol = wsolReserves.asBaseAmount())
	}

	private fun getReserves(tokenVault: PublicKey, wsolVault: PublicKey): Pair<BigInteger, BigInteger> {
		val (tokenReserves, wsolReserves) = rpcClient.getTokenAccountBalances(
			listOf(tokenVault, wsolVault),
			Commitment.CONFIRMED,
		)

		requireNotNull(tokenReserves) { "Received null token reserves for tokenVault: $tokenVault" }
		requireNotNull(wsolReserves) { "Received null wsol reserves for wsolVault: $wsolVault" }
		require(tokenReserves != BigInteger.ZERO) { "Received 0 token reserves for tokenVault: $tokenVault" }
		require(wsolReserves != BigInteger.ZERO) { "Received 0 wsol reserves for wsolVault: $wsolVault" }

		return tokenReserves to wsolReserves
	}

	// Buy Wsol -> Token
	private fun calculateBuyAmount(amount: BaseAmount, wsolReserves: BigInteger, tokenReserves: BigInteger): BaseAmount {
		val denominator = (BasisPoint.MAX_VALUE + SolanaConstants.PUMPSWAP_TOTAL_FEE_BP.value).toBigInteger()

		// Amount after Pumpswap fees that will be swapped in the pool
		val effectiveAmount = amount.amount * BasisPoint.MAX_VALUE.toBigInteger() / denominator

		val amountOut = (tokenReserves * effectiveAmount) / (wsolReserves + effectiveAmount)

		return amountOut.asBaseAmount()
	}

	// Sell Token -> Wsol
	private fun calculateSellAmount(amount: BaseAmount, wsolReserves: BigInteger, tokenReserves: BigInteger): BaseAmount {
		val solAmount = (wsolReserves * amount.amount) / (tokenReserves + amount.amount)

		val lpFee = SolanaConstants.PUMPSWAP_LP_FEE_BP.applyOnCeilDiv(solAmount)
		val protocolFee = SolanaConstants.PUMPSWAP_PROTOCOL_FEE_BP.applyOnCeilDiv(solAmount)
		val creatorFee = SolanaConstants.PUMPSWAP_CREATOR_FEE_BP.applyOnCeilDiv(solAmount)

		val amountOut = solAmount - lpFee - protocolFee - creatorFee

		return amountOut.asBaseAmount()
	}
}
