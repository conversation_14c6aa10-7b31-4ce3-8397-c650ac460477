package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.asWei
import com.cleevio.fatbot.application.common.crypto.remove0x
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.transaction.constant.FunctionSelector
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.constant.isSellOrTransfer
import com.cleevio.fatbot.application.module.transaction.port.out.DecodeSignedTransaction
import com.cleevio.fatbot.domain.transaction.Transaction
import org.springframework.stereotype.Component
import org.web3j.crypto.SignedRawTransaction
import org.web3j.crypto.TransactionDecoder
import org.web3j.crypto.transaction.type.Transaction1559
import java.util.UUID

@Component
class DecodeEvmSignedTransaction : DecodeSignedTransaction {

	override fun invoke(
		walletId: UUID,
		signedTx: SignedTx,
		transactionType: TransactionType,
		tokenAddress: AddressWrapper?,
		limitOrderId: UUID?,
	): Transaction {
		val signedRawTransaction = TransactionDecoder.decode(signedTx.signedTx) as SignedRawTransaction

		return Transaction.ofEvm(
			walletId = walletId,
			type = determineEvmTransactionType(type = transactionType, transactionData = signedRawTransaction.data),
			nonce = signedRawTransaction.nonce.longValueExact(),
			baseValue = signedRawTransaction.value.asWei(),
			signedTx = signedTx,
			tokenAddress = tokenAddress,
			chainId = determineEvmChainId(signedRawTransaction = signedRawTransaction),
			limitOrderId = limitOrderId,
		)
	}

	private fun determineEvmTransactionType(type: TransactionType, transactionData: String): TransactionType {
		if (transactionData.isBlank()) return type

		if (type.isSellOrTransfer()) {
			if (transactionData.take(8).equals(FunctionSelector.APPROVE.remove0x(), ignoreCase = true)) {
				return TransactionType.APPROVE
			}
		}

		return type
	}

	private fun determineEvmChainId(signedRawTransaction: SignedRawTransaction) =
		if (signedRawTransaction.transaction.type.isLegacy) {
			signedRawTransaction.chainId
		} else {
			(signedRawTransaction.transaction as Transaction1559).chainId
		}
}
