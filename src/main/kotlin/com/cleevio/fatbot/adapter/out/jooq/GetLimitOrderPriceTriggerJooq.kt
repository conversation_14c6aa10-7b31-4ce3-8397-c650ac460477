package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.limitorder.port.out.GetLimitOrderPriceTrigger
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.tables.references.LIMIT_ORDER
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class GetLimitOrderPriceTriggerJooq(
	private val dslContext: DSLContext,
) : GetLimitOrderPriceTrigger {

	override fun invoke(): Map<AddressWrapper, GetLimitOrderPriceTrigger.Result> {
		val maxBuyField = DSL.max(LIMIT_ORDER.LIMIT_PRICE).filterWhere(LIMIT_ORDER.TYPE.eq(LimitOrderType.BUY))
		val minSellField = DSL.min(LIMIT_ORDER.LIMIT_PRICE).filterWhere(LIMIT_ORDER.TYPE.eq(LimitOrderType.SELL))

		return dslContext
			.select(
				LIMIT_ORDER.TOKEN_ADDRESS,
				maxBuyField,
				minSellField,
			)
			.from(LIMIT_ORDER)
			.where(LIMIT_ORDER.REMAINING_AMOUNT.gt(BigDecimal.ZERO))
			.groupBy(LIMIT_ORDER.TOKEN_ADDRESS)
			.fetch()
			.associate {
				val result = GetLimitOrderPriceTrigger.Result(
					maxBuyPrice = it[maxBuyField]?.toBigInteger()?.asBaseAmount()?.toNative(Chain.SOLANA),
					minSellPrice = it[minSellField]?.toBigInteger()?.asBaseAmount()?.toNative(Chain.SOLANA),
				)

				it[LIMIT_ORDER.TOKEN_ADDRESS]!! to result
			}
	}
}
