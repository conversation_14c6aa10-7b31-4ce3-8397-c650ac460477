package com.cleevio.fatbot.adapter.out.firebase

import com.cleevio.fatbot.application.common.util.EpochClockTicker
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.access.port.out.GetEnabledChains
import com.cleevio.fatbot.application.module.access.port.out.ParseToken
import com.cleevio.fatbot.application.module.access.port.out.UserAuthDetail
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.FatbotDispatchers
import com.cleevio.fatbot.infrastructure.config.logger
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.benmanes.caffeine.cache.Caffeine
import com.google.auth.oauth2.GoogleCredentials
import com.google.firebase.FirebaseApp
import com.google.firebase.FirebaseOptions
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ParameterValue
import com.google.firebase.remoteconfig.Template
import io.sentry.spring.jakarta.tracing.SentrySpan
import kotlinx.coroutines.asExecutor
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.DependsOn
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import java.time.Clock
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.concurrent.CompletableFuture

private const val REMOTE_CONFIG_ENABLED_CHAINS_PROPERTY_NAME = "BE_enabled_chains"

@Service
class FirebaseService(
	private val firebaseAuth: FirebaseAuth,
	private val firebaseRemoteConfig: FirebaseRemoteConfig,
	private val objectMapper: ObjectMapper,
	clock: Clock,
	fatbotDispatchers: FatbotDispatchers,
) : ParseToken, GetEnabledChains {

	private val logger = logger()

	/**
	 * Cache used to track if the Firebase JWT token in still valid. The reason for this is, that
	 * we don't want to slow every API call by cca. 200ms to check whether the JWT token is revoked.
	 *
	 * But we still need a way to tell, if the token was revoked. By using async cache, whenever when
	 * new JWT token arrives, it's always checked for revocation (as it's not in cache) and then on each subsequent request
	 * after `refreshAfterWrite` duration passes we check on async thread if it's still valid and not revoked.
	 *
	 * After `expireAfterWrite` duration passes without any request JWT token is guaranteed to be checked for revocation
	 * (as it is marked by cache as expired)
	 */
	private val cache = Caffeine
		.newBuilder()
		.refreshAfterWrite(Duration.of(10, ChronoUnit.SECONDS))
		.expireAfterWrite(Duration.of(60, ChronoUnit.SECONDS))
		.ticker(EpochClockTicker(clock))
		.executor(fatbotDispatchers.Caffeine.asExecutor())
		.buildAsync<String, UserAuthDetail> { key, executor ->
			CompletableFuture.supplyAsync(
				{
					val userEmail = getUserEmailFromTokenOrNull(key)?.email ?: return@supplyAsync null
					UserAuthDetail(email = userEmail)
				},
				executor,
			)
		}

	@SentrySpan
	override fun invoke(token: String): UserAuthDetail {
		return cache.get(token).join()
	}

	private fun getUserEmailFromTokenOrNull(token: String) =
		runCatching { firebaseAuth.verifyIdToken(token, true) }.getOrNull()

	override fun fetch(): Set<Chain> {
		// remote config versions are ordered in a way where first is the latest
		val latestVersion = firebaseRemoteConfig.listVersions().values.first().versionNumber
		val latestTemplate = firebaseRemoteConfig.getTemplateAtVersion(latestVersion)

		val enabledChains = latestTemplate
			.getJsonPropertyFrom<List<String>>(REMOTE_CONFIG_ENABLED_CHAINS_PROPERTY_NAME)
			.mapToSet { Chain.valueOf(it) }

		return enabledChains
	}

	private inline fun <reified T : Any> Template.getJsonPropertyFrom(name: String): T {
		val propertyAsString = (this.parameters[name]?.defaultValue as ParameterValue.Explicit?)?.value
			?: error("Firebase remote config property $name could not been found!")
		return runCatching { objectMapper.readValue(propertyAsString, T::class.java) }
			.getOrElse { ex ->
				logger.error(
					"Could not parse Firebase remote config property $name into class of type ${T::class.java.name}",
					ex,
				)
				error("Could not parse property $name into class of type ${T::class.java.name}")
			}
	}
}

@Configuration
@Profile("!test")
private class FirebaseConfig(
	@Value("\${firebase.credentials}") private val firebaseCredentials: String,
) {
	@Bean
	fun firebaseApp(): FirebaseApp {
		val firebaseOptions =
			FirebaseOptions.builder()
				.setCredentials(GoogleCredentials.fromStream(firebaseCredentials.byteInputStream()))
				.build()

		return FirebaseApp.initializeApp(firebaseOptions)
	}

	@Bean
	@DependsOn("firebaseApp")
	fun firebaseAuth(firebaseApp: FirebaseApp): FirebaseAuth = FirebaseAuth.getInstance(firebaseApp)

	@Bean
	@DependsOn("firebaseApp")
	fun firebaseMessaging(firebaseApp: FirebaseApp): FirebaseMessaging = FirebaseMessaging.getInstance(firebaseApp)

	@Bean
	@DependsOn("firebaseApp")
	fun firebaseRemoteConfig(firebaseApp: FirebaseApp): FirebaseRemoteConfig {
		return FirebaseRemoteConfig.getInstance(firebaseApp)
	}
}
