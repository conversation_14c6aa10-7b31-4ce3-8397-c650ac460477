package com.cleevio.fatbot.adapter.out.websocket

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.context.event.EventListener
import org.springframework.messaging.simp.stomp.StompHeaderAccessor
import org.springframework.stereotype.Component
import org.springframework.web.socket.messaging.SessionDisconnectEvent
import org.springframework.web.socket.messaging.SessionSubscribeEvent
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap

@Component
class TokenTradeWebSocketSessionRegistry {

	private val logger = logger()

	private val chainAddressToSessionIds: ConcurrentMap<ChainAddress, Set<String>> = ConcurrentHashMap()

	@EventListener
	fun onSessionSubscribe(event: SessionSubscribeEvent) {
		val accessor = StompHeaderAccessor.wrap(event.message)
		val sessionId = accessor.sessionId ?: error("Session ID is required")
		val destination = accessor.destination ?: error("Destination is null")

		val chainAddress = extractChainAddress(destination) ?: return

		chainAddressToSessionIds.merge(chainAddress, setOf(sessionId), Set<String>::plus)
	}

	@EventListener
	fun onSessionDisconnectEvent(event: SessionDisconnectEvent) {
		val accessor = StompHeaderAccessor.wrap(event.message)
		val sessionId = accessor.sessionId ?: error("Session ID is required")

		val chainAddressesWithSession = chainAddressToSessionIds.filterValues { sessionId in it }.keys

		chainAddressesWithSession.forEach { chainAddress ->
			chainAddressToSessionIds.compute(chainAddress) { _, sessionIds ->
				sessionIds?.minus(sessionId)?.ifEmpty { null }
			}
		}
	}

	fun hasSubscribers(chainAddress: ChainAddress) = chainAddressToSessionIds.containsKey(chainAddress)

	private fun extractChainAddress(destination: String): ChainAddress? {
		if (!destination.startsWith(TOKEN_TRADE_DESTINATION_PREFIX)) return null

		val matchResult = destination.removePrefix("$TOKEN_TRADE_DESTINATION_PREFIX/").split("/")

		if (matchResult.size != 2) return null

		val result = runCatching {
			val chain = Chain.valueOf(matchResult[0])
			val tokenAddress = AddressWrapper(matchResult[1])

			ChainAddress(chain, tokenAddress)
		}

		return result
			.onFailure { logger.warn("Failed to parse chain or token address from subscription: $destination", it) }
			.getOrNull()
	}
}
