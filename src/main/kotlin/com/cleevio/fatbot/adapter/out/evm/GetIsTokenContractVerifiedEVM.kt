package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.port.out.GetIsTokenContractVerified
import org.springframework.stereotype.Component

@Component
class GetIsTokenContractVerifiedEVM(private val etherscanConnector: EtherscanConnector) : GetIsTokenContractVerified {

	override fun invoke(tokenAddress: AddressWrapper, chainId: Long): Boolean {
		val contractAbiResponse = etherscanConnector.getSourceCode(tokenAddress, chainId).result.first()
		val isProxy = contractAbiResponse.isProxy()

		// isProxy - true, isVerified - true 	| result - true
		// isProxy - true, isVerified - false 	| result - true
		// isProxy - false, isVerified - true 	| result - true
		// isProxy - false, isVerified - false	| result - false
		return contractAbiResponse.isVerified() || isProxy
	}

	private fun EtherscanConnector.GetSourceCodeResultResponse.isVerified(): Boolean = when {
		abi == "Contract source code not verified" -> false
		abi.matches(Regex("""^\[.*]$""")) -> true
		else -> error("Failed to get verified contract ABI. Result: $abi")
	}
}
