package com.cleevio.fatbot.adapter.out.evm.builder.beaverbuild

import com.cleevio.fatbot.adapter.out.BaseConnector
import com.cleevio.fatbot.adapter.out.evm.builder.beaverbuild.request.SendPrivateEthTransactionBeaverbuildRequest
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.infrastructure.config.properties.BeaverbuildProperties
import org.springframework.http.MediaType
import org.springframework.stereotype.Component

@Component
class BeaverbuildConnector(
	beaverbuildProperties: BeaverbuildProperties,
) : BaseConnector(
	baseUrl = beaverbuildProperties.baseUrl,
) {

	fun sendPrivateEthTransaction(signedTx: SignedTx) {
		restClient
			.post()
			.body(
				SendPrivateEthTransactionBeaverbuildRequest(
					params = listOf(
						signedTx,
					),
				),
			)
			.contentType(MediaType.APPLICATION_JSON)
			.retrieveWithError<PERSON>andler()
	}
}
