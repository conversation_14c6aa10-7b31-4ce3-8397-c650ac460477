package com.cleevio.fatbot.adapter.out.websocket.dto

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.time.Instant

data class TokenRealTimeTradeMessage(
	val tokenAddress: AddressWrapper,
	val chain: Chain,
	val timestamp: Instant,
	val priceUsd: BigDecimal,
	val amountUsd: BigDecimal,
)
