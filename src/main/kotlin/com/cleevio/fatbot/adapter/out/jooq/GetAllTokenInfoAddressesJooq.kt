package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.tokenpricesnapshot.port.out.GetKnownTokenAddresses
import com.cleevio.fatbot.tables.references.EVM_TOKEN_INFO
import org.jooq.DSLContext
import org.springframework.stereotype.Component

@Component
class GetAllTokenInfoAddressesJooq(
	private val dslContext: DSLContext,
) : GetKnownTokenAddresses {

	/**
	 * To optimize performance, the tokens are ordered by chainId.
	 * This increases the likelihood that when the tokens are chunked for price retrieval,
	 * a single call will be sufficient to fetch their prices.
	 */
	override fun invoke(): Set<ChainAddress> {
		return dslContext
			.select(EVM_TOKEN_INFO.ADDRESS, EVM_TOKEN_INFO.CHAIN)
			.from(EVM_TOKEN_INFO)
			.fetch()
			.sortedBy { (_, chain) -> chain!!.id }
			.mapToSet { (address, chain) -> ChainAddress(chain!!, address!!) }
	}
}
