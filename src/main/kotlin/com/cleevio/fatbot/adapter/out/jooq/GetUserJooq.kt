package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.user.exception.UserNotFoundException
import com.cleevio.fatbot.application.module.user.port.out.GetUser
import com.cleevio.fatbot.application.module.user.query.GetUserQuery
import com.cleevio.fatbot.tables.references.FIREBASE_USER
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class GetUserJooq(private val dslContext: DSLContext) : GetUser {

	override fun invoke(userId: UUID): GetUserQuery.Result {
		return dslContext
			.select(
				FIREBASE_USER.ID,
				FIREBASE_USER.EMAIL,
				FIREBASE_USER.REFERRAL_CODE,
				FIREBASE_USER.QUICK_BUY_AMOUNT_USD,
				FIREBASE_USER.REFERRED_BY_USER_ID,
				FIREBASE_USER.SELECTED_CHAINS,
				FIREBASE_USER.LANGUAGE,
			)
			.from(FIREBASE_USER)
			.where(FIREBASE_USER.ID.eq(userId))
			.fetchOne()
			?.map {
				GetUserQuery.Result(
					userId = it[FIREBASE_USER.ID]!!,
					email = it[FIREBASE_USER.EMAIL]!!,
					referralCode = it[FIREBASE_USER.REFERRAL_CODE],
					quickBuyAmountUsd = it[FIREBASE_USER.QUICK_BUY_AMOUNT_USD]!!,
					isReferred = it[FIREBASE_USER.REFERRED_BY_USER_ID] != null,
					selectedChains = it[FIREBASE_USER.SELECTED_CHAINS]!!.toSet(),
					language = it[FIREBASE_USER.LANGUAGE]!!,
				)
			} ?: throw UserNotFoundException("User not found.")
	}
}
