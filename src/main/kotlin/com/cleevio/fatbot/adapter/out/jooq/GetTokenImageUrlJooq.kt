package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.market.port.out.GetTokenImageUrl
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.EVM_TOKEN_INFO
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.net.URI

@Component
class GetTokenImageUrlJooq(private val dslContext: DSLContext) : GetTokenImageUrl {
	override fun invoke(token: AddressWrapper, chain: Chain, mapper: FileToUrlMapper): URI? {
		val conditions = listOf(
			EVM_TOKEN_INFO.ADDRESS.eq(token),
			EVM_TOKEN_INFO.CHAIN.eq(chain),
		)

		return dslContext
			.select(
				EVM_TOKEN_INFO.file.ID,
				EVM_TOKEN_INFO.file.EXTENSION,
			)
			.from(EVM_TOKEN_INFO)
			.where(conditions)
			.fetchOne()
			?.let {
				mapper(
					it[EVM_TOKEN_INFO.file.ID],
					it[EVM_TOKEN_INFO.file.EXTENSION],
				)
			}
	}
}
