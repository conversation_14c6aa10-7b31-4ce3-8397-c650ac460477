package com.cleevio.fatbot.adapter.out.coinbase

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.util.EpochClockTicker
import com.cleevio.fatbot.application.common.util.runWithRetry
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.infrastructure.config.FatbotDispatchers
import com.cleevio.fatbot.infrastructure.config.logger
import com.github.benmanes.caffeine.cache.Caffeine
import kotlinx.coroutines.asExecutor
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.util.concurrent.CompletableFuture
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

private val GET_EXCHANGE_RATE_RETRY_DELAY = 150.milliseconds.toJavaDuration()

@Component
class GetExchangeRateCoinbase(
	private val coinbaseConnector: CoinbaseConnector,
	clock: Clock,
	fatbotDispatchers: FatbotDispatchers,
) : GetUsdExchangeRate {

	private val logger = logger()

	private val cache = Caffeine
		.newBuilder()
		.maximumSize(10)
		.refreshAfterWrite(45.seconds.toJavaDuration())
		.expireAfterWrite(60.seconds.toJavaDuration())
		.ticker(EpochClockTicker(clock))
		.executor(fatbotDispatchers.Caffeine.asExecutor())
		.buildAsync<CryptoCurrency, BigDecimal> { key, executor ->
			CompletableFuture.supplyAsync({ getUsdExchangeRate(currency = key) }, executor)
		}

	override fun invoke(cryptoCurrency: CryptoCurrency): BigDecimal {
		return cache.get(cryptoCurrency).join()
	}

	override fun getAll(cryptoCurrencies: Set<CryptoCurrency>): Map<CryptoCurrency, BigDecimal> {
		return cache.getAll(cryptoCurrencies).join()
	}

	/**
	 * Retry addresses a very rare ResourceAccessException with an UnknownHostException
	 */
	private fun getUsdExchangeRate(currency: CryptoCurrency): BigDecimal {
		val result = runWithRetry(
			retries = 2,
			retryDelay = GET_EXCHANGE_RATE_RETRY_DELAY,
			onError = { logger.warn("Error getting usd exchange rate", it) },
			block = { runCatching { coinbaseConnector.getUsdExchangeRate(currency = currency) } },
		)

		return result.getOrThrow()
	}
}
