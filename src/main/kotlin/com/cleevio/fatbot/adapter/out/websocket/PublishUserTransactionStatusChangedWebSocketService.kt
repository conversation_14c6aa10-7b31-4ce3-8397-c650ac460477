package com.cleevio.fatbot.adapter.out.websocket

import com.cleevio.fatbot.adapter.out.messagingproxy.FatbotMessagingProxyConnector
import com.cleevio.fatbot.adapter.out.websocket.constant.WebSocketMessageType
import com.cleevio.fatbot.adapter.out.websocket.dto.WebSocketMessage
import com.cleevio.fatbot.infrastructure.config.logger
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Service

@Service
@ConditionalOnProperty("processing.user-transactions.enabled", havingValue = "true")
class PublishUserTransactionStatusChangedWebSocketService(
	private val webSocketMessagingService: WebSocketMessagingService,
	private val messagingProxyConnector: FatbotMessagingProxyConnector,
) {

	private val logger = logger()

	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	@EventListener(ApplicationReadyEvent::class)
	fun onStartup() {
		scope.launch { subscribeToUserTransactionStatusChanged() }
	}

	suspend fun subscribeToUserTransactionStatusChanged() {
		while (true) {
			runCatching {
				logger.info("Subscribing to user transaction messages stream.")

				messagingProxyConnector.subscribeToUserTransactionMessages().collect { message ->
					webSocketMessagingService.sendMessageToUser(
						userId = message.userId,
						destination = "/queue/user-transactions",
						message = WebSocketMessage(
							type = WebSocketMessageType.TRANSACTION_STATUS_CHANGED,
							data = message,
						),
					)
				}
			}.onFailure {
				logger.error("Error subscribing to user transaction messages stream.", it)
			}

			logger.info("Backing off before retrying to connect to user transactions message stream.")
			delay(1000)
		}
	}
}
