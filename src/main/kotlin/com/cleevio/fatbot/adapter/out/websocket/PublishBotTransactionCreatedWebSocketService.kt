package com.cleevio.fatbot.adapter.out.websocket

import com.cleevio.fatbot.adapter.out.messagingproxy.FatbotMessagingProxyConnector
import com.cleevio.fatbot.adapter.out.websocket.constant.WebSocketMessageType
import com.cleevio.fatbot.adapter.out.websocket.dto.WebSocketMessage
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.infrastructure.config.logger
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Service

@Service
@ConditionalOnProperty("processing.bot-transactions.enabled", havingValue = "true")
class PublishBotTransactionCreatedWebSocketService(
	private val webSocketMessagingService: WebSocketMessagingService,
	private val messagingProxyConnector: FatbotMessagingProxyConnector,
) {

	private val logger = logger()
	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	@EventListener(ApplicationReadyEvent::class)
	fun onStartup() {
		scope.launch { subscribeToBotTransactionMessages() }
	}

	suspend fun subscribeToBotTransactionMessages() {
		while (true) {
			runCatching {
				logger.info("Subscribing to Bot transaction messages stream.")
				messagingProxyConnector.subscribeToBotTransactionMessages().collect { message ->

					// TODO: This should be sending to user, but currently we don't have user id in the message
					//  Ideally we should fetch the userId by botId, cache it and then use sendMessageToUser
					webSocketMessagingService.sendMessage(
						destination = "/queue/bot/${message.botId}/trades",
						message = WebSocketMessage(
							type = when (message.type) {
								BotTransactionType.BUY -> WebSocketMessageType.BOT_TRANSACTION_BUY
								BotTransactionType.SELL -> WebSocketMessageType.BOT_TRANSACTION_SELL
								else -> throw IllegalArgumentException("Unsupported transaction type")
							},
							data = message,
						),
					)
				}
			}
				.onFailure { logger.error("Subscription to bot transaction messages failed", it) }

			logger.info("Backing off before retrying to connect to bot transaction message stream.")
			delay(1000)
		}
	}
}
