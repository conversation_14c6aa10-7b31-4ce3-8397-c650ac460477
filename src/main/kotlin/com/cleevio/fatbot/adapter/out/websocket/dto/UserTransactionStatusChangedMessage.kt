package com.cleevio.fatbot.adapter.out.websocket.dto

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import java.math.BigDecimal
import java.util.UUID

data class UserTransactionStatusChangedMessage(
	val userId: UUID,
	val transactionId: UUID,
	val transactionStatus: TransactionStatus,
	val txHash: TxHash,
	val tokenNativeAmount: NativeAmount?,
	val currencyNativeAmount: NativeAmount?,
	val currencyAmountUsd: BigDecimal?,
)
