package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.getFunctionDiscriminator
import com.cleevio.fatbot.application.common.util.readAddress
import com.cleevio.fatbot.application.common.util.toUUID
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailure
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailureReason
import com.cleevio.fatbot.application.module.transaction.constant.TransactionPending
import com.cleevio.fatbot.application.module.transaction.constant.TransactionResult
import com.cleevio.fatbot.application.module.transaction.constant.TransferCurrencyTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransferTokenTransactionSuccess
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.syntifi.near.borshj.BorshBuffer
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.bitcoinj.core.Base58
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.ConfirmedTransaction
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.stereotype.Component
import java.math.BigInteger
import java.util.Base64
import java.util.UUID

private val TOKEN_PROGRAMS = listOf(SolanaConstants.TOKEN_PROGRAM_ID, SolanaConstants.TOKEN_2022_PROGRAM_ID)

@Component
class GetTransactionResultsSVM(
	private val rpcClient: RpcClient,
	private val getTokenPrices: GetTokenPrices,
) {

	@SentrySpan
	operator fun invoke(txHashes: Set<TxHash>): Map<TxHash, TransactionResult> {
		if (txHashes.isEmpty()) return emptyMap()
		val signatures = txHashes.map { it.txHash }

		// TODO: This could be done in parallel
		val response = signatures
			.chunked(100)
			.flatMap { rpcClient.api.getTransactions(it, Commitment.CONFIRMED) }

		val signatureToTxResult = response.mapNotNull { txResult ->
			val signature = txResult?.transaction?.signatures?.first()
			signature?.let { it to txResult }
		}.toMap()

		val txHashToTxResult = txHashes.associateWith { signatureToTxResult[it.txHash] }
		val txHashToResult = txHashToTxResult.mapValues { (txHash, txResult) ->
			when {
				txResult == null -> TransactionPending()
				txResult.meta.err != null -> TransactionFailure(
					// The whole balance change on error tx is Fees
					balanceChange = (txResult.meta.postBalances[0] - txResult.meta.preBalances[0])
						.toBigInteger()
						.asBaseAmount(),
					isNoTokensOwnedErrorHint = txResult.meta.logMessages.any {
						it.contains("Error Number: 6001. Error Message: ZeroBaseAmount.")
					},
					failReason = TransactionFailureReason.UNDEFINED,
				)

				txResult.isCurrencyTransferTransaction() -> txResult.getCurrencyTransferResult()
				txResult.isTokenTransferTransaction() -> txResult.getTransferTokenResult()
				txResult.isBuySellSuccessTransaction() -> {
					val event = TokensSuccessfullySwappedEvent.fromTransaction(txResult)

					val instructionAmount = txResult.getBuySellInstructionAmount()

					BuySellTransactionSuccess(
						amountIn = event.amountIn.toBigInteger(),
						amountOut = event.amountOut.toBigInteger(),
						fee = event.fee.toBigInteger(),
						referralFee = event.referralFee.toBigInteger(),
						referralRewardRecipient = event.referralUUID,
						balanceChange = event.balanceChange,
						instructionAmount = instructionAmount.toBigInteger(),
					)
				}

				else -> error("Unknown txResult of tx with signature ${txHash.txHash}")
			}
		}

		return txHashToResult
	}

	fun ConfirmedTransaction.getBuySellInstructionAmount(): Long {
		val accountKeys = transaction.message.accountKeys
		val buySellInstruction = transaction.message.instructions.single {
			val programAddress = accountKeys[it.programIdIndex.toInt()]

			PublicKey(programAddress) == SolanaConstants.FATBOT_ROUTER_PROGRAM_ID
		}

		val buffer = BorshBuffer.wrap(Base58.decode(buySellInstruction.data))

		buffer.read(8) // Skip discriminator
		val amount = buffer.readU64()

		return amount
	}

	/**
	 * We check the message object and determine that this transaction is transfer of SOL if
	 * there is:
	 *  * exactly three Instructions (two ComputeBudget + one transfer)
	 *  * last Instruction is interacting with two accounts (from -> to)
	 *  * the programIdIndex is pointing to SystemProgram address in accountKeys
	 */
	private fun ConfirmedTransaction.isCurrencyTransferTransaction(): Boolean {
		val message = transaction.message
		if (message.instructions.size != 3) return false

		val instruction = message.instructions.last()
		if (instruction.accounts.size != 2) return false

		val accountKeys = message.accountKeys
		val programAddress = accountKeys[instruction.programIdIndex.toInt()]

		return programAddress == SolanaConstants.SYSTEM_PROGRAM_ID.toString()
	}

	/**
	 * We determine transaction is token transfer if:
	 * * there is 3/4/5 instructions: (first two are always ComputeBudget instructions and are omitted in examples below)
	 * ```
	 * Transfer (sender and receiver has token account)
	 *
	 * CreateAccount (creating receiver token account)
	 * Transfer
	 *
	 * Transfer
	 * CloseAccount (sender has 0 token balance after transfer on sender token account)
	 *
	 * CreateAccount (creating receiver token account)
	 * Transfer
	 * CloseAccount (sender has 0 token balance after transfer on sender token account)
	 * ```
	 * * the last instruction is interacting with three accounts (source -> destination, authority)
	 * * the last instruction programIdIndex is pointing to TokenProgram
	 *
	 */
	private fun ConfirmedTransaction.isTokenTransferTransaction(): Boolean {
		val message = transaction.message
		val numberOfInstruction = message.instructions.size
		if (numberOfInstruction !in 3..5) return false

		message.findTokenTransferInstruction() ?: return false

		return true
	}

	private val transferMethodId = 3.toByte()
	private val transferCheckedMethodId = 12.toByte()
	private val transferMethodIds = listOf(transferMethodId, transferCheckedMethodId)

	private fun ConfirmedTransaction.Message.findTokenTransferInstruction() = this.instructions.find {
		val programAddress = accountKeys[it.programIdIndex.toInt()]
		val instructionDescriptor = Base58.decode(it.data).first()

		instructionDescriptor in transferMethodIds && PublicKey(programAddress) in TOKEN_PROGRAMS
	}

	/**
	 * Assuming this is currency transfer transaction, the first account must be the one sending SOL,
	 * the second one is the destination account where SOL is being transferred to.
	 *
	 * By checking their SOL balance pre and post we can determine how much was sent.
	 */
	private fun ConfirmedTransaction.getCurrencyTransferResult(): TransferCurrencyTransactionSuccess {
		val amountSubtractedFromSenderAccount = meta.preBalances[0] - meta.postBalances[0]
		val destinationAddressReceived = amountSubtractedFromSenderAccount - this.meta.fee

		return TransferCurrencyTransactionSuccess(
			value = destinationAddressReceived.toBigInteger(),
			fee = this.meta.fee.toBigInteger(),
		)
	}

	private fun ConfirmedTransaction.getTransferTokenResult(): TransferTokenTransactionSuccess {
		val tokenAddress = AddressWrapper(meta.preTokenBalances.first().mint)

		// we grab aby token account after
		val postTokenBalance = meta.postTokenBalances.first()
		val postTokenBalanceAccountIndex = postTokenBalance.accountIndex

		val matchingPreTokenBalanceAmount = meta
			.preTokenBalances
			// if matching token account is not found, that means before this transaction it did not exist,
			// and so we can assume 0 amount
			.find { it.accountIndex == postTokenBalanceAccountIndex }
			?.uiTokenAmount
			?.amount
			?.toBigInteger()
			?: BigInteger.ZERO

		val tokenDecimals = postTokenBalance.uiTokenAmount.decimals
		val diff = postTokenBalance.uiTokenAmount.amount.toBigInteger() - matchingPreTokenBalanceAmount

		val tokenBaseAmount = diff.abs()
		val tokenNativeAmount = tokenBaseAmount.asBaseAmount().toNative(tokenDecimals)

		val tokenPrice = getTokenPrices.getSingle(tokenAddress.toChainAddress(Chain.SOLANA))
		val tokenValueInLamports = tokenNativeAmount.amount * tokenPrice.amount.toBigDecimal()

		return TransferTokenTransactionSuccess(
			amountIn = tokenValueInLamports.toBigInteger(),
			amountOut = tokenBaseAmount,
		)
	}

	private fun ConfirmedTransaction.isBuySellSuccessTransaction() =
		TokensSuccessfullySwappedEvent.containedInTransaction(this)

	data class TokensSuccessfullySwappedEvent(
		val tokenIn: AddressWrapper,
		val tokenOut: AddressWrapper,
		val amountIn: Long,
		val amountOut: Long,
		val fee: Long,
		val referralFee: Long,
		val referralUUID: UUID,
		val balanceChange: BaseAmount,
	) {

		companion object {
			private const val SIGNATURE = "event:TokensSuccessfullySwapped"
			private val DISCRIMINATOR = getFunctionDiscriminator(SIGNATURE)
			private const val LOG_PREFIX = "Program data: "

			private fun List<String>.findEventLog() = find { log ->
				if (!log.startsWith(LOG_PREFIX)) return@find false

				Base64.getDecoder()
					.decode(log.removePrefix(LOG_PREFIX).take(12))
					.sliceArray(0..7)
					.contentEquals(DISCRIMINATOR)
			}

			fun containedInTransaction(confirmedTransaction: ConfirmedTransaction): Boolean =
				confirmedTransaction.meta?.logMessages?.findEventLog() != null

			fun fromTransaction(transaction: ConfirmedTransaction): TokensSuccessfullySwappedEvent {
				val eventLog = transaction.meta.logMessages?.findEventLog()
				requireNotNull(eventLog) { "Failed to find event log in logMessages!" }
				val logBytes = Base64.getDecoder().decode(eventLog.removePrefix(LOG_PREFIX))

				val buffer = BorshBuffer.wrap(logBytes)
				buffer.read(8) // Skip discriminator

				val tokenIn = buffer.readAddress()
				val tokenOut = buffer.readAddress()
				val amountIn = buffer.readU64()
				val amountOut = buffer.readU64()
				val fee = buffer.readU64()
				val referralFee = buffer.readU64()
				val referralUUID = buffer.readString().toUUID()

				// change of balance on wallet (used for bot trading)
				val balanceChangeOnAccount = transaction.meta.postBalances[0] - transaction.meta.preBalances[0]
				val balanceChangeBaseAmount = balanceChangeOnAccount.toBigInteger().asBaseAmount()

				return TokensSuccessfullySwappedEvent(
					tokenIn = tokenIn,
					tokenOut = tokenOut,
					amountIn = amountIn,
					amountOut = amountOut,
					fee = fee,
					referralFee = referralFee,
					referralUUID = referralUUID,
					balanceChange = balanceChangeBaseAmount,
				)
			}
		}
	}
}
