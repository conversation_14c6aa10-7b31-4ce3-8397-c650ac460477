package com.cleevio.fatbot.adapter.out.bitquery.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.time.Instant

@JsonIgnoreProperties(ignoreUnknown = true)
data class BitqueryProxyEvmTradesResponse(
	val evm: Evm,
) {

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Evm(
		val dextrades: List<DexTrade>,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class DexTrade(
		val block: Block,
		val trade: Trade,
		val transaction: Transaction,
		val chainId: String,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Block(
		val time: Instant,
		val number: String,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Trade(
		val buy: TradeSide,
		val sell: TradeSide,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class TradeSide(
		val amountInUSD: String,
		val priceInUSD: String,
		val price: String,
		val currency: Currency,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Currency(
		val smartContract: String,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Transaction(
		val index: Int,
	)
}
