package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.port.out.GetTransactionStatusChangedMessageAmounts
import com.cleevio.fatbot.tables.references.EVM_TOKEN_INFO
import com.cleevio.fatbot.tables.references.TRANSACTION
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.util.UUID

@Component
class GetTransactionStatusChangedMessageAmountsJooq(
	private val dslContext: DSLContext,
) : GetTransactionStatusChangedMessageAmounts {

	override fun invoke(transactionIds: Set<UUID>): Map<UUID, GetTransactionStatusChangedMessageAmounts.Result> {
		return dslContext.select(
			TRANSACTION.ID,
			TRANSACTION.AMOUNT_IN,
			TRANSACTION.AMOUNT_OUT,
			TRANSACTION.CHAIN,
			TRANSACTION.EXCHANGE_RATE_USD,
			TRANSACTION.TYPE,
			EVM_TOKEN_INFO.DECIMALS,
			WALLET.USER_ID,
		)
			.from(TRANSACTION)
			.leftJoin(EVM_TOKEN_INFO).on(
				TRANSACTION.TOKEN_ADDRESS.eq(EVM_TOKEN_INFO.ADDRESS).and(
					TRANSACTION.CHAIN.eq(
						EVM_TOKEN_INFO.CHAIN,
					),
				),
			)
			.innerJoin(WALLET).on(TRANSACTION.WALLET_ID.eq(WALLET.ID))
			.where(TRANSACTION.ID.`in`(transactionIds))
			.fetch()
			.map {
				val txType = it[TRANSACTION.TYPE]!!
				val amountIn = it[TRANSACTION.AMOUNT_IN]
				val amountOut = it[TRANSACTION.AMOUNT_OUT]
				val exchangeRate = it[TRANSACTION.EXCHANGE_RATE_USD]
				val chain = it[TRANSACTION.CHAIN]!!

				val currencyBaseAmount = determineCoinBaseAmountOrNull(txType, amountIn = amountIn, amountOut = amountOut)
				val currencyNativeAmount = currencyBaseAmount?.toNative(chain)
				val currencyAmountUsd = if (currencyNativeAmount != null && exchangeRate != null) {
					currencyNativeAmount.amount * exchangeRate
				} else {
					null
				}

				it[TRANSACTION.ID]!! to
					GetTransactionStatusChangedMessageAmounts.Result(
						userId = it[WALLET.USER_ID]!!,
						tokenNativeAmount = determineTokenNativeAmountOrNull(
							transactionType = it[TRANSACTION.TYPE]!!,
							amountIn = it[TRANSACTION.AMOUNT_IN]!!,
							amountOut = it[TRANSACTION.AMOUNT_OUT]!!,
							tokenDecimals = it[EVM_TOKEN_INFO.DECIMALS]?.toInt(),
						),
						currencyNativeAmount = currencyNativeAmount,
						currencyAmountUsd = currencyAmountUsd,
					)
			}.toMap()
	}

	private fun determineTokenNativeAmountOrNull(
		tokenDecimals: Int?,
		transactionType: TransactionType,
		amountIn: BigDecimal?,
		amountOut: BigDecimal?,
	): NativeAmount? {
		val amount = when (transactionType) {
			TransactionType.BUY -> amountOut
			TransactionType.SELL -> amountIn
			TransactionType.TRANSFER_TOKEN -> amountOut
			TransactionType.APPROVE,
			TransactionType.CLAIM_REFERRAL_REWARD,
			TransactionType.TRANSFER_CURRENCY,
			-> null
		}
		val baseAmount = amount?.toBigInteger()?.asBaseAmount()
		return baseAmount?.toNative(tokenDecimals!!)
	}

	private fun determineCoinBaseAmountOrNull(
		transactionType: TransactionType,
		amountIn: BigDecimal?,
		amountOut: BigDecimal?,
	): BaseAmount? = when (transactionType) {
		TransactionType.BUY -> amountIn
		TransactionType.SELL -> amountOut
		TransactionType.TRANSFER_CURRENCY -> amountOut
		TransactionType.TRANSFER_TOKEN -> amountIn
		TransactionType.APPROVE,
		TransactionType.CLAIM_REFERRAL_REWARD,
		-> null
	}?.toBigInteger()?.asBaseAmount()
}
