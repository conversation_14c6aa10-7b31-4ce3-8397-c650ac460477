package com.cleevio.fatbot.adapter.out.etherscan

import com.cleevio.fatbot.adapter.out.BaseConnector
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.stereotype.Component
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.toJavaDuration

@Component
class EtherscanConnector(
	@Value("\${integration.etherscan.base-url}") private val baseUrl: String,
	@Value("\${integration.etherscan.api-key}") private val apiKey: String,
) : BaseConnector(
	baseUrl = baseUrl,
	restClientCustomizer = {},
) {

	companion object {
		const val ETHERSCAN_RATE_LIMIT_SIZE = 5
		val ETHERSCAN_RATE_LIMIT_COOLDOWN = 1000.milliseconds.toJavaDuration()
	}

	@SentrySpan
	fun getSourceCode(tokenAddress: AddressWrapper, chainId: Long): GetSourceCodeResponse = restClient
		.get()
		.uri { uriBuilder ->
			uriBuilder.path("/v2/api")
				.queryParam(CHAIN_ID_QUERY_PARAM, chainId)
				.queryParam(MODULE_QUERY_PARAM, "contract")
				.queryParam(ACTION_QUERY_PARAM, "getsourcecode")
				.queryParam(TOKEN_ADDRESS_QUERY_PARAM, tokenAddress.getAddressString())
				.queryParam(API_KEY_QUERY_PARAM, apiKey)
				.build()
		}
		.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
		.retrieveResponseWithErrorHandler<GetSourceCodeResponse>()

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class GetSourceCodeResponse(val result: List<GetSourceCodeResultResponse>)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class GetSourceCodeResultResponse(
		@JsonProperty("SourceCode") val sourceCode: String,
		@JsonProperty("Proxy") val proxy: String,
		@JsonProperty("ABI") val abi: String,
		@JsonProperty("Implementation") val implementation: String,
	) {
		fun isProxy() = proxy == "1"
	}
}

private const val CHAIN_ID_QUERY_PARAM = "chainid"
private const val TOKEN_ADDRESS_QUERY_PARAM = "address"
private const val MODULE_QUERY_PARAM = "module"
private const val ACTION_QUERY_PARAM = "action"
private const val API_KEY_QUERY_PARAM = "apikey"
