package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.userstatistics.port.out.GetUserTradeDays
import com.cleevio.fatbot.tables.references.TRANSACTION
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.util.UUID

private val TRANSACTION_CREATED_AT = TRANSACTION.CREATED_AT.cast(LocalDate::class.java)

@Component
class GetUserTradeDaysJooq(
	private val dslContext: DSLContext,
) : GetUserTradeDays {
	override operator fun invoke(userId: UUID, startDate: LocalDate, endDate: LocalDate): GetUserTradeDays.Result {
		val tradeDays = dslContext.selectDistinct(
			TRANSACTION_CREATED_AT,
		)
			.from(TRANSACTION)
			.innerJoin(WALLET).on(TRANSACTION.WALLET_ID.eq(WALLET.ID))
			.where(
				TRANSACTION_CREATED_AT.greaterOrEqual(startDate),
				TRANSACTION_CREATED_AT.lessOrEqual(endDate),
				WALLET.USER_ID.eq(userId),
			)
			.fetch()
			.map {
				it[TRANSACTION_CREATED_AT]
			}

		return GetUserTradeDays.Result(tradeDays = tradeDays)
	}
}
