package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.module.botwallet.port.out.GetBotWallets
import com.cleevio.fatbot.tables.references.BOT
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class GetBotWalletsJooq(
	private val dsl: DSLContext,
) : GetBotWallets {

	override operator fun invoke(userId: UUID, botId: UUID?): List<GetBotWallets.Result> {
		val conditions = listOfNotNull(
			BOT.USER_ID.eq(userId),
			botId?.let { BOT.ID.eq(it) },
		)

		return dsl.select(
			BOT.ID,
			BOT_WALLET.ID,
			BOT_WALLET.ADDRESS,
			BOT_WALLET.BALANCE,
			BOT_WALLET.ACQUISITION_VALUE_USD,
			BOT_WALLET.CHAIN,
			BOT_WALLET.LATEST_SIGNATURE,
		)
			.from(BOT)
			.join(BOT_WALLET)
			.on(BOT_WALLET.BOT_ID.eq(BOT.ID))
			.where(conditions)
			.fetch()
			.map {
				GetBotWallets.Result(
					botId = it[BOT.ID]!!,
					id = it[BOT_WALLET.ID]!!,
					address = it[BOT_WALLET.ADDRESS]!!,
					balance = it[BOT_WALLET.BALANCE]!!.toBigInteger().asBaseAmount(),
					acquisitionValueUsd = it[BOT_WALLET.ACQUISITION_VALUE_USD]!!,
					chain = it[BOT_WALLET.CHAIN]!!,
					latestSignature = it[BOT_WALLET.LATEST_SIGNATURE],
				)
			}
	}
}
