package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollAsc
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.adapter.`in`.toInfiniteScrollSlice
import com.cleevio.fatbot.application.module.userleaderbord.port.out.SearchUserLeaderboard
import com.cleevio.fatbot.tables.references.FIREBASE_USER
import com.cleevio.fatbot.tables.references.USER_LEADERBOARD
import com.cleevio.fatbot.tables.references.USER_STATISTICS
import org.jooq.DSLContext
import org.springframework.stereotype.Component

@Component
class SearchUserLeaderboardJooq(
	private val dslContext: DSLContext,
) : SearchUserLeaderboard {

	override operator fun invoke(
		infiniteScroll: InfiniteScrollAsc.Integer,
	): InfiniteScrollSlice<SearchUserLeaderboard.Result, Int> {
		return dslContext
			.select(
				USER_LEADERBOARD.USER_ID,
				USER_LEADERBOARD.RANK,
				USER_LEADERBOARD.DONUT_MULTIPLIER,
				USER_STATISTICS.LEADERBOARD_DONUTS,
				USER_STATISTICS.LEADERBOARD_TRADED_AMOUNT,
				FIREBASE_USER.EMAIL,
			)
			.from(USER_LEADERBOARD)
			.innerJoin(USER_STATISTICS).on(USER_LEADERBOARD.USER_ID.eq(USER_STATISTICS.USER_ID))
			.innerJoin(FIREBASE_USER).on(USER_LEADERBOARD.USER_ID.eq(FIREBASE_USER.ID))
			.whereWithInfiniteScroll(
				conditions = listOf(),
				infiniteScroll = infiniteScroll,
				idFieldSelector = USER_LEADERBOARD.RANK,
			)
			.fetch()
			.map {
				SearchUserLeaderboard.Result(
					userId = it[USER_LEADERBOARD.USER_ID]!!,
					rank = it[USER_LEADERBOARD.RANK]!!,
					multiplier = it[USER_LEADERBOARD.DONUT_MULTIPLIER]!!,
					donutsGained = it[USER_STATISTICS.LEADERBOARD_DONUTS]!!,
					email = maskEmail(it[FIREBASE_USER.EMAIL]!!),
					volume = it[USER_STATISTICS.LEADERBOARD_TRADED_AMOUNT]!!,
				)
			}
			.toInfiniteScrollSlice(infiniteScroll = infiniteScroll, idSelector = { rank })
	}

	fun maskEmail(email: String): String {
		if (!email.contains('@')) error("Firebase email is not valid")

		val emailSplit = email.split('@', limit = 2)
		val username = emailSplit[0]
		val domain = emailSplit[1]

		return "${username[0]}***@${domain[0]}***"
	}
}
