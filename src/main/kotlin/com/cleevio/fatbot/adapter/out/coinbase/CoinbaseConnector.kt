package com.cleevio.fatbot.adapter.out.coinbase

import com.cleevio.fatbot.adapter.out.BaseConnector
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.fasterxml.jackson.annotation.JsonProperty
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.MediaType
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class CoinbaseConnector(
	@Value("\${integration.coinbase.base-url}")
	baseUrl: String,
) : BaseConnector(
	baseUrl = baseUrl,
) {
	@SentrySpan
	fun getUsdExchangeRate(currency: CryptoCurrency): BigDecimal = restClient.get()
		.uri {
			it
				.path("/v2/exchange-rates")
				.queryParam("currency", currency)
				.build()
		}
		.accept(MediaType.APPLICATION_JSON)
		.retrieveResponseWithErrorHandler<CoinbaseExchangeRatesResponse>()
		.data.rates.usd
}

private data class CoinbaseExchangeRatesResponse(
	val data: CoinbaseExchangeRatesData,
) {
	data class CoinbaseExchangeRatesData(
		val currency: String,
		val rates: CoinbaseExchangeRates,
	)

	data class CoinbaseExchangeRates(
		@JsonProperty("USD")
		val usd: BigDecimal,
	)
}
