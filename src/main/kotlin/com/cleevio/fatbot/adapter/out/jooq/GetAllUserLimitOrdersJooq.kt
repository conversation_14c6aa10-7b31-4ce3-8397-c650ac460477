package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.module.limitorder.port.out.GetAllUserLimitOrders
import com.cleevio.fatbot.application.module.limitorder.query.GetAllUserLimitOrdersQuery
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.tables.references.EVM_TOKEN_INFO
import com.cleevio.fatbot.tables.references.LIMIT_ORDER
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class GetAllUserLimitOrdersJooq(
	private val dslContext: DSLContext,
) : GetAllUserLimitOrders {
	override fun invoke(userId: UUID): List<GetAllUserLimitOrdersQuery.Result> {
		val limitOrderConditions = listOf(
			LIMIT_ORDER.USER_ID.eq(userId),
		)

		return dslContext.select(
			LIMIT_ORDER.ID,
			LIMIT_ORDER.WALLET_ID,
			LIMIT_ORDER.TYPE,
			LIMIT_ORDER.TOKEN_ADDRESS,
			LIMIT_ORDER.CHAIN,
			LIMIT_ORDER.LIMIT_PRICE,
			LIMIT_ORDER.REMAINING_AMOUNT,
			LIMIT_ORDER.FILLED_AMOUNT,
			LIMIT_ORDER.IS_LOCKED,
			EVM_TOKEN_INFO.DECIMALS,
		)
			.from(LIMIT_ORDER)
			.join(EVM_TOKEN_INFO)
			.on(EVM_TOKEN_INFO.ADDRESS.eq(LIMIT_ORDER.TOKEN_ADDRESS))
			.and(EVM_TOKEN_INFO.CHAIN.eq(LIMIT_ORDER.CHAIN))
			.where(limitOrderConditions)
			.fetch()
			.map {
				val remainingAmount = it[LIMIT_ORDER.REMAINING_AMOUNT]!!.toBigInteger().asBaseAmount()
				val filledAmount = it[LIMIT_ORDER.FILLED_AMOUNT]!!.toBigInteger().asBaseAmount()
				val type = it[LIMIT_ORDER.TYPE]!!
				val chain = it[LIMIT_ORDER.CHAIN]!!
				val tokenDecimals = it[EVM_TOKEN_INFO.DECIMALS]!!.intValueExact()

				val amountDecimals = when (type) {
					LimitOrderType.BUY -> chain.currency.decimals
					LimitOrderType.SELL -> tokenDecimals
				}

				GetAllUserLimitOrdersQuery.Result(
					id = it[LIMIT_ORDER.ID]!!,
					walletId = it[LIMIT_ORDER.WALLET_ID]!!,
					type = it[LIMIT_ORDER.TYPE]!!,
					tokenAddress = it[LIMIT_ORDER.TOKEN_ADDRESS]!!,
					chain = it[LIMIT_ORDER.CHAIN]!!,
					limitPrice = it[LIMIT_ORDER.LIMIT_PRICE]!!.toBigInteger().asBaseAmount().toNative(chain),
					remainingAmount = remainingAmount.toNative(amountDecimals),
					filledAmount = filledAmount.toNative(amountDecimals),
					isLocked = it[LIMIT_ORDER.IS_LOCKED]!!,
				)
			}
	}
}
