package com.cleevio.fatbot.adapter.out.aggregator

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.botmarket.port.out.GetBotMarketPositionPriceChart
import com.cleevio.fatbot.application.module.botmarket.query.GetBotMarketPositionPriceChartQuery
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import java.time.temporal.ChronoUnit

@Component
class GetBotMarketPositionPriceChartAggregator(private val aggregatorConnector: FatbotAggregatorConnector) :
	GetBotMarketPositionPriceChart {

	@SentrySpan
	override fun invoke(tokenAddress: AddressWrapper): List<GetBotMarketPositionPriceChartQuery.Result> {
		// the graph is sorted by default from fatbot-token-aggregator where fist value is the earliest price
		val priceChart = aggregatorConnector.getTokenPriceChart(tokenAddress = tokenAddress)

		val secondToPriceChartResponses = priceChart.groupBy { it.timestamp.truncatedTo(ChronoUnit.SECONDS) }
		val priceChartSeconds = secondToPriceChartResponses.keys

		return priceChartSeconds.windowed(size = 2, step = 1, partialWindows = true) {
			val currentSecond = it.first()
			val nextSecond = it.last()

			val currentSecondPriceChartValues = secondToPriceChartResponses.getValue(currentSecond)

			val openUsd = currentSecondPriceChartValues.first().priceUsd
			val highUsd = currentSecondPriceChartValues.maxBy { it.priceUsd }.priceUsd
			val lowUsd = currentSecondPriceChartValues.minBy { it.priceUsd }.priceUsd

			val closeUsd = if (currentSecond == nextSecond) {
				// on partial window, these are the same, meaning we are constructing the last candle
				currentSecondPriceChartValues.last().priceUsd
			} else {
				// when there is next second, we use it's first price as close for the candle
				val nextSecondPriceChartValues = secondToPriceChartResponses.getValue(nextSecond)
				nextSecondPriceChartValues.first().priceUsd
			}

			GetBotMarketPositionPriceChartQuery.Result(
				timestamp = currentSecond,
				openUsd = openUsd,
				highUsd = highUsd,
				lowUsd = lowUsd,
				closeUsd = closeUsd,
			)
		}
	}
}
