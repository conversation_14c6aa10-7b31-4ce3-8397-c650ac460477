package com.cleevio.fatbot.adapter.out.solana.raydium

import com.cleevio.fatbot.adapter.out.solana.client.FatbotTsProxyConnector
import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalances
import com.cleevio.fatbot.adapter.out.solana.model.PoolReserves
import com.cleevio.fatbot.adapter.out.solana.raydium.util.getCPMMPoolVaultPda
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.WSOL_MINT_ADDRESS
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutRaydium
import com.cleevio.fatbot.domain.token.RaydiumAmmMarketData
import com.cleevio.fatbot.domain.token.RaydiumCPMMMarketData
import com.cleevio.fatbot.domain.token.getTradeFeeBp
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class GetAmountOutRaydiumSolana(
	private val rpcClient: RpcClient,
	private val fatbotTsProxyConnector: FatbotTsProxyConnector,
) : GetAmountOutRaydium {

	/* -------- CLMM (Concentrated Liquidity Market Maker) --------
	- Ticks for amountOut calculation
	 */

	override fun getAmountOutCLMM(
		amount: BaseAmount,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		isBuy: Boolean,
	): BaseAmount {
		val inputTokenMint = if (isBuy) WSOL_MINT_ADDRESS else tokenAddress

		val result = fatbotTsProxyConnector.computeSwapCLMM(
			poolAddress = pairAddress,
			inputTokenMint = inputTokenMint,
			inputAmount = amount,
		)

		return result.expectedAmountOut.asBaseAmount()
	}

	/* -------- CPMM (Constant Product Market Maker) --------
	- Constant product formula for amountOut calculation
	- Dynamic trade fee
	 */

	override fun getAmountOutCPMM(
		amount: BaseAmount,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		marketData: RaydiumCPMMMarketData,
		isBuy: Boolean,
	): BaseAmount {
		val reserves = getReservesCPMM(tokenAddress, pairAddress)

		return fromReservesCPMM(amount, reserves, marketData.getTradeFeeBp(), isBuy)
	}

	override fun fromReservesCPMM(
		amount: BaseAmount,
		poolReserves: PoolReserves,
		tradeFeeBp: BasisPoint,
		isBuy: Boolean,
	): BaseAmount {
		val amountOut = fromReservesConstantProduct(
			amount = amount,
			poolReserves = poolReserves,
			feeBp = tradeFeeBp,
			isBuy = isBuy,
		)

		return amountOut
	}

	override fun getReservesCPMM(tokenAddress: AddressWrapper, pairAddress: AddressWrapper): PoolReserves {
		val tokenVault = getCPMMPoolVaultPda(pairAddress.getSolanaPublicKey(), tokenAddress.getSolanaPublicKey())
		val wsolVault = getCPMMPoolVaultPda(pairAddress.getSolanaPublicKey(), SolanaConstants.WSOL_MINT)

		return getPoolReserves(wsolVault, tokenVault)
	}

	/* -------- AMM (Automated Market Maker) --------
	- Constant product formula for amountOut calculation
	- Fixed trade fee
	 */

	override fun getAmountOutAMM(
		amount: BaseAmount,
		pairAddress: AddressWrapper,
		marketData: RaydiumAmmMarketData,
		isBuy: Boolean,
	): BaseAmount {
		val reserves = getReservesAMM(marketData)

		return fromReservesAMM(amount, reserves, isBuy)
	}

	override fun fromReservesAMM(amount: BaseAmount, poolReserves: PoolReserves, isBuy: Boolean): BaseAmount {
		val amountOut = fromReservesConstantProduct(
			amount = amount,
			poolReserves = poolReserves,
			feeBp = SolanaConstants.RAYDIUM_AMM_LP_FEE_BP,
			isBuy = isBuy,
		)

		return amountOut
	}

	override fun getReservesAMM(marketData: RaydiumAmmMarketData): PoolReserves {
		return getPoolReserves(
			wsolVault = marketData.wsolVault().getSolanaPublicKey(),
			tokenVault = marketData.tokenVault().getSolanaPublicKey(),
		)
	}

	// -------- Constant Product Helpers --------

	private fun fromReservesConstantProduct(
		amount: BaseAmount,
		poolReserves: PoolReserves,
		feeBp: BasisPoint,
		isBuy: Boolean,
	): BaseAmount {
		val (wsolReserves, tokenReserves) = poolReserves
		val (reserveIn, reserveOut) = if (isBuy) wsolReserves to tokenReserves else tokenReserves to wsolReserves

		val amountOut = calculateAmountOutConstantProduct(
			amount = amount,
			reserveIn = reserveIn,
			reserveOut = reserveOut,
			feeBp = feeBp,
		)

		return amountOut
	}

	private fun calculateAmountOutConstantProduct(
		amount: BaseAmount,
		reserveIn: BaseAmount,
		reserveOut: BaseAmount,
		feeBp: BasisPoint,
	): BaseAmount {
		val fee = feeBp.applyOnCeilDiv(amount.amount)
		val amountInAfterFee = amount.amount - fee

		val amountOut = (reserveOut.amount * amountInAfterFee) / (reserveIn.amount + amountInAfterFee)

		return amountOut.asBaseAmount()
	}

	private fun getPoolReserves(wsolVault: PublicKey, tokenVault: PublicKey): PoolReserves {
		val (wsolReserves, tokenReserves) = rpcClient.getTokenAccountBalances(
			listOf(wsolVault, tokenVault),
			Commitment.CONFIRMED,
		)

		requireNotNull(tokenReserves) { "Received null token reserves for tokenVault: $tokenVault" }
		requireNotNull(wsolReserves) { "Received null wsol reserves for wsolVault: $wsolVault" }
		require(tokenReserves != BigInteger.ZERO) { "Received 0 token reserves for tokenVault: $tokenVault" }
		require(wsolReserves != BigInteger.ZERO) { "Received 0 wsol reserves for wsolVault: $wsolVault" }

		return PoolReserves(wsolReserves.asBaseAmount(), tokenReserves.asBaseAmount())
	}
}
