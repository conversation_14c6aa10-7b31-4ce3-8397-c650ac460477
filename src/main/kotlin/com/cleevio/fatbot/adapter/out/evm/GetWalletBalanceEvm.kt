package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component

@Component
class GetWalletBalanceEvm(
	private val evmChainContextFactory: EvmChainContextFactory,
) {

	@SentrySpan
	operator fun invoke(address: AddressWrapper, chainId: Long): BaseAmount {
		val balance = evmChainContextFactory.ofChainId(chainId) {
			client.getWalletBalance(walletAddress = address)
		}

		return balance.asBaseAmount()
	}

	fun getMany(addresses: List<AddressWrapper>, chainId: Long): Map<AddressWrapper, BaseAmount> {
		val balances = evmChainContextFactory.ofChainId(chainId) { client.getWalletBalances(addresses) }

		val addressToBalance = addresses.zip(balances) { address, balance ->
			address to balance.asBaseAmount()
		}.toMap()

		return addressToBalance
	}
}
