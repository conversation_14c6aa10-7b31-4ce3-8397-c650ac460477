package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.user.exception.UserNotFoundException
import com.cleevio.fatbot.application.module.userleaderbord.port.out.GetUserLeaderboardInfo
import com.cleevio.fatbot.tables.references.USER_LEADERBOARD
import com.cleevio.fatbot.tables.references.USER_STATISTICS
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.util.UUID

@Component
class GetUserLeaderboardInfoJooq(private val dslContext: DSLContext) : GetUserLeaderboardInfo {
	override operator fun invoke(userId: UUID): GetUserLeaderboardInfo.Result {
		return dslContext
			.select(
				USER_STATISTICS.USER_ID,
				USER_STATISTICS.LEADERBOARD_DONUTS,
				USER_STATISTICS.DAYS_IN_STREAK,
				USER_LEADERBOARD.DONUT_MULTIPLIER,
				USER_LEADERBOARD.RANK,
				USER_STATISTICS.LEADERBOARD_TRADED_AMOUNT,
			)
			.from(USER_STATISTICS)
			.leftJoin(USER_LEADERBOARD).on(USER_LEADERBOARD.USER_ID.eq(USER_STATISTICS.USER_ID))
			.where(USER_STATISTICS.USER_ID.eq(userId))
			.fetchOne()
			?.let {
				GetUserLeaderboardInfo.Result(
					donutsGained = it[USER_STATISTICS.LEADERBOARD_DONUTS]!!,
					donutMultiplier = it[USER_LEADERBOARD.DONUT_MULTIPLIER] ?: BigDecimal.ONE,
					rank = it[USER_LEADERBOARD.RANK],
					daysInStreak = it[USER_STATISTICS.DAYS_IN_STREAK]!!,
					volume = it[USER_STATISTICS.LEADERBOARD_TRADED_AMOUNT]!!,
				)
			} ?: throw UserNotFoundException("User not found.")
	}
}
