package com.cleevio.fatbot.adapter.out.evm.cache

import com.cleevio.fatbot.application.common.util.mapToSet
import com.github.benmanes.caffeine.cache.AsyncCacheLoader
import org.slf4j.LoggerFactory
import reactor.core.publisher.Sinks
import reactor.core.publisher.Sinks.EmitResult
import reactor.core.publisher.Sinks.Many
import reactor.core.scheduler.Schedulers
import java.time.Duration
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executor
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * Code taken from: https://github.com/ben-manes/caffeine/issues/7
 *
 * Transformed to Kotlin a removed mapping function to use abstract function instead
 */
abstract class CoalescingBulkLoader<K, V>(
	maxSize: Int,
	maxTime: Duration,
	parallelism: Int,
) : AsyncCacheLoader<K, V> {

	private val logger = LoggerFactory.getLogger(this::class.java)

	private val sinkAccessLock = ReentrantLock()

	// many -> unicast
	// many requests can come and only one subscriber will handle them
	private val sink: Many<Request<K, V>> = Sinks.many().unicast().onBackpressureBuffer()

	init {
		// this will be called when max size of Requests is hit, or max time passed
		val onNextHandler = { requests: List<Request<K, V>> -> this.handle(requests) }

		// this will be called if there would be unhandled exception in onNextHandler
		// note that if this error handler is called, the sink is closed after!
		// Ideally it would be never called
		val onErrorHandler = { ex: Throwable -> logger.info("Unexpected error happened in sink: $ex") }

		sink.asFlux()
			.bufferTimeout(maxSize, maxTime)
			.parallel(parallelism)
			.runOn(Schedulers.boundedElastic())
			.subscribe(onNextHandler, onErrorHandler)
	}

	/**
	 * This function is called whenever async cache needs new element
	 */
	override fun asyncLoad(key: K, executor: Executor): CompletableFuture<V> {
		// This object represents the future value that will be returned to Caffeine.
		// Each same-key invocation after (while this key is being loaded) will be handed this future
		// by Caffeine and will not call this function again (thus we don't need to handle duplicates at this point)
		val valueFuture = CompletableFuture<V>()

		// We have to make sure that access to emitting next element is thread safe
		// in case we did not do this, requests would be silently dropped
		sinkAccessLock.withLock {
			// we assume failure handler will never be needed this way, however we add it just to log
			// any unexpected cases (exceptions are silently dropped)
			sink.emitNext(Request(key, valueFuture), failureHandler)
		}

		return valueFuture
	}

	abstract fun getResults(keys: Set<K>): Map<K, V>

	/**
	 * This function is triggered when either `maxTime` passed (since first request came) or `maxSize` was hit.
	 * So this means the size of `requests` must be in `1 .. maxSize`
	 *
	 * There will be no duplicates in `requests`
	 */
	private fun handle(requests: List<Request<K, V>>) {
		val keys = requests.mapToSet { it.key }
		runCatching {
			// At this point we have our values for keys, and we have to now map them to correct
			// CompletableFuture that we passed to Caffeine
			val results = getResults(keys)
			requests.forEach { (key, valueFuture) -> valueFuture.complete(results[key]) }
		}
			.onFailure { ex ->
				// If exception happens in `getResults` we just pass it to the value future
				// We can not throw any exception from `handle` method as that would cancel sink
				requests.forEach { (_, valueFuture) -> valueFuture.completeExceptionally(ex) }
			}
	}

	private val failureHandler: Sinks.EmitFailureHandler = Sinks.EmitFailureHandler { signalType, emission ->
		logger.info("$signalType emitted result $emission")

		// If it somehow happens that thread did not acquire lock over sink, we retry
		if (emission == EmitResult.FAIL_NON_SERIALIZED) return@EmitFailureHandler true

		// In any other case, something is wrong, so we stop repeating
		false
	}

	private data class Request<K, V>(val key: K, val valueFuture: CompletableFuture<V>)
}
