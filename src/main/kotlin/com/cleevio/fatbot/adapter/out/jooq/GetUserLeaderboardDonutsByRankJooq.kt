package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.userleaderbord.port.out.GetUserLeaderboardDonutsByRank
import com.cleevio.fatbot.tables.references.USER_LEADERBOARD
import com.cleevio.fatbot.tables.references.USER_STATISTICS
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class GetUserLeaderboardDonutsByRankJooq(
	private val dslContext: DSLContext,
) : GetUserLeaderboardDonutsByRank {
	override fun invoke(rank: Int): BigDecimal {
		return dslContext
			.select(USER_STATISTICS.LEADERBOARD_DONUTS)
			.from(USER_LEADERBOARD)
			.innerJoin(USER_STATISTICS).on(USER_LEADERBOARD.USER_ID.eq(USER_STATISTICS.USER_ID))
			.where(USER_LEADERBOARD.RANK.eq(rank))
			.fetchOne()
			?.let { it[USER_STATISTICS.LEADERBOARD_DONUTS]!! } ?: BigDecimal.ZERO
	}
}
