package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.constants.EVM_MAX_UINT
import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContext
import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.adapter.out.evm.contract.FatbotRouter
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.util.toUUID
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailure
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailureReason
import com.cleevio.fatbot.application.module.transaction.constant.TransactionPending
import com.cleevio.fatbot.application.module.transaction.constant.TransactionResult
import com.cleevio.fatbot.application.module.transaction.constant.TransactionSuccess
import com.cleevio.fatbot.application.module.transaction.port.out.GetTransactionResults
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.coroutines.createJobScope
import io.sentry.spring.jakarta.tracing.SentrySpan
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.springframework.stereotype.Component
import org.web3j.protocol.core.DefaultBlockParameterNumber
import org.web3j.protocol.core.methods.request.Transaction
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt
import org.web3j.protocol.core.methods.response.TransactionReceipt
import java.math.BigInteger
import kotlin.jvm.optionals.getOrNull

private val MIN_L2_BLOCK_CONFIRMATIONS = 3.toBigInteger()

@Component
class GetTransactionResultsEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
) : GetTransactionResults {

	@SentrySpan
	override fun invoke(txHashes: Set<TxHash>, chainId: Long): Map<TxHash, TransactionResult> {
		val scope = createJobScope()

		return evmChainContextFactory.ofChainId(chainId = chainId) {
			val confirmationThresholdBlockNumberDeferred = scope.async { getConfirmationThresholdBlockNumber() }
			val batchRequest = client.getWeb3j().newBatch()

			val txHashOrder = txHashes.withIndex().associate { (index, txHash) ->
				// add request, not triggering any network communication so far
				batchRequest.add(client.getWeb3j().ethGetTransactionReceipt(txHash.txHash))
				txHash to index
			}

			// send to RPC node in one HTTP request
			val batchResponse = runCatching { batchRequest.send() }
				.getOrElse { ex -> error("Batch request failed: ${ex.message}.") }

			val confirmationThresholdBlockNumber = runBlocking { confirmationThresholdBlockNumberDeferred.await() }

			txHashOrder.mapValues { (_, index) ->
				// responses are guaranteed to be in order of requests put in Batch
				val response = batchResponse.responses[index] as EthGetTransactionReceipt
				determineTransactionStatus(response.transactionReceipt.getOrNull(), confirmationThresholdBlockNumber)
			}
		}
	}

	private fun EvmChainContext.determineTransactionStatus(
		transactionReceipt: TransactionReceipt?,
		confirmationThresholdBlockNumber: BigInteger,
	): TransactionResult {
		return when {
			transactionReceipt == null -> TransactionPending()
			transactionReceipt.blockNumber > confirmationThresholdBlockNumber -> TransactionPending()
			transactionReceipt.isStatusOK -> {
				val event = FatbotRouter.getTokensSuccessfullySwappedEvents(transactionReceipt).firstOrNull()
					?: return TransactionSuccess()

				BuySellTransactionSuccess(
					amountIn = event.amountIn,
					amountOut = event.amountOut,
					fee = event.fee,
					referralFee = event.referralFee,
					referralRewardRecipient = event.uuid.toUUID(),

					// For now this value is not used at all on EVM side and is here
					// only for need of BotTransactions
					balanceChange = BaseAmount.ZERO,
					instructionAmount = BigInteger.ZERO,
				)
			}

			else -> {
				val isFailureSlippage = determineTransactionFailureIsSlippage(
					txHash = transactionReceipt.transactionHash,
				)

				TransactionFailure(
					// For now this value is not used at all on EVM side and is here
					// only for need of BotTransactions
					balanceChange = BaseAmount.ZERO,
					isNoTokensOwnedErrorHint = null,
					failReason = when (isFailureSlippage) {
						true -> TransactionFailureReason.SLIPPAGE
						false -> TransactionFailureReason.UNDEFINED
					},
				)
			}
		}
	}

	private fun EvmChainContext.determineTransactionFailureIsSlippage(txHash: String): Boolean {
		val web3j = client.getWeb3j()

		val ethTransaction = web3j.ethGetTransactionByHash(txHash).send()
		val transaction = ethTransaction.transaction.get()

		val callTransaction = Transaction.createEthCallTransaction(
			transaction.from,
			transaction.to,
			transaction.input,
		)
		val blockParameter = DefaultBlockParameterNumber(transaction.blockNumber)
		val ethCall = web3j.ethCall(callTransaction, blockParameter).send()
		val resultMessage = ethCall.error.message

		return SLIPPAGE_ERROR_MESSAGES.any { it in resultMessage }
	}

	/**
	 * Returns the blockNumber up to which transactions can be considered confirmed / acceptable for further evaluation.
	 * If this is not required for certain chain, a [EVM_MAX_UINT] is returned.
	 *
	 * I.e. if the current blockNumber is 52 and [MIN_L2_BLOCK_CONFIRMATIONS] is 3, then all transactions before
	 * blockNumber 49 can be accepted.
	 */
	private fun EvmChainContext.getConfirmationThresholdBlockNumber(): BigInteger {
		return when (properties.chain) {
			Chain.EVM_BASE, Chain.EVM_ARBITRUM_ONE -> {
				client.getWeb3j().ethBlockNumber().send().blockNumber - MIN_L2_BLOCK_CONFIRMATIONS
			}
			// No requirement for L1 only chains
			else -> EVM_MAX_UINT
		}
	}
}

private val SLIPPAGE_ERROR_MESSAGES =
	listOf("INSUFFICIENT_OUTPUT_AMOUNT", "Too little received", "Price slippage check")
