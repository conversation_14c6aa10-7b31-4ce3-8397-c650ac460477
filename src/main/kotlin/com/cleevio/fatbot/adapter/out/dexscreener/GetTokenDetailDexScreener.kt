package com.cleevio.fatbot.adapter.out.dexscreener

import com.cleevio.fatbot.adapter.out.dexscreener.response.GetTokenPairsInfoResponse
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.EpochClockTicker
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.port.out.GetTokenDecimals
import com.cleevio.fatbot.application.module.market.port.out.GetTokenDetailFromDexScreener
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.market.port.out.invoke
import com.cleevio.fatbot.application.module.market.query.GetTokenDetailQuery
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.token.event.TokenOnDexScreenerFoundEvent
import com.cleevio.fatbot.application.module.token.event.TokenPairFoundEvent
import com.cleevio.fatbot.application.module.tokenpricesnapshot.port.out.GetTokenPriceSnapshots
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.FatbotDispatchers
import com.cleevio.fatbot.infrastructure.coroutines.createSupervisorJobScope
import com.github.benmanes.caffeine.cache.Caffeine
import io.sentry.spring.jakarta.tracing.SentrySpan
import io.sentry.spring.jakarta.tracing.SentryTransaction
import kotlinx.coroutines.asExecutor
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.concurrent.CompletableFuture

@Component
class GetTokenDetailDexScreener(
	private val dexScreenerConnector: DexScreenerConnector,
	private val dexScreenerPairInfoParser: DexScreenerPairInfoParser,
	private val getTokenDecimals: List<GetTokenDecimals>,
	private val getTokenPrices: GetTokenPrices,
	private val getTokenPriceSnapshots: GetTokenPriceSnapshots,
	private val getDex: List<GetDex>,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val usdConverter: UsdConverter,
	private val clock: Clock,
	fatbotDispatchers: FatbotDispatchers,
) : GetTokenDetailFromDexScreener {

	@Lazy
	@Autowired
	private lateinit var self: GetTokenDetailDexScreener

	private val cache = Caffeine
		.newBuilder()
		.maximumSize(5000)
		.refreshAfterWrite(Duration.of(45, ChronoUnit.SECONDS))
		.expireAfterWrite(Duration.of(60, ChronoUnit.SECONDS))
		.ticker(EpochClockTicker(clock))
		.executor(fatbotDispatchers.Caffeine.asExecutor())
		.buildAsync<AddressWrapper, Map<Chain, GetTokenPairsInfoResponse.Pair>> { key, executor ->
			CompletableFuture.supplyAsync({ self.getTokenPairs(tokenAddress = key) }, executor)
		}

	@SentrySpan
	override fun invoke(token: ChainAddress): GetTokenDetailQuery.DexDetail? {
		val tokenPairsFuture = cache.get(token.address)

		// Check if we already fetched data for token and no pair was found
		if (tokenPairsFuture.isDone && token.chain !in tokenPairsFuture.join()) return null

		val scope = createSupervisorJobScope()
		val tokenDecimalsDeferred = scope.async { getTokenDecimals(token = token) }
		val exchangeRateDeferred = scope.async { getUsdExchangeRate(cryptoCurrency = token.chain.currency) }
		val tokenPriceDeferred = scope.async { getTokenPrices.getSingleWithoutCoalescing(token) }
		val tokenPrice24hAgoDeferred = scope.async {
			getTokenPriceSnapshots.getSingleOrNull(token, Instant.now(clock).minus(24, ChronoUnit.HOURS))
		}

		val tokenPair = tokenPairsFuture.join()[token.chain] ?: return null

		val dexDetail = runBlocking {
			val pairAddress = AddressWrapper(tokenPair.pairAddress)

			val dexId = tokenPair.dexId
			val version = tokenPair.labels.toVersionOrNull(token.chain)
			val raydiumPool = tokenPair.toRaydiumPoolOrNull()

			val dexPairInfo = getDex(
				dexId = dexId,
				pairAddress = pairAddress,
				version = version,
				chain = token.chain,
				poolType = raydiumPool,
			)

			val tokenDecimals = tokenDecimalsDeferred.await()
			applicationEventPublisher.publishEvent(
				TokenPairFoundEvent(
					chain = token.chain,
					dex = dexPairInfo,
					tokenAddress = token.address,
					tokenDecimals = tokenDecimals,
				),
			)

			applicationEventPublisher.publishEvent(
				TokenOnDexScreenerFoundEvent(
					chain = token.chain,
					tokenAddress = token.address,
					pairAddress = pairAddress,
					decimals = tokenDecimals,
					name = tokenPair.baseToken.name,
					symbol = tokenPair.baseToken.symbol,
					imageUrl = tokenPair.info?.imageUrl,
				),
			)

			// Retry price load as Solana tokens require TokenPairFoundEvent to save pair data in the DB on first discovery
			val tokenPriceNative = runCatching { tokenPriceDeferred.await() }
				.getOrElse { getTokenPrices.getSingle(token) }
				.toNative(token.chain)

			val exchangeRate = exchangeRateDeferred.await()

			val tokenPriceUsd = usdConverter.nativeToUsd(tokenPriceNative, token.chain, exchangeRate)

			val tokenPrice24hAgo = tokenPrice24hAgoDeferred.await()
			val tokenPriceUsd24hAgo = tokenPrice24hAgo?.let {
				usdConverter.baseToUsd(it.price, token.chain, it.exchangeRateUsd)
			}

			GetTokenDetailQuery.DexDetail(
				tokenDecimals = tokenDecimals,
				dexInfo = tokenPair.toDexInfo(
					pairInfo = tokenPair.info,
					dexPairInfo = dexPairInfo,
					priceNative = tokenPriceNative.amount,
					priceUsd = tokenPriceUsd,
					priceUsd24hAgo = tokenPriceUsd24hAgo,
				),
			)
		}

		return dexDetail
	}

	// Cant be private so proxy object works
	@SentryTransaction(operation = "async.get-token-pairs")
	fun getTokenPairs(tokenAddress: AddressWrapper): Map<Chain, GetTokenPairsInfoResponse.Pair> {
		val allTokenPairs = dexScreenerConnector.getTokenPairsInfoByAddress(token = tokenAddress)

		val chainToTokenPair = dexScreenerPairInfoParser.extractTokenPairs(tokenAddress, allTokenPairs)

		return chainToTokenPair
	}
}

private fun GetTokenPairsInfoResponse.Pair.toDexInfo(
	dexPairInfo: GetDex.Result,
	pairInfo: GetTokenPairsInfoResponse.Pair.Info?,
	priceNative: BigDecimal,
	priceUsd: BigDecimal,
	priceUsd24hAgo: BigDecimal?,
): GetTokenDetailQuery.DexInfo {
	val change24h = priceUsd24hAgo?.let { _ ->
		calculateFractionChange(priceUsd, priceUsd24hAgo)
	}

	return GetTokenDetailQuery.DexInfo(
		chainId = chainId,
		url = url,
		dexPairInfo = dexPairInfo,
		baseToken = GetTokenDetailQuery.DexInfo.Token(
			address = baseToken.address,
			name = baseToken.name,
			symbol = baseToken.symbol,
		),
		quoteToken = GetTokenDetailQuery.DexInfo.Token(
			address = quoteToken.address,
			name = quoteToken.name,
			symbol = quoteToken.symbol,
		),
		priceUsd = priceUsd,
		priceNative = priceNative,
		volume = GetTokenDetailQuery.DexInfo.TimeIntervals(
			hours24 = volume.hours24,
			hours06 = volume.hours06,
			hours01 = volume.hours01,
			minutes05 = volume.minutes05,
		),
		priceChange = GetTokenDetailQuery.DexInfo.TimeIntervals(
			// DexScreener returns change in %, so we convert to a fraction
			hours24 = change24h ?: priceChange.hours24?.scaleByPowerOfTen(-2),
			hours06 = priceChange.hours06?.scaleByPowerOfTen(-2),
			hours01 = priceChange.hours01?.scaleByPowerOfTen(-2),
			minutes05 = priceChange.minutes05?.scaleByPowerOfTen(-2),
		),
		fullyDilutedValue = fullyDilutedValue,
		marketCap = marketCap,
		liquidity = liquidity?.let {
			GetTokenDetailQuery.DexInfo.Liquidity(
				usd = it.usd,
				base = it.base,
				quote = it.quote,
			)
		},
		buys = GetTokenDetailQuery.DexInfo.TimeIntervals(
			hours24 = transactions.hours24.buys.toBigDecimal(),
			hours06 = transactions.hours06.buys.toBigDecimal(),
			hours01 = transactions.hours01.buys.toBigDecimal(),
			minutes05 = transactions.minutes05.buys.toBigDecimal(),
		),
		sells = GetTokenDetailQuery.DexInfo.TimeIntervals(
			hours24 = transactions.hours24.sells.toBigDecimal(),
			hours06 = transactions.hours06.sells.toBigDecimal(),
			hours01 = transactions.hours01.sells.toBigDecimal(),
			minutes05 = transactions.minutes05.sells.toBigDecimal(),
		),
		pairCreatedAt = Instant.ofEpochMilli(pairCreatedAtMillis),
		info = pairInfo?.let {
			GetTokenDetailQuery.DexInfo.Info(
				imageUrl = it.imageUrl,
				websites = it.websites.map { web ->
					GetTokenDetailQuery.DexInfo.Info.Website(
						label = web.label,
						url = web.url,
					)
				},
				socials = it.socials.map { social ->
					GetTokenDetailQuery.DexInfo.Info.Social(
						type = social.type,
						url = social.url,
					)
				},
			)
		},
	)
}

private fun List<String>?.toVersionOrNull(chain: Chain) = when (this?.firstOrNull()) {
	"v2" -> GetDex.Version.V2
	"v3" -> GetDex.Version.V3
	null -> if (chain == Chain.EVM_ARBITRUM_ONE) GetDex.Version.V3 else null
	else -> null
}

private fun GetTokenPairsInfoResponse.Pair.toRaydiumPoolOrNull(): GetDex.PoolType? {
	if (dexId != SOLANA_RAYDIUM_DEX_ID) return null

	return when (labels?.firstOrNull()) {
		"CLMM" -> GetDex.PoolType.CLMM
		"CPMM" -> GetDex.PoolType.CPMM
		null -> GetDex.PoolType.AMM
		else -> null
	}
}
