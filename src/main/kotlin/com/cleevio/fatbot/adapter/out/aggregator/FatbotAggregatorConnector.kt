package com.cleevio.fatbot.adapter.out.aggregator

import com.cleevio.fatbot.adapter.out.BaseConnector
import com.cleevio.fatbot.adapter.out.aggregator.response.GetTokenPriceChartResponse
import com.cleevio.fatbot.adapter.out.aggregator.response.GetTokenStateResponse
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.infrastructure.config.properties.FatbotApiProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Component

@Component
class FatbotAggregatorConnector(
	private val fatbotApiProperties: FatbotApiProperties,
	@Value("\${integration.fatbot-token-aggregator.base-url}") private val baseUrl: String,
) : BaseConnector(
	baseUrl = baseUrl,
	restClientCustomizer = { it.defaultHeader(HttpHeaders.AUTHORIZATION, fatbotApiProperties.apiKey.toString()) },
) {

	@SentrySpan
	fun getTokenState(tokenAddress: AddressWrapper) = restClient
		.get()
		.uri("/internal/tokens/${tokenAddress.getAddressString()}")
		.retrieveResponseWithErrorHandler<GetTokenStateResponse>()

	@SentrySpan
	fun searchTokenStates(tokenAddresses: Set<AddressWrapper>) = restClient
		.post()
		.uri("/internal/tokens/search")
		.body(SearchTokenStatesRequest(tokenAddresses = tokenAddresses))
		.retrieveResponseWithErrorHandler<List<GetTokenStateResponse>>()

	@SentrySpan
	fun getTokenPriceChart(tokenAddress: AddressWrapper) = restClient
		.get()
		.uri("/internal/tokens/${tokenAddress.getAddressString()}/price-chart")
		.retrieveResponseWithErrorHandler<List<GetTokenPriceChartResponse>>()
}

data class SearchTokenStatesRequest(
	val tokenAddresses: Set<AddressWrapper>,
)
