package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.limitorder.port.out.MatchLimitOrders
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.tables.references.LIMIT_ORDER
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.jooq.Record9
import org.jooq.SelectUnionStep
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.util.UUID

private typealias MatchLimitOrderQuery =
	SelectUnionStep<
		Record9<
			UUID?,
			UUID?,
			UUID?,
			AddressWrapper?,
			AddressWrapper?,
			BigDecimal?,
			LimitOrderType?,
			BigDecimal?,
			BigDecimal?,
			>,
		>

@Component
class MatchLimitOrdersJooq(
	private val dslContext: DSLContext,
) : MatchLimitOrders {

	override fun invoke(tokenToPrice: Map<AddressWrapper, BigDecimal>): List<MatchLimitOrders.Result> {
		val queries: List<MatchLimitOrderQuery> = tokenToPrice.map { (tokenAddress, price) ->
			val nativePrice = price.asNativeAmount().toBase(Chain.SOLANA).amount.toBigDecimal()

			val priceCondition = DSL
				.`when`(LIMIT_ORDER.TYPE.eq(LimitOrderType.BUY), LIMIT_ORDER.LIMIT_PRICE.gt(nativePrice))
				.`when`(LIMIT_ORDER.TYPE.eq(LimitOrderType.SELL), LIMIT_ORDER.LIMIT_PRICE.lt(nativePrice))
				.isTrue

			val conditions = listOf(
				LIMIT_ORDER.TOKEN_ADDRESS.eq(tokenAddress),
				LIMIT_ORDER.CHAIN.eq(Chain.SOLANA),
				priceCondition,
				LIMIT_ORDER.REMAINING_AMOUNT.gt(BigDecimal.ZERO),
				LIMIT_ORDER.IS_LOCKED.isFalse,
			)

			dslContext.select(
				LIMIT_ORDER.ID,
				LIMIT_ORDER.USER_ID,
				LIMIT_ORDER.WALLET_ID,
				WALLET.ADDRESS,
				LIMIT_ORDER.TOKEN_ADDRESS,
				LIMIT_ORDER.LIMIT_PRICE,
				LIMIT_ORDER.TYPE,
				LIMIT_ORDER.FILLED_AMOUNT,
				LIMIT_ORDER.REMAINING_AMOUNT,
			)
				.from(LIMIT_ORDER)
				.join(WALLET).on(WALLET.ID.eq(LIMIT_ORDER.WALLET_ID))
				.where(conditions)
		}

		val query = queries.reduce { acc, query -> acc.unionAll(query) }

		return query.fetch().map {
			MatchLimitOrders.Result(
				id = it[LIMIT_ORDER.ID]!!,
				userId = it[LIMIT_ORDER.USER_ID]!!,
				walletId = it[LIMIT_ORDER.WALLET_ID]!!,
				walletAddress = it[WALLET.ADDRESS]!!,
				tokenAddress = it[LIMIT_ORDER.TOKEN_ADDRESS]!!,
				limitPrice = it[LIMIT_ORDER.LIMIT_PRICE]!!.toBigInteger(),
				type = it[LIMIT_ORDER.TYPE]!!,
				filledAmount = it[LIMIT_ORDER.FILLED_AMOUNT]!!.toBigInteger(),
				remainingAmount = it[LIMIT_ORDER.REMAINING_AMOUNT]!!.toBigInteger(),

			)
		}
	}
}
