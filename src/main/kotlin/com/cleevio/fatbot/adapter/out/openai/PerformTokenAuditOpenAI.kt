package com.cleevio.fatbot.adapter.out.openai

import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector
import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector.Companion.ETHERSCAN_RATE_LIMIT_COOLDOWN
import com.cleevio.fatbot.adapter.out.openai.model.SystemPrompt
import com.cleevio.fatbot.adapter.out.openai.model.UserPrompt
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.util.runWithExponentialBackoffSuspend
import com.cleevio.fatbot.application.common.util.runWithRetrySuspend
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.tokenaudit.command.SaveTokenAuditCommand
import com.cleevio.fatbot.application.module.tokenaudit.port.out.PerformTokenAudit
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.infrastructure.config.FatbotDispatchers
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentrySpan
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.Lazy
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import kotlin.math.min
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds

@Component
class PerformTokenAuditOpenAI(
	private val etherscanConnector: EtherscanConnector,
	private val openAIConnector: OpenAIConnector,
	fatbotDispatchers: FatbotDispatchers,
) : PerformTokenAudit {

	@Autowired
	@Lazy
	private lateinit var commandBus: CommandBus

	private val logger = logger()
	private val scope = CoroutineScope(
		fatbotDispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	// Channel for audit requests
	private val auditRequestChannel: Channel<AuditRequest> = Channel(capacity = Channel.UNLIMITED)

	private data class AuditRequest(val tokenAddress: AddressWrapper, val chain: Chain)

	@EventListener(ApplicationReadyEvent::class)
	fun onStartup() {
		scope.launch { auditRequestProcessor() }
	}

	@SentrySpan
	override fun invoke(tokenAddress: AddressWrapper, chain: Chain) {
		require(chain.type == ChainType.EVM) { "Only EVM chains supported" }

		runBlocking { auditRequestChannel.send(AuditRequest(tokenAddress, chain)) }
	}

	private suspend fun auditRequestProcessor() {
		for (request in auditRequestChannel) {
			runCatching {
				processAuditRequest(request)
			}.onFailure { e ->
				logger.error("Error processing audit request for token: ${request.tokenAddress}", e)
			}
		}
	}

	private suspend fun PerformTokenAuditOpenAI.processAuditRequest(request: AuditRequest) {
		logger.info("Processing audit request for token: ${request.tokenAddress} on chain: ${request.chain}")

		val smartContractSourceCode = getSmartContractSourceCode(request.tokenAddress, request.chain.evmId)

		val result = if (smartContractSourceCode.length <= MAX_CHUNK_SIZE) {
			openAIConnector.responses(
				inputs = listOf(
					SystemPrompt(AUDIT_SYSTEM_PROMPT),
					UserPrompt(smartContractSourceCode),
				),
				responseModel = PerformTokenAudit.Result::class.java,
			)
		} else {
			processLargeContract(smartContractSourceCode)
		}

		commandBus(
			SaveTokenAuditCommand(
				tokenAddress = request.tokenAddress.toChainAddress(request.chain),
				auditResult = result,
			),
		)
	}

	private suspend fun processLargeContract(sourceCode: String): PerformTokenAudit.Result {
		val chunks = splitSourceCode(sourceCode)

		val chunkResults = chunks.mapIndexed { index, chunk ->
			logger.info("Processing chunk ${index + 1}/${chunks.size}")

			val chunkPrompt = """
				PART ${index + 1} OF ${chunks.size}

				$chunk
			""".trimIndent()

			val result = runWithExponentialBackoffSuspend(
				maxRetries = 5,
				initialBackoffMs = 1000L,
				maxBackoffMs = 60000L,
				errorParser = ::extractOpenAIWaitTime,
			) {
				runCatching {
					openAIConnector.responses(
						inputs = listOf(
							SystemPrompt(AUDIT_SYSTEM_PROMPT_FOR_CHUNKS),
							UserPrompt(chunkPrompt),
						),
						responseModel = PerformTokenAudit.Result::class.java,
					)
				}
			}.getOrThrow()

			result
		}

		return if (chunkResults.size == 1) {
			chunkResults.first()
		} else {
			mergeResults(chunkResults)
		}
	}

	private fun splitSourceCode(sourceCode: String): List<String> {
		val splitSequence = generateSequence(sourceCode to listOf<String>()) { (remainingCode, chunks) ->
			if (remainingCode.isEmpty()) return@generateSequence null

			val chunkSize = min(MAX_CHUNK_SIZE, remainingCode.length)

			// Try to find a good splitting point (end of a contract or function)
			val splitPoint = findGoodSplitPoint(remainingCode, chunkSize) ?: chunkSize

			val newChunk = remainingCode.take(splitPoint)
			val newRemainingCode = remainingCode.drop(splitPoint)

			newRemainingCode to chunks + newChunk
		}

		val (_, chunks) = splitSequence.last()

		return chunks
	}

	private fun findGoodSplitPoint(code: String, maxSize: Int): Int? {
		val searchArea = code.take(min(maxSize, code.length))

		val contractEnd = searchArea.lastIndexOf("}\n")
		if (contractEnd != -1) {
			return contractEnd + 2
		}

		return null
	}

	private fun mergeResults(results: List<PerformTokenAudit.Result>): PerformTokenAudit.Result {
		val allIssues = results.flatMap { it.issues }.distinctBy { it.summary }

		val riskFactors = results.map { it.riskFactor }
		val overallRiskFactor = when {
			riskFactors.contains(PerformTokenAudit.RiskFactor.RED) -> PerformTokenAudit.RiskFactor.RED
			riskFactors.contains(PerformTokenAudit.RiskFactor.ORANGE) -> PerformTokenAudit.RiskFactor.ORANGE
			else -> PerformTokenAudit.RiskFactor.GREEN
		}

		val riskFactorReasons = results.map { it.riskFactorReason }.distinct()
		val overallReason = if (riskFactorReasons.size == 1) {
			riskFactorReasons.first()
		} else {
			when (overallRiskFactor) {
				PerformTokenAudit.RiskFactor.RED -> "Multiple high-risk issues detected across the contract code."
				PerformTokenAudit.RiskFactor.ORANGE -> "Several concerning patterns found in different parts of the contract."
				PerformTokenAudit.RiskFactor.GREEN -> "No significant risks found across all parts of the contract."
			}
		}

		return PerformTokenAudit.Result(
			issues = allIssues,
			riskFactor = overallRiskFactor,
			riskFactorReason = overallReason,
		)
	}

	private suspend fun getSmartContractSourceCode(
		tokenAddress: AddressWrapper,
		chainId: Long,
		proxyDepth: Int = 0,
	): String {
		val response = runWithRetrySuspend(
			retries = FETCH_SC_SOURCE_CODE_RETRY_LIMIT,
			retryDelay = ETHERSCAN_RATE_LIMIT_COOLDOWN,
			block = { runCatching { etherscanConnector.getSourceCode(tokenAddress = tokenAddress, chainId = chainId) } },
		).getOrNull()

		checkNotNull(response) {
			"Failed to fetch smart contract source code for token: $tokenAddress on " +
				"chain: $chainId with $FETCH_SC_SOURCE_CODE_RETRY_LIMIT attempts."
		}

		// Max proxy depth is 1, so we only go one level deep
		if (response.isProxy() && proxyDepth < MAX_PROXY_DEPTH) {
			return getSmartContractSourceCode(AddressWrapper(response.implementation), chainId, proxyDepth.inc())
		}

		return response.sourceCode
	}
}

private const val FETCH_SC_SOURCE_CODE_RETRY_LIMIT = 5
private const val MAX_CHUNK_SIZE = 50000 // Characters per chunk, adjust based on token limits

// Original prompt for single requests
private const val AUDIT_SYSTEM_PROMPT = """
### Identity
You are an expert blockchain developer and smart contract security auditor specialized in Ethereum Virtual Machine (EVM)-based Solidity contracts.

### Task
Analyze the provided Solidity smart contract source code and identify any **realistic, exploitable vulnerabilities or critical security risks**. Clearly distinguish between genuine threats and common or acceptable industry-standard patterns.

### Evaluation Guidelines
- **High Priority Risks (must report clearly)**:
  - Hidden or undisclosed minting or burning of tokens
  - Honeypot mechanisms preventing token sales or transfers
  - Secret admin backdoors or obscure control mechanisms
  - Rug-pull risks, such as easy liquidity removal or disabling user trades
  - Manipulable fee structures or taxes post-launch
  - Critical unchecked external calls or misuse of delegatecall
  - Unsecured proxy/implementation logic or potential mismatch exploits
  - Dangerous use of self-destruct or emergency kill-switches

- **Lower Priority Risks (report with caution)**:
  - Owner-controlled functions (like fee wallet updates, withdrawals, trade enabling/disabling, limits) are common; **ONLY flag these if implemented deceptively, secretly, unusually, or abusively**.
  - Lack of reentrancy protection only if it realistically threatens user funds.

- **Do NOT report**:
  - Standard admin or owner controls that are common practice, well-documented, and expected by token communities
  - Purely theoretical risks without practical exploitability
  - Gas inefficiencies, naming conventions, coding style, or minor optimizations (these are out of scope)

### Special Considerations
- For **well-known, established tokens** (stablecoins, major meme-coins like DOGE, SHIB, PEPE, etc.), significantly **reduce strictness** in evaluating standard owner/admin control patterns. Assume broad community trust unless clearly deceptive or malicious code is evident.
- If the token is **clearly based on well-audited OpenZeppelin ERC20 standards without significant customizations**, immediately classify it as a **GREEN** risk factor, barring any explicitly added malicious functionality.

### Output Requirements
Clearly structured JSON response:
- `issues`: List vulnerabilities ONLY if they are realistically exploitable or critically impactful.
  - Each issue must contain:
    - `summary`: Short, plain-language description of the issue (understandable by non-technical users)
    - `detail`: Brief technical description (1–2 concise sentences)
    - `severity`: Clearly assign one of the following severities:
      - **LOW**: Minor, non-exploitable or minimal-impact issue
      - **MEDIUM**: Issue potentially exploitable under specific realistic conditions
      - **HIGH**: Critical vulnerability, clear malicious intent, or severe risk of user fund loss or fraud
- `riskFactor`: Overall rating clearly indicating risk:
  - **Green**: No significant risks; safe for typical interaction.
  - **Orange**: Notable concerns present; caution advised.
  - **Red**: High risk or strong evidence of malicious intent; avoid interaction entirely.
- `riskFactorReason`: Single-sentence summary clearly explaining the chosen risk rating, emphasizing practical risks over theoretical ones.

### Example JSON Output Format
{
  "issues": [
    {
      "summary": "...",
      "detail": "...",
      "severity": "HIGH | MEDIUM | LOW"
    }
  ],
  "riskFactor": "Green | Orange | Red",
  "riskFactorReason": "Clear, brief reasoning focusing on real-world risk implications."
}
"""

private const val AUDIT_SYSTEM_PROMPT_FOR_CHUNKS = """
### Identity
You are an expert blockchain developer and smart contract security auditor specialized in Ethereum Virtual Machine (EVM)-based Solidity contracts.

### Task
Analyze the provided PART of a Solidity smart contract source code and identify any **realistic, exploitable vulnerabilities or critical security risks** in THIS PART ONLY. Clearly distinguish between genuine threats and common or acceptable industry-standard patterns.

### Important Note
You are analyzing only ONE PART of a larger contract. Focus only on the code provided in this chunk. The complete analysis will combine results from all parts.

### Evaluation Guidelines
- **High Priority Risks (must report clearly)**:
  - Hidden or undisclosed minting or burning of tokens
  - Honeypot mechanisms preventing token sales or transfers
  - Secret admin backdoors or obscure control mechanisms
  - Rug-pull risks, such as easy liquidity removal or disabling user trades
  - Manipulable fee structures or taxes post-launch
  - Critical unchecked external calls or misuse of delegatecall
  - Unsecured proxy/implementation logic or potential mismatch exploits
  - Dangerous use of self-destruct or emergency kill-switches

- **Lower Priority Risks (report with caution)**:
  - Owner-controlled functions (like fee wallet updates, withdrawals, trade enabling/disabling, limits) are common; **ONLY flag these if implemented deceptively, secretly, unusually, or abusively**.
  - Lack of reentrancy protection only if it realistically threatens user funds.

- **Do NOT report**:
  - Standard admin or owner controls that are common practice, well-documented, and expected by token communities
  - Purely theoretical risks without practical exploitability
  - Gas inefficiencies, naming conventions, coding style, or minor optimizations (these are out of scope)
  - Issues that might exist in other parts of the contract not shown in this chunk

### Special Considerations
- For **well-known, established tokens** (stablecoins, major meme-coins like DOGE, SHIB, PEPE, etc.), significantly **reduce strictness** in evaluating standard owner/admin control patterns. Assume broad community trust unless clearly deceptive or malicious code is evident.
- If the token is **clearly based on well-audited OpenZeppelin ERC20 standards without significant customizations**, immediately classify it as a **GREEN** risk factor, barring any explicitly added malicious functionality.

### Output Requirements
Clearly structured JSON response:
- `issues`: List vulnerabilities ONLY if they are realistically exploitable or critically impactful.
  - Each issue must contain:
    - `summary`: Short, plain-language description of the issue (understandable by non-technical users)
    - `detail`: Brief technical description (1–2 concise sentences)
    - `severity`: Clearly assign one of the following severities:
      - **LOW**: Minor, non-exploitable or minimal-impact issue
      - **MEDIUM**: Issue potentially exploitable under specific realistic conditions
      - **HIGH**: Critical vulnerability, clear malicious intent, or severe risk of user fund loss or fraud
- `riskFactor`: Overall rating clearly indicating risk for this part only:
  - **Green**: No significant risks; safe for typical interaction.
  - **Orange**: Notable concerns present; caution advised.
  - **Red**: High risk or strong evidence of malicious intent; avoid interaction entirely.
- `riskFactorReason`: Single-sentence summary clearly explaining the chosen risk rating, emphasizing practical risks over theoretical ones.

### Example JSON Output Format
{
  "issues": [
    {
      "summary": "...",
      "detail": "...",
      "severity": "HIGH | MEDIUM | LOW"
    }
  ],
  "riskFactor": "Green | Orange | Red",
  "riskFactorReason": "Clear, brief reasoning focusing on real-world risk implications."
}
"""

/**
 * Try to extract wait time from OpenAI rate limit error message
 * Example: "Please try again in 8.378s"
 */
private fun extractOpenAIWaitTime(error: Throwable): Duration? {
	val errorMessage = error.message ?: return null

	val regex = """Please try again in (\d+\.?\d*)s""".toRegex()
	val matchResult = regex.find(errorMessage) ?: return null

	return try {
		val seconds = matchResult.groupValues[1].toDouble()
		// Add 500ms buffer to be safe
		(seconds * 1000 + 500).milliseconds
	} catch (e: Exception) {
		null
	}
}

const val MAX_PROXY_DEPTH = 1
