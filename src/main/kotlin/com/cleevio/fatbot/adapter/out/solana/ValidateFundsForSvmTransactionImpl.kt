package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.compareTo
import com.cleevio.fatbot.application.common.crypto.minus
import com.cleevio.fatbot.application.common.crypto.plus
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.ValidateFundsForSvmTransaction
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.exception.InsufficientFundsForTransactionException
import com.cleevio.fatbot.application.module.wallet.exception.TransactionLikelyFailOnBlockchainException
import com.cleevio.fatbot.application.module.wallet.service.WalletBalanceService
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class ValidateFundsForSvmTransactionImpl(
	private val walletBalanceService: WalletBalanceService,
) : ValidateFundsForSvmTransaction {

	override fun invoke(walletAddress: AddressWrapper, transactionAmount: BaseAmount, estimatedFees: BaseAmount) {
		val balance = walletBalanceService.getBalance(walletAddress, Chain.SOLANA)

		return withBalance(walletAddress, balance, transactionAmount, estimatedFees)
	}

	override fun withBalance(
		walletAddress: AddressWrapper,
		balance: BaseAmount,
		transactionAmount: BaseAmount,
		estimatedFees: BaseAmount,
	) {
		val totalLamportsNeeded = transactionAmount + estimatedFees

		if (totalLamportsNeeded > balance) {
			val maxAmountPossibleToSend = (balance - estimatedFees).toNative(Chain.SOLANA).amount.toPlainString()
			val balanceNativeAmount = balance.toNative(Chain.SOLANA).amount

			throw InsufficientFundsForTransactionException(
				"Insufficient funds on wallet ${walletAddress.getAddressString()} to make transaction. " +
					"Maximum amount possible to send after applying fees is: $maxAmountPossibleToSend. " +
					"Your balance is $balanceNativeAmount SOL.",
			)
		}

		val balanceAfter = balance - totalLamportsNeeded
		if (balanceAfter.amount == BigInteger.ZERO) {
			// The balance after is zero so account is clear and thus no rent check is needed
			return
		}

		if (balanceAfter < SolanaConstants.MINIMUM_RENT_THRESHOLD.amount) {
			val balanceAfterNativeAmount = balanceAfter.toNative(Chain.SOLANA).amount.toPlainString()
			val minRentNativeAmount = SolanaConstants.MINIMUM_RENT_THRESHOLD.toNative(Chain.SOLANA).amount.toPlainString()
			val maxAmountBaseAmount = balance - SolanaConstants.MINIMUM_RENT_THRESHOLD - estimatedFees
			val maxAmountNativeAmount = maxAmountBaseAmount.toNative(Chain.SOLANA).amount.toPlainString()
			throw TransactionLikelyFailOnBlockchainException(
				"Transaction is likely to fail due to post transaction SOL amount would be $balanceAfterNativeAmount " +
					"but at least $minRentNativeAmount is needed for rent.",
			)
		}
	}
}
