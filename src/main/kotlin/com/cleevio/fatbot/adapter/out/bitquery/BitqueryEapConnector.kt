package com.cleevio.fatbot.adapter.out.bitquery

import com.apollographql.apollo3.api.Optional
import com.apollographql.apollo3.network.http.HttpInfo
import com.cleevio.fatbot.adapter.ApolloConnector
import com.cleevio.fatbot.adapter.out.bitquery.dto.BitqueryProxySolanaTradesResponse
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.token.constant.TimeInterval
import com.cleevio.fatbot.application.module.token.query.TokenOHLCTimeIntervalItem
import com.cleevio.fatbot.application.module.token.query.TokenPriceTimeIntervalItem
import com.cleevio.fatbot.graphql.bitquery.eap.type.OLAP_DateTimeIntervalUnits
import com.cleevio.fatbot.graphql.bitquery.eapoperation.SolanaTokenHistoricalDataQuery
import com.cleevio.fatbot.graphql.bitquery.eapoperation.SolanaTokenPriceQuery
import com.cleevio.fatbot.infrastructure.config.properties.BitqueryProperties
import com.cleevio.fatbot.infrastructure.config.properties.FatbotBitqueryProxyProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.runBlocking
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.bodyToFlow
import java.time.Instant
import java.time.ZoneOffset

/**
 * EAP (Early access program) GQL endpoint provides different chains than V2 endpoint.
 *
 * [Bitquery EAP](https://docs.bitquery.io/docs/graphql/dataset/EAP/)
 */
@Component
class BitqueryEapConnector(
	fatbotBitqueryProxyProperties: FatbotBitqueryProxyProperties,
	bitqueryProperties: BitqueryProperties,
) : ApolloConnector(
	graphQlEndpoint = bitqueryProperties.graphqlEapEndpoint,
	accessToken = bitqueryProperties.accessToken,
) {

	private val webClient = WebClient
		.builder()
		.baseUrl(fatbotBitqueryProxyProperties.baseUrl)
		.defaultHeader(HttpHeaders.AUTHORIZATION, fatbotBitqueryProxyProperties.apiKey.toString())
		.codecs { configurer -> configurer.defaultCodecs().maxInMemorySize(1 * 1024 * 1024) } // 1MB (default is 256KB)
		.build()

	@SentrySpan
	fun getSolanaTokenPrice(
		tokenAddress: AddressWrapper,
		dex: GetDex.Dex,
		timeInterval: TimeInterval,
		intervalCount: Int,
		limit: Int,
		after: Instant?,
		before: Instant?,
	): List<TokenPriceTimeIntervalItem> {
		require(tokenAddress.isSolana())
		val query = apolloClient.query(
			SolanaTokenPriceQuery(
				tokenAddress = tokenAddress.getAddressString(),
				// This is address of token against which we are trading our token of interest
				// Currently dexScreener filters only pools / pairs using SOL/WSOL as quote, so we can get away
				// with this being hardcoded for some time
				// Only leave SOL_QUOTE_TOKEN when our most recent dex is Pumpfun. If SOL_QUOTE_TOKEN is present after
				// a token promotes to Pumpswap, Bitquery will return duplicate data for the time when the token promoted
				quoteTokenAddresses = listOfNotNull(SOL_QUOTE_TOKEN.takeIf { dex == GetDex.Dex.PUMP_FUN }, WRAPPED_SOL_QUOTE_TOKEN),
				timeInterval = timeInterval.toBitqueryInterval(),
				intervalCount = intervalCount,
				limit = limit,
				after = Optional.presentIfNotNull(after),
				before = Optional.presentIfNotNull(before),
			),
		)

		val result = runBlocking { query.execute() }

		val queryId = (result.executionContext as? HttpInfo)?.headers
			?.find { it.name == "x-bitquery-gql-query-id" }?.value

		logger.info("Fetched SolanaTokenPriceQuery with queryId: $queryId")

		return result.data?.Solana?.DEXTradeByTokens?.map { it.toTimeIntervalItem() }
			?: error("Invalid Bitquery response for SolanaTokenPriceQuery.")
	}

	@SentrySpan
	fun getSolanaTokenHistoricalData(
		tokenAddress: AddressWrapper,
		isPumpfunExclusive: Boolean,
		timeInterval: TimeInterval,
		intervalCount: Int,
		limit: Int,
		after: Instant?,
		before: Instant?,
	): List<TokenOHLCTimeIntervalItem> {
		require(tokenAddress.isSolana())

		// Only leave SOL_QUOTE_TOKEN when our most recent dex is Pumpfun. If SOL_QUOTE_TOKEN is present after
		// a token promotes to Pumpswap, Bitquery will return duplicate data for the time when the token promoted
		val quoteToken = if (isPumpfunExclusive) SOL_QUOTE_TOKEN else WRAPPED_SOL_QUOTE_TOKEN

		// Bitquery suggested performance improvement (presumably they index the date field)
		val beforeDate = before?.atZone(ZoneOffset.UTC)?.toLocalDate()?.plusDays(1)?.toString()
		val afterDate = after?.atZone(ZoneOffset.UTC)?.toLocalDate()?.minusDays(1)?.toString()

		val query = apolloClient.query(
			SolanaTokenHistoricalDataQuery(
				tokenAddress = tokenAddress.getAddressString(),
				// This is address of token against which we are trading our token of interest
				// Currently dexScreener filters only pools / pairs using SOL/WSOL as quote, so we can get away
				// with this being hardcoded for some time
				quoteTokenAddresses = listOf(quoteToken),
				timeInterval = timeInterval.toBitqueryInterval(),
				intervalCount = intervalCount,
				limit = limit,
				after = Optional.presentIfNotNull(after),
				afterDate = Optional.presentIfNotNull(afterDate),
				before = Optional.presentIfNotNull(before),
				beforeDate = Optional.presentIfNotNull(beforeDate),
			),
		)

		val result = runBlocking { query.execute() }

		val queryId = (result.executionContext as? HttpInfo)?.headers
			?.find { it.name == "x-bitquery-gql-query-id" }?.value

		logger.info("Fetched SolanaTokenHistoricalDataQuery with queryId: $queryId")

		return result.data?.Solana?.DEXTradeByTokens?.map { it.toTimeIntervalItem() }
			?: error("Invalid Bitquery response for SolanaTokenHistoricalDataQuery.")
	}

	@SentrySpan
	fun subscribeToSolanaTokenTrades(): Flow<BitqueryProxySolanaTradesResponse> {
		return webClient
			.get()
			.uri("/internal/stream/bitquery/solana-trades")
			.retrieve()
			.bodyToFlow()
	}

	private fun TimeInterval.toBitqueryInterval(): OLAP_DateTimeIntervalUnits = when (this) {
		TimeInterval.SECOND -> OLAP_DateTimeIntervalUnits.seconds
		TimeInterval.MINUTE -> OLAP_DateTimeIntervalUnits.minutes
		TimeInterval.HOUR -> OLAP_DateTimeIntervalUnits.hours
		TimeInterval.DAY -> OLAP_DateTimeIntervalUnits.days
	}
}

fun SolanaTokenPriceQuery.DEXTradeByToken.toTimeIntervalItem() = TokenPriceTimeIntervalItem(
	timestamp = Block?.timestamp.orError(),
	close = Trade?.close?.toBigDecimal().orError(),
	closeNative = Trade?.closeNative?.toBigDecimal().orError(),
	volume = volume?.toBigDecimal().orError(),
)

fun SolanaTokenHistoricalDataQuery.DEXTradeByToken.toTimeIntervalItem() = TokenOHLCTimeIntervalItem(
	timestamp = Block?.Time.orError(),
	openUsd = Trade?.open?.toBigDecimal().orError(),
	openNative = Trade?.openNative?.toBigDecimal().orError(),
	highUsd = high?.toBigDecimal().orError(),
	lowUsd = low?.toBigDecimal().orError(),
	closeUsd = Trade?.close?.toBigDecimal().orError(),
	closeNative = Trade?.closeNative?.toBigDecimal().orError(),
	volumeUsd = volume?.toBigDecimal().orError(),
)

private fun <T : Any> T?.orError(message: String = "Error parsing token price data."): T = this ?: error(message)

private const val WRAPPED_SOL_QUOTE_TOKEN = "So11111111111111111111111111111111111111112"
private const val SOL_QUOTE_TOKEN = "11111111111111111111111111111111"
