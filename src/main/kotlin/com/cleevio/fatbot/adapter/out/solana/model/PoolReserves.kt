package com.cleevio.fatbot.adapter.out.solana.model

import com.cleevio.fatbot.application.common.crypto.BaseAmount

/**
 * Represents reserves of a pool / pair.
 * We enforce all pools to be a pair between a token and wrapped solana.
 * So instead of `token0, token1` or `baseReserve, quoteReserves`, we can afford to specify the `wsol` directly.
 */
data class PoolReserves(
	val wsol: BaseAmount,
	val token: BaseAmount,
)
