query SolanaTokenPrice(
    $tokenAddress: String!,
    $quoteTokenAddresses: [String!]!,
    $timeInterval: OLAP_DateTimeIntervalUnits!,
    $intervalCount: Int!
    $limit: Int!
    $after: DateTime,
    $before: DateTime,
) {
    Solana(dataset: combined) {
        DEXTradeByTokens(
            orderBy: { descendingByField: "Block_timestamp"}
            where: {
                Block: {
                    Time: { after: $after, before: $before}
                },
                Trade: {
                    Currency: {
                        MintAddress: {
                            is: $tokenAddress
                        }
                    }
                    Side: {
                        Amount: { gt: "0" }
                        Currency: {
                            MintAddress: {
                                in: $quoteTokenAddresses
                            }
                        }
                    },
                    PriceInUSD: { gt: 0 }
                },
                Transaction: { Result: { Success: true } }
            }
            limit: { count: $limit }
        ) {
            Block {
                timestamp: Time(interval: {count: $intervalCount, in: $timeInterval})
            }
            volume: sum(of: Trade_Side_AmountInUSD)
            high: quantile(of: Trade_PriceInUSD, level: 0.95)
            low: quantile(of: Trade_PriceInUSD, level: 0.05)
            Trade {
                close: PriceInUSD(maximum: Block_Slot)
                closeNative: Price(maximum: Block_Slot)
                open: PriceInUSD(minimum: Block_Slot)
            }
        }
    }
}
