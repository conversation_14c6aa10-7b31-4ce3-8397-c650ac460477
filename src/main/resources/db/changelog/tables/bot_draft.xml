<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">


    <changeSet id="0" author="sebastian.jakabcin">
        <createTable tableName="bot_draft">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="uuid">
                <constraints nullable="false" />
            </column>
            <column name="name" type="text"/>
            <column name="avatar_file_id" type="uuid"/>
            <column name="trade_amount" type="numeric"/>
            <column name="buy_frequency" type="bigint"/>
            <column name="profit_target_fraction" type="numeric"/>
            <column name="stop_loss_fraction" type="numeric"/>
            <column name="market_cap_from_usd" type="numeric"/>
            <column name="market_cap_to_usd" type="numeric"/>
            <column name="liquidity_from_usd" type="numeric"/>
            <column name="liquidity_to_usd" type="numeric"/>
            <column name="daily_volume_from_usd" type="numeric"/>
            <column name="daily_volume_to_usd" type="numeric"/>
            <column name="number_of_holders_from" type="bigint"/>
            <column name="number_of_holders_to" type="bigint"/>
            <column name="buy_count_from" type="bigint"/>
            <column name="buy_count_to" type="bigint"/>
            <column name="sell_count_from" type="bigint"/>
            <column name="sell_count_to" type="bigint"/>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <createIndex tableName="bot_draft" indexName="1e17e990ff434c41baed_ix">
            <column name="user_id" />
        </createIndex>
    </changeSet>
    <changeSet id="1" author="anna-navratilova">
        <dropColumn tableName="bot_draft" columnName="buy_count_from"/>
        <dropColumn tableName="bot_draft" columnName="buy_count_to"/>
        <dropColumn tableName="bot_draft" columnName="sell_count_from"/>
        <dropColumn tableName="bot_draft" columnName="sell_count_to"/>
        <addColumn tableName="bot_draft">
            <column name="buy_volume" type="numeric"/>
        </addColumn>
        <addColumn tableName="bot_draft">
            <column name="sell_volume" type="numeric"/>
        </addColumn>
    </changeSet>
    <changeSet id="3" author="anna.navratilova">
        <addColumn tableName="bot_draft">
            <column name="sell_transaction_fraction_threshold" type="numeric"/>
        </addColumn>
    </changeSet>

    <changeSet id="4" author="tomas.krason">
        <dropColumn tableName="bot_draft">
            <column name="sell_transaction_fraction_threshold"/>
        </dropColumn>

        <addColumn tableName="bot_draft">
            <column name="sell_transaction_fraction" type="numeric"/>
            <column name="buy_transaction_fraction" type="numeric"/>
        </addColumn>
    </changeSet>

    <changeSet id="5" author="tomas.krason">
        <addColumn tableName="bot_draft">
            <column name="token_ticker_copy_is_checked" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot_draft">
            <column name="creator_high_buy_is_checked" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot_draft">
            <column name="bundled_buys_detected_is_checked" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot_draft">
            <column name="suspicious_wallets_detected_is_checked" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot_draft">
            <column name="should_auto_sell_after_hold_time" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot_draft">
            <column name="single_high_buy_is_checked" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot_draft">
            <column name="buy_tokens_alive_at_least_for" type="interval"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>