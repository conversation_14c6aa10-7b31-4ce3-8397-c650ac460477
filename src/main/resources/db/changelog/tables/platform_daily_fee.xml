<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="norbert-bodnar">
        <createTable tableName="platform_daily_fee">
            <column name="id" type="UUID">
                <constraints primaryKey="true"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="date" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="wei_amount" type="numeric">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="platform_daily_fee" indexName="0b2d72b0d09d40d6b535_ui" unique="true">
            <column name="date"/>
            <column name="type"/>
        </createIndex>
    </changeSet>

    <changeSet id="1" author="tomas.krason">
        <dropIndex tableName="platform_daily_fee" indexName="0b2d72b0d09d40d6b535_ui"/>

        <addColumn tableName="platform_daily_fee">
            <column name="chain" type="text" defaultValue="EVM_MAINNET">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <renameColumn tableName="platform_daily_fee" oldColumnName="wei_amount" newColumnName="base_amount"/>

        <createIndex tableName="platform_daily_fee" indexName="0b2d72b0d09d40d6b535_ui" unique="true">
            <column name="date"/>
            <column name="type"/>
            <column name="chain"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>