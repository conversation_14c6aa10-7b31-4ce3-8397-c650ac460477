<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="jana-farova">
        <createTable tableName="fatty_league_season">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="season_number" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="start_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="closed_at" type="timestamptz"/>
            <column name="claim_cooldown_period_in_minutes" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="initial_release_percentage" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="donuts_to_fatty_tokens_ratio" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="remaining_unlock_period_in_months" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="remaining_unlock_period_parts" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>
