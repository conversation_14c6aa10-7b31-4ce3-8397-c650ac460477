<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="tomas.krason">
        <createTable tableName="token_pair_info">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>

            <column name="chain" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="dex_type" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="uniswap_v3_fee" type="text">

            </column>
            <column name="token_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="pair_address" type="bytea">
                <constraints nullable="false"/>
            </column>

            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="token_pair_info" indexName="e45d9e3a2e6c413c8177_ui" unique="true">
            <column name="pair_address"/>
        </createIndex>

    </changeSet>
    
    <changeSet id="1" author="sebastian.jakabcin">
        <addColumn tableName="token_pair_info">
            <column name="raydium_amm_market_data" type="jsonb" />
        </addColumn>

        <createIndex tableName="token_pair_info" indexName="3072e82750ab4498a20f_ix" >
            <column name="token_address"/>
        </createIndex>
    </changeSet>

    <changeSet id="2" author="sebastian.jakabcin">
        <addColumn tableName="token_pair_info">
            <column name="token_decimals" type="numeric" />
        </addColumn>

        <sql>
            UPDATE token_pair_info
            SET token_decimals = eti.decimals
            FROM evm_token_info eti
            WHERE token_pair_info.token_address = eti.address
        </sql>

        <addNotNullConstraint tableName="token_pair_info" columnName="token_decimals" />
    </changeSet>

    <changeSet id="3" author="sebastian.jakabcin">
        <addColumn tableName="token_pair_info">
            <column name="raydium_pool_type" type="text"/>
        </addColumn>

        <sql>
            UPDATE token_pair_info
            SET raydium_pool_type = 'CLMM'
            WHERE dex_type = 'RAYDIUM' and raydium_amm_market_data IS NULL
        </sql>
        <sql>
            UPDATE token_pair_info
            SET raydium_pool_type = 'AMM'
            WHERE dex_type = 'RAYDIUM' and raydium_amm_market_data IS NOT NULL
        </sql>

        <addColumn tableName="token_pair_info">
            <column name="raydium_cpmm_market_data" type="jsonb" />
        </addColumn>
    </changeSet>

    <changeSet id="4" author="sebastian.jakabcin">
        <addColumn tableName="token_pair_info">
            <column name="creator_address" type="bytea" />
        </addColumn>

        <sql>
--          Delete invalid pair_addresses (EVM - 32, SOLANA - 20)
            DELETE FROM token_pair_info where length(pair_address) not in (32, 20)
        </sql>
    </changeSet>
</databaseChangeLog>