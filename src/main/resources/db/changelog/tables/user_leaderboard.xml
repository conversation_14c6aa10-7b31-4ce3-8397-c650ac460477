<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="jana-farova">
        <createTable tableName="user_leaderboard">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="uuid">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="rank" type="int">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="donut_multiplier" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="donut_gained_snapshot" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz"/>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="user_leaderboard" indexName="78ab60d743174b9c8514_ix">
            <column name="user_id" />
        </createIndex>

        <createIndex tableName="user_leaderboard" indexName="3fdeee7c3723401fb6ae_ix">
            <column name="rank" />
        </createIndex>
    </changeSet>
    <changeSet id="1" author="anna.navratilova">
        <dropUniqueConstraint tableName="user_leaderboard" constraintName="user_leaderboard_rank_key"/>
        <dropUniqueConstraint tableName="user_leaderboard" constraintName="user_leaderboard_user_id_key"/>
        <dropIndex tableName="user_leaderboard" indexName="78ab60d743174b9c8514_ix"/>
        <dropIndex tableName="user_leaderboard" indexName="3fdeee7c3723401fb6ae_ix"/>

        <createIndex tableName="user_leaderboard" indexName="78ab60d743174b9c8514_ux" unique="true">
            <column name="user_id"/>
        </createIndex>
        <createIndex tableName="user_leaderboard" indexName="3fdeee7c3723401fb6ae_ux" unique="true">
            <column name="rank"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
