spring:
  threads:
    virtual:
      enabled: false # Currently scheduler might deadlock the application. Turn and try this on with new Java version, perhaps
  servlet:
    multipart:
      max-file-size: 1MB
      max-request-size: 1MB
  codec:
    max-in-memory-size: 1MB # set higher, as JSON payloads from bitquery proxy might be larger that default value
  liquibase:
    change-log: "classpath:db/changelog/db.changelog.xml"
    default-schema: public
  datasource:
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    url: ${DB_URL}?reWriteBatchedInserts=true
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 10
  jpa:
    hibernate:
      ddl-auto: validate
    open-in-view: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          time_zone: UTC
          batch_size: 20
        order_updates: true
        order_inserts: true
        batch_versioned_data: true
  application:
    name: fatbot-api
  profiles:
    active: ${SPRING_PROFILES_ACTIVE}
  task:
    scheduling:
      pool:
        size: 1
    execution:
      pool:
        core-size: 10
        max-size: 20
        queue-capacity: 100
      thread-name-prefix: AsyncThread-
management:
  server:
    port: 8282
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
  health:
    mail:
      enabled: false

springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /api-docs/swagger-ui.html
    operationsSorter: alpha
    tagsSorter: alpha

logging:
  level:
    root: INFO
  config: classpath:logback-spring.xml

sentry:
  dsn: ${SENTRY_DSN}
  exception-resolver-order: -**********
  send-default-pii: true
  max-request-body-size: always
  traces-sample-rate: 0.2
  attach-server-name: false

security:
  wallet-encryption-key: ${SECURITY_WALLET_ENCRYPTION_KEY}

cleevio:
  distributed-locks:
    storage-type: POSTGRES

fatbot:
  proxy:
    api-key: ${FATBOT_PROXY_API_KEY}
  anti-mev:
    force-use: true
  file-storage-url: placeholder
  storage:
    local-path: /upload
    resources-path: /resources
  evm: # Ethereum Virtual Machine
    - chain: EVM_MAINNET
      node: ${ETH_NODE_ADDRESS}
      tx-explorer-url: ${ETH_TX_EXPLORER_URL}
      weth-address: ${ETH_WETH_ADDRESS}
      fatbot-router: ${ETH_FATBOT_ROUTER_ADDRESS}
      fatbot-util: ${ETH_FATBOT_UTIL_ADDRESS}
      referral-wallet-private-key: ${ETH_REFERRAL_WALLET_PK}
      referral-claim-threshold-native-amount: 0.05
      priority-fee-buffer-basis-point: 1000
      buy-sell-slippage-basis-point: 3000
    - chain: EVM_BASE
      node: ${BASE_NODE_ADDRESS}
      tx-explorer-url: ${BASE_TX_EXPLORER_URL}
      weth-address: ${BASE_WETH_ADDRESS}
      fatbot-router: ${BASE_FATBOT_ROUTER_ADDRESS}
      fatbot-util: ${BASE_FATBOT_UTIL_ADDRESS}
      referral-wallet-private-key: ${BASE_REFERRAL_WALLET_PK}
      referral-claim-threshold-native-amount: 0.05
      priority-fee-buffer-basis-point: 1000
      buy-sell-slippage-basis-point: 400
    - chain: EVM_BSC
      node: ${BSC_NODE_ADDRESS}
      tx-explorer-url: ${BSC_TX_EXPLORER_URL}
      weth-address: ${BSC_WETH_ADDRESS}
      fatbot-router: ${BSC_FATBOT_ROUTER_ADDRESS}
      fatbot-util: ${BSC_FATBOT_UTIL_ADDRESS}
      referral-wallet-private-key: ${BSC_REFERRAL_WALLET_PK}
      referral-claim-threshold-native-amount: 0.05
      priority-fee-buffer-basis-point: 1000
      buy-sell-slippage-basis-point: 400
    - chain: EVM_ARBITRUM_ONE
      node: ${ARBITRUM_ONE_NODE_ADDRESS}
      tx-explorer-url: ${ARBITRUM_ONE_TX_EXPLORER_URL}
      weth-address: ${ARBITRUM_ONE_WETH_ADDRESS}
      fatbot-router: ${ARBITRUM_ONE_FATBOT_ROUTER_ADDRESS}
      fatbot-util: ${ARBITRUM_ONE_FATBOT_UTIL_ADDRESS}
      referral-wallet-private-key: ${ARBITRUM_ONE_REFERRAL_WALLET_PK}
      referral-claim-threshold-native-amount: 0.05
      priority-fee-buffer-basis-point: 1000
      buy-sell-slippage-basis-point: 400
  svm: # Solana Virtual Machine
    solana:
      node: ${SOLANA_NODE_ADDRESS}
      # TODO: For now these are the same. When implementing staked Node, switch this!
      staked-node: ${SOLANA_STAKED_NODE_ADDRESS}
      alchemy-repeater-node: ${SOLANA_ALCHEMY_REPEATER_NODE}
      warm-up-helius-staked-node-cache: false # default false so while developing, helius will no get spammed
      jito-node: https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/transactions?bundleOnly=true
      tx-explorer-url: ${SOLANA_TX_EXPLORER_URL}
      treasury-wallet: ${SOLANA_TREASURY_WALLET_ADDRESS}
      referral-wallet-private-key: ${SOLANA_REFERRAL_WALLET_PK}
      referral-claim-threshold-native-amount: 0.5

  fee:
    platform-fee-percentage: ${PLATFORM_FEE_PERCENTAGE}
    referrer-fee-reward-percentage: ${REFERRER_FEE_REWARD_PERCENTAGE}
    referee-manual-trading-discount-percentage: ${REFEREE_MANUAL_TRADING_DISCOUNT_PERCENTAGE}
    referee-automatic-trading-discount-percentage: ${REFEREE_AUTOMATIC_TRADING_DISCOUNT_PERCENTAGE}
  transaction:
    status-processing:
      cron: " 0/2 * * * * *"
      lock-at-least-for: "PT1.9S"
      lock-at-most-for: "PT5S"
    verification-limit: 50
  bot-transaction:
    sell-stale-tokens:
      cron: "0 * * * * *"
      lock-for: "PT30S" # run every minute, 30s safety around the cron is enough
    status-processing:
      cron: "1/5 * * * * *" # ! when changing this, always recalculate BOT_TRANSACTION_VERIFICATION_LIMIT !
      lock-for: "PT4S"
    verification-limit: 20
  bot-leaderboard:
    create-snapshot:
      cron: "0 0 0/1 * * *"
      lock-for: "PT180S" # run every hour, 180s safety around the cron is enough
  gas:
    snapshot-processing:
      cron: "0/15 * * * * *"
      lock-for: "PT10S" # run every 15s, 10s safety around the cron is enough
  token:
    price-processing:
      cron: "0 0/5 * * * *"
      lock-for: "PT60S" # run every five minutes, 60s safety around the cron is enough
    # offset cron by minute, so it runs after price-processing.cron
    hot-token-refresh:
      cron: "0 1/5 * * * *"
      lock-for: "PT60S" # run every five minutes, 60s safety around the cron is enough
  market-position:
    create-snapshot:
      cron: "0 0/15 * * * *"
      lock-for: "PT180S" # run every fifteen minutes, 180s safety around the cron is enough
  analytics:
    daily-fees:
      cron: "0 1 0 * * *"
      lock-for: "PT3600S"  # run every day, 1h safety around the cron is enough
  portfolio-value:
    create-snapshots:
      cron: "0 0 0 * * *"
      lock-for: "PT3600S" # run every day, 1h safety around the cron is enough
  exchange-rate:
    create-snapshot:
      cron: "0 0/5 * * * *"
      lock-for: "PT60S" # run every five minutes, 60s safety around the cron is enough
  bot:
    portfolio-value:
      create-snapshots:
        cron: "0 2/5 * * * *"
        lock-for: "PT60S" # run every five minutes, 60s safety around the cron is enough
    reset-remaining-buy-frequency:
      cron: "-"
      lock-for: "PT30S" # run every minute, 30s safety around the cron is enough
    active-days:
      cron: "0 2 0 * * *"
      lock-for: "PT3600S" # run every day, 1h safety around the cron is enough
  fatty-cards:
    trade-amount-needed-for-card: 1000
    max-cards-earned-in-day: 10
    statistics-reset:
      cron: "0 0 0 * * *"
      lock-for: "PT3600S" # run every day, 1h safety around the cron is enough
  whitelist-honeypot:
    contracts: ["0xbC4c97Fb9befaa8B41448e1dFcC5236dA543217F"]
  limit-order:
    trades-processing:
      cron: " 0/20 * * * * *"
      lock-for: "PT10S" # run every 20s, 10s safety around the cron is enough

firebase:
  credentials: ${FIREBASE_CREDENTIALS}
  api-key: ${FIREBASE_API_KEY}
  base-url: https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword

temper:
  base-url: ${TEMPER_API_URL}

integration:
  coinbase:
    base-url: https://api.coinbase.com
  titanbuilder:
    api-key: ${TITANBUILDER_API_KEY}
    base-url: https://eu.rpc.titanbuilder.xyz
  beaverbuild:
    base-url: https://rpc.beaverbuild.org/
  dexscreener:
    base-url: https://api.dexscreener.com
  bitquery:
    graphql-api-v2-endpoint: https://streaming.bitquery.io/graphql
    graphql-eap-endpoint: https://streaming.bitquery.io/eap
    access-token: ${BITQUERY_ACCESS_TOKEN}
  fatbot-api:
    api-key: ${FATBOT_API_API_KEY}
  fatbot-ts-proxy:
    base-url: ${FATBOT_TS_PROXY_API_URL}
  fatbot-token-aggregator:
    base-url: http://fatbot-token-aggregator-production-cluster-ip-service.fatbot-token-aggregator-production.svc.cluster.local:8080
  fatbot-messaging-proxy:
    base-url: http://fatbot-messaging-proxy-api-cluster-ip-service.fatbot-messaging-proxy-api-production.svc.cluster.local:8080
  ipfs:
    base-url: https://ipfs.io
  etherscan:
    base-url: https://api.etherscan.io
    api-key: ${ETHERSCAN_API_KEY}
  openai:
    api-key: ${OPENAI_API_KEY}
    base-url: https://api.openai.com
    model: ${OPENAI_MODEL}
  airdrop-platform:
    api-key: ${AIRDROP_PLATFORM_API_KEY}

leaderboard:
  reset:
    cron: "0 0 0 * * 7"
    lock-for: "PT3600S" # run every week, 1h safety around the cron is enough
  refresh:
    cron: "0 0/5 * * * *"
    lock-for: "PT60S" # run every five minutes, 60s safety around the cron is enough
  multiplier:
    rank:
      thresholds:
        "10": 2
        "50": 1.75
        "100": 1.5
        "250": 1.25
        "1000": 1.1

streak-system:
  multiplier:
    thresholds:
      "3": 1.2
      "7": 1.5
      "30": 2
      "90": 3
  reset:
    cron: "0 0 0 * * *"
    lock-for: "PT3600S" # run every day, 1h safety around the cron is enough

fatty-league:
  season:
    close:
      cron: "0 0 0 1 * *"
      lock-for: "PT3600S" # run every month, 1h safety around the cron is enough
    claim-cooldown-period-in-minutes: 10
    initial-release-percentage: 10
    remaining-unlock-period-in-months: 3
    remaining-unlock-period-parts: 3
    donuts-to-fatty-tokens-ratio: 0.5

processing:
  bot-transactions:
    enabled: true
  token-trade-publishing:
    enabled: true
  user-transactions:
    enabled: true
