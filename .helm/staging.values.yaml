#-----------------------
# Basic
#-----------------------
application:
  name: fatbot-api-staging
  port: 8080

  resources:
    limits:
      memory: 2Gi
      cpu: 2
    requests:
      memory: 2Gi
      cpu: 500m

  readinessProbe:
    httpGet:
      port: 8282
      path: /actuator/health
    initialDelaySeconds: 60
    timeoutSeconds: 10
    periodSeconds: 15
    failureThreshold: 5

  env:
    - name: SPRING_PROFILES_ACTIVE
      value: staging
    - name: DB_URL
      valueFrom:
        secretKeyRef:
          key: spring.datasource.url
          name: env-variables
    - name: DB_USERNAME
      valueFrom:
        secretKeyRef:
          key: spring.datasource.username
          name: env-variables
    - name: DB_PASSWORD
      valueFrom:
        secretKeyRef:
          key: spring.datasource.password
          name: env-variables
    - name: SENTRY_DSN
      valueFrom:
        secretKeyRef:
          key: sentry.dsn
          name: env-variables
    - name: FIREBASE_CREDENTIALS
      valueFrom:
        secretKeyRef:
          key: firebase.credentials
          name: env-variables
    - name: FIREBASE_API_KEY
      valueFrom:
        secretKeyRef:
          key: firebase.api-key
          name: env-variables
    - name: TITANBUILDER_API_KEY
      valueFrom:
        secretKeyRef:
          key: titanbuilder.api-key
          name: env-variables
    - name: BITQUERY_ACCESS_TOKEN
      valueFrom:
        secretKeyRef:
          key: bitquery.access-token
          name: env-variables
    - name: ETH_REFERRAL_WALLET_PK
      valueFrom:
        secretKeyRef:
          key: referralwallet.eth.private-key
          name: env-variables
    - name: BASE_REFERRAL_WALLET_PK
      valueFrom:
        secretKeyRef:
          key: referralwallet.base.private-key
          name: env-variables
    - name: SOLANA_REFERRAL_WALLET_PK
      valueFrom:
        secretKeyRef:
          key: referralwallet.solana.private-key
          name: env-variables
    - name: SECURITY_WALLET_ENCRYPTION_KEY
      valueFrom:
        secretKeyRef:
          key: security.wallet-encryption-key
          name: env-variables
    - name: TEMPER_API_URL
      valueFrom:
        secretKeyRef:
          key: temper-simulator.fatbot-temper
          name: env-variables
    - name: ETH_NODE_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.eth.node
          name: env-variables

    - name: ETH_TX_EXPLORER_URL
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.eth.tx-explorer-url
          name: env-variables
    - name: ETH_WETH_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.eth.weth-address
          name: env-variables
    - name: ETH_FATBOT_ROUTER_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.eth.fatbot-router
          name: env-variables
    - name: ETH_FATBOT_UTIL_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.eth.fatbot-util
          name: env-variables

    - name: BASE_NODE_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.base.node
          name: env-variables

    - name: BASE_TX_EXPLORER_URL
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.base.tx-explorer-url
          name: env-variables
    - name: BASE_WETH_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.base.weth-address
          name: env-variables
    - name: BASE_FATBOT_ROUTER_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.base.fatbot-router
          name: env-variables
    - name: BASE_FATBOT_UTIL_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.base.fatbot-util
          name: env-variables
    - name: BSC_NODE_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.bsc.node
          name: env-variables
    - name: BSC_TX_EXPLORER_URL
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.bsc.tx-explorer-url
          name: env-variables
    - name: BSC_WETH_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.bsc.weth-address
          name: env-variables
    - name: BSC_FATBOT_ROUTER_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.bsc.fatbot-router
          name: env-variables
    - name: BSC_FATBOT_UTIL_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.bsc.fatbot-util
          name: env-variables
    - name: BSC_REFERRAL_WALLET_PK
      valueFrom:
        secretKeyRef:
          key: referralwallet.bsc.private-key
          name: env-variables
    - name: ARBITRUM_ONE_NODE_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.arbitrum.node
          name: env-variables
    - name: ARBITRUM_ONE_TX_EXPLORER_URL
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.arbitrum.tx-explorer-url
          name: env-variables
    - name: ARBITRUM_ONE_WETH_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.arbitrum.weth-address
          name: env-variables
    - name: ARBITRUM_ONE_FATBOT_ROUTER_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.arbitrum.fatbot-router
          name: env-variables
    - name: ARBITRUM_ONE_FATBOT_UTIL_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.evm.arbitrum.fatbot-util
          name: env-variables
    - name: ARBITRUM_ONE_REFERRAL_WALLET_PK
      valueFrom:
        secretKeyRef:
          key: referralwallet.arbitrum.private-key
          name: env-variables

    - name: SOLANA_NODE_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.svm.solana.node
          name: env-variables
    - name: SOLANA_STAKED_NODE_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.svm.solana.staked-node
          name: env-variables
    - name: SOLANA_ALCHEMY_REPEATER_NODE
      valueFrom:
        secretKeyRef:
          key: fatbot.svm.solana.alchemy-repeater-node
          name: env-variables
    - name: SOLANA_TREASURY_WALLET_ADDRESS
      valueFrom:
        secretKeyRef:
          key: fatbot.svm.solana.treasury-wallet
          name: env-variables
    - name: SOLANA_TX_EXPLORER_URL
      valueFrom:
        secretKeyRef:
          key: fatbot.svm.solana.tx-explorer-url
          name: env-variables

    - name: PLATFORM_FEE_PERCENTAGE
      valueFrom:
        secretKeyRef:
          key: fatbot.platform-fee-percentage
          name: env-variables
    - name: REFERRER_FEE_REWARD_PERCENTAGE
      valueFrom:
        secretKeyRef:
          key: fatbot.referrer-fee-reward-percentage
          name: env-variables
    - name: REFEREE_MANUAL_TRADING_DISCOUNT_PERCENTAGE
      valueFrom:
        secretKeyRef:
          key: fatbot.referee-manual-trading-discount-percentage
          name: env-variables
    - name: REFEREE_AUTOMATIC_TRADING_DISCOUNT_PERCENTAGE
      valueFrom:
        secretKeyRef:
          key: fatbot.referee-automatic-trading-discount-percentage
          name: env-variables
    - name: FATBOT_API_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.fatbot-api.api-key
          name: env-variables
    - name: FATBOT_TS_PROXY_API_URL
      valueFrom:
        secretKeyRef:
          key: integration.fatbot-ts-proxy.base-url
          name: env-variables
    - name: FATBOT_PROXY_API_KEY
      valueFrom:
        secretKeyRef:
          key: fatbot.proxy.api-key
          name: env-variables
    - name: ETHERSCAN_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.etherscan.api-key
          name: env-variables
    - name: OPENAI_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.openai.api-key
          name: env-variables
    - name: OPENAI_MODEL
      valueFrom:
        secretKeyRef:
          key: integration.openai.model
          name: env-variables
    - name: AIRDROP_PLATFORM_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.airdrop-platform.api-key
          name: env-variables

  image:
    repository: gitlab.cleevio.cz:4567/cleeviox/backend/fatbot-api/staging
    tag: latest

  replicaCount: 2

#-----------------------
# Ingress
#-----------------------
ingress:
  certificateIssuer: letsencrypt-http

  annotation:
    nginx.ingress.kubernetes.io/affinity: "cookie"
    nginx.ingress.kubernetes.io/session-cookie-name: "http-cookie"
    nginx.ingress.kubernetes.io/session-cookie-expires: "172800"
    nginx.ingress.kubernetes.io/session-cookie-max-age: "172800"

  hosts:
    - host: api.fatbot.staging.cleevio.dev
      paths: [ "/" ]

  tls:
    - hosts:
        - api.fatbot.staging.cleevio.dev

#-----------------------
# Service
#-----------------------
service:
  port: 8080

#-----------------------
# Persistent Volume
#-----------------------
persistentVolume:
  enabled: true                     # Enables / Disables mounting
  mountName: static-files           # (Optional) Mount name
  size: "5Gi"                       # Size of mount
  mountPath: "/upload"              # Mount path in the container
  storageClassName: "nfs"           # Storage class name - different on each provider
  cdn:
    enabled: true
    host: cdn.fatbot.staging.cleevio.dev
